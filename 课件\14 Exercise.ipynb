{"cells": [{"cell_type": "markdown", "id": "7851ae00", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["# 随堂练习（1）"]}, {"cell_type": "code", "execution_count": 3, "id": "e380e1ad", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Dumping model to file cache /var/folders/05/v0gmlpbd5ys54y88q0r8mr9m0000gn/T/jieba.cache\n", "Loading model cost 0.664 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"data": {"text/plain": ["'/Users/<USER>/Nutstore Files/program/PycharmProjects/pcourses/2024-2025/wordcloud.html'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import jieba\n", "from collections import Counter\n", "from pyecharts.charts import WordCloud\n", "from pyecharts import options as opts\n", "\n", "\n", "# 统计词频并返回前20个词\n", "def get_top_words(words, top_k=20):\n", "    # counter返回一个类似字典的结构：每个单词出现了多少次\n", "    counter = Counter(words)\n", "    # most_common方法：返回出现频率最高的 n 个元素\n", "    return counter.most_common(top_k)\n", "\n", "# 读取当前目录下的文件\n", "with open(\"学院简介\", 'r', encoding='utf-8') as f:\n", "    text = f.read()\n", "\n", "# 使用jieba进行分词\n", "words = jieba.lcut(text)\n", "# 去除单个字和标点符号\n", "words = [w for w in words if len(w) > 1 and w.strip()]\n", "\n", "# 获取前20个词和词频\n", "top_words = get_top_words(words, top_k=20)\n", "wordcloud = (\n", "    WordCloud()\n", "    .add(\"\", top_words, word_size_range=[20, 100], shape='circle')\n", "    .set_global_opts(title_opts=opts.TitleOpts(title=\"词云图 - Top20 高频词\"))\n", ")\n", "wordcloud.render(\"wordcloud.html\")  # 生成 HTML 文件"]}, {"cell_type": "markdown", "id": "9482ecf8-0253-4410-a797-aa090aaafa7f", "metadata": {}, "source": ["# 随堂练习（2）"]}, {"cell_type": "code", "execution_count": 2, "id": "36aff8a9-9b66-408a-934b-aadea6bc1acd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Product  Sales\n", "0       A    330\n", "1       B    340\n", "2       C    225\n", "3       D    295\n"]}, {"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8050/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x1305f605b10>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import dash\n", "from dash import html, dcc, Input, Output\n", "import pandas as pd\n", "import plotly.express as px\n", "\n", "# 创建数据\n", "data = {\n", "    'City': ['Beijing'] * 4 + ['Shanghai'] * 4 + ['Shenzhen'] * 4,\n", "    'Product': ['A', 'B', 'C', 'D'] * 3,\n", "    'Sales': [100, 150, 80, 70, 120, 90, 60, 130, 110, 100, 85, 95]\n", "}\n", "df = pd.DataFrame(data)\n", "\n", "# 初始化 Dash 应用\n", "app = dash.Dash(__name__)\n", "\n", "# 按产品汇总总销售额\n", "df_sum = df.groupby('Product', as_index=False)['Sales'].sum()\n", "print(df_sum)\n", "pie_fig = px.pie(df_sum, names='Product', values='Sales', title=\"产品销量占比饼图\")\n", "\n", "# 页面布局\n", "app.layout = html.Div([\n", "    # 顶部标题，设置格式\n", "    html.H1(\"产品销售数据大屏\", style={'textAlign': 'center', 'marginBottom': '30px'}),\n", "\n", "    \n", "\n", "    # 统计卡片\n", "    html.Div(id='stats-cards', style={\n", "        'display': 'flex',\n", "        'justifyContent': 'space-around',\n", "        'marginBottom': '40px'\n", "    }),\n", "\n", "    # 控件区域\n", "    html.Div([\n", "        html.Label(\"请选择城市：\"),\n", "        dcc.Dropdown(\n", "            id='city-dropdown',\n", "            options=[{'label': c, 'value': c} for c in df['City'].unique()],\n", "            value='Beijing',\n", "            style={'width': '300px'}\n", "        )\n", "    ], style={'marginBottom': '30px'}),\n", "    \n", "    # 图表区域\n", "    html.Div([\n", "        \n", "        html.Div([\n", "            dcc.Graph(id='bar-chart')\n", "        ], style={'width': '48%', 'display': 'inline-block'}),\n", "\n", "        html.Div([\n", "            dcc.Graph(figure=pie_fig)\n", "        ], style={'width': '48%', 'display': 'inline-block'})\n", "    ])\n", "], style={'padding': '20px'})\n", "\n", "# 回调：更新卡片 & 图表\n", "@app.callback(\n", "    [Output('stats-cards', 'children'), # id是 'stats-cards' 组件的 children 属性（就是这个区域里面显示的内容，通常是子组件）\n", "     Output('bar-chart', 'figure'),],\n", "    [Input('city-dropdown', 'value')]\n", ")\n", "\n", "def update_dashboard(city):\n", "    filtered = df[df['City'] == city]\n", "\n", "    total_sales = filtered['Sales'].sum()\n", "    product_count = filtered['Product'].nunique()\n", "    avg_sales = round(filtered['Sales'].mean(), 2)\n", "\n", "    # 卡片组件——注意这里存储的是CSS样式\n", "    # padding：内边距，卡片内容离边框距离，20像素。\n", "    # borderRadius：圆角，10像素圆滑边角。\n", "    # backgroundColor：背景色，浅灰色 #f0f0f0。\n", "    # width：卡片宽度，这里设置为父容器的 30%。\n", "    # textAlign：文字居中。\n", "    # boxShadow：阴影效果，使卡片有立体感。\n", "    card_style = {\n", "        'padding': '20px',\n", "        'borderRadius': '10px',\n", "        'backgroundColor': '#f0f0f0',\n", "        'width': '30%',\n", "        'textAlign': 'center',\n", "        'boxShadow': '2px 2px 8px rgba(0,0,0,0.1)'\n", "    }\n", "\n", "    # 这是一个列表，包含了 3 个 html.Div，代表 3 个统计卡片。\n", "    # 每个卡片结构一样：\n", "    # - html.Div 作为卡片容器\n", "    # - 里面有标题（html.H4）和具体数值（html.H2）\n", "    # - 通过 style=card_style，这 3 个卡片都共享刚才定义的样式。\n", "    # # 由于外层容器用的是 flex 布局且 justifyContent: space-around，3 个卡片会横排且间距均匀。\n", "    cards = [\n", "        html.Div([\n", "            html.H4(\"总销量\"),\n", "            html.H2(f\"{total_sales}\")\n", "        ], style=card_style),\n", "\n", "        html.Div([\n", "            html.H4(\"产品种类\"),\n", "            html.H2(f\"{product_count}\")\n", "        ], style=card_style),\n", "\n", "        html.Div([\n", "            html.H4(\"平均销量\"),\n", "            html.H2(f\"{avg_sales}\")\n", "        ], style=card_style)\n", "    ]\n", "\n", "    # 图表\n", "    bar_fig = px.bar(filtered, x='Product', y='Sales', title=f\"{city} 产品销量柱状图\")\n", "\n", "    return cards, bar_fig\n", "\n", "# 启动应用\n", "if __name__ == '__main__':\n", "    app.run_server(debug=True)\n"]}, {"cell_type": "code", "execution_count": null, "id": "8b7aad83", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.7"}}, "nbformat": 4, "nbformat_minor": 5}