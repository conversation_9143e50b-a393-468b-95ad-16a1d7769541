"""
全面的数据预处理程序
处理收集到的B站数据，进行清洗、特征工程等
"""

import pandas as pd
import numpy as np
from datetime import datetime
import re
import json


class ComprehensiveDataProcessor:
    """
    全面的数据预处理类
    """
    
    def __init__(self):
        """初始化"""
        self.processed_data = None
        self.data_summary = {}
    
    def load_and_clean_data(self, file_path):
        """
        加载和清洗数据
        """
        print("=== 开始数据加载和清洗 ===")
        
        # 加载数据
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            print(f"成功加载数据: {len(df)} 行, {len(df.columns)} 列")
        except Exception as e:
            print(f"数据加载失败: {e}")
            return None
        
        # 数据清洗
        print("\n1. 数据清洗...")
        
        # 去除重复数据
        initial_count = len(df)
        df = df.drop_duplicates(subset=['bvid'], keep='first')
        print(f"去除重复数据: {initial_count - len(df)} 条")
        
        # 处理缺失值
        print("\n2. 处理缺失值...")
        missing_before = df.isnull().sum().sum()
        
        # 字符串字段填充空值
        string_cols = ['title', 'desc', 'owner_name', 'tname', 'pic']
        for col in string_cols:
            if col in df.columns:
                df[col] = df[col].fillna('')
        
        # 数值字段填充0
        numeric_cols = ['aid', 'duration', 'pubdate', 'view', 'danmaku', 'reply', 
                       'favorite', 'coin', 'share', 'like', 'owner_mid', 'tid']
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
        
        missing_after = df.isnull().sum().sum()
        print(f"处理缺失值: {missing_before} -> {missing_after}")
        
        # 数据类型转换
        print("\n3. 数据类型转换...")
        
        # 确保数值字段为正数
        for col in numeric_cols:
            if col in df.columns:
                df[col] = df[col].clip(lower=0)
        
        # 处理时间字段
        if 'pubdate' in df.columns:
            df['publish_time'] = pd.to_datetime(df['pubdate'], unit='s', errors='coerce')
            df['publish_date'] = df['publish_time'].dt.date
            df['publish_hour'] = df['publish_time'].dt.hour
            df['publish_weekday'] = df['publish_time'].dt.day_name()
            df['publish_month'] = df['publish_time'].dt.month
            df['publish_year'] = df['publish_time'].dt.year
        
        # 清洗标题和描述
        if 'title' in df.columns:
            df['title'] = df['title'].apply(self._clean_text)
            df['title_length'] = df['title'].str.len()
        
        if 'desc' in df.columns:
            df['desc'] = df['desc'].apply(self._clean_text)
            df['desc_length'] = df['desc'].str.len()
        
        print(f"数据清洗完成: {len(df)} 条记录")
        return df
    
    def feature_engineering(self, df):
        """
        特征工程
        """
        print("\n=== 开始特征工程 ===")
        
        featured_df = df.copy()
        
        # 1. 互动率计算
        print("1. 计算互动率指标...")
        
        # 避免除零错误
        view_safe = featured_df['view'] + 1
        
        featured_df['like_rate'] = featured_df['like'] / view_safe
        featured_df['coin_rate'] = featured_df['coin'] / view_safe
        featured_df['favorite_rate'] = featured_df['favorite'] / view_safe
        featured_df['share_rate'] = featured_df['share'] / view_safe
        featured_df['reply_rate'] = featured_df['reply'] / view_safe
        featured_df['danmaku_rate'] = featured_df['danmaku'] / view_safe
        
        # 总互动数和互动率
        featured_df['total_interaction'] = (featured_df['like'] + featured_df['coin'] + 
                                          featured_df['favorite'] + featured_df['share'] + 
                                          featured_df['reply'])
        featured_df['interaction_rate'] = featured_df['total_interaction'] / view_safe
        
        # 2. 视频分类
        print("2. 创建分类特征...")
        
        # 时长分类
        featured_df['duration_category'] = pd.cut(
            featured_df['duration'],
            bins=[0, 60, 300, 600, 1800, float('inf')],
            labels=['短视频(<1分钟)', '短视频(1-5分钟)', '中等(5-10分钟)', 
                   '长视频(10-30分钟)', '超长视频(>30分钟)']
        )
        
        # 播放量分级
        featured_df['view_level'] = pd.cut(
            featured_df['view'],
            bins=[0, 1000, 10000, 100000, 1000000, float('inf')],
            labels=['低播放量', '一般播放量', '热门', '爆款', '现象级']
        )
        
        # 互动质量分级（基于互动率）
        featured_df['interaction_quality'] = pd.cut(
            featured_df['interaction_rate'],
            bins=[0, 0.05, 0.1, 0.2, 0.3, float('inf')],
            labels=['低互动', '一般互动', '高互动', '超高互动', '现象级互动']
        )
        
        # 3. 时间特征
        print("3. 创建时间特征...")
        
        if 'publish_time' in featured_df.columns:
            # 发布时段分类
            featured_df['time_period'] = featured_df['publish_hour'].apply(self._categorize_time_period)
            
            # 是否周末
            featured_df['is_weekend'] = featured_df['publish_weekday'].isin(['Saturday', 'Sunday'])
            
            # 发布季节
            featured_df['season'] = featured_df['publish_month'].apply(self._get_season)
        
        # 4. 内容特征
        print("4. 创建内容特征...")
        
        # UP主活跃度（基于该UP主的视频数量）
        if 'owner_mid' in featured_df.columns:
            owner_counts = featured_df['owner_mid'].value_counts()
            featured_df['owner_video_count'] = featured_df['owner_mid'].map(owner_counts)
            
            # UP主类型分类
            featured_df['owner_type'] = pd.cut(
                featured_df['owner_video_count'],
                bins=[0, 1, 3, 10, float('inf')],
                labels=['新UP主', '偶尔发布', '活跃UP主', '高产UP主']
            )
        
        # 5. 分区特征
        print("5. 创建分区特征...")
        
        if 'tname' in featured_df.columns:
            # 分区热度（基于该分区的视频数量）
            category_counts = featured_df['tname'].value_counts()
            featured_df['category_popularity'] = featured_df['tname'].map(category_counts)
            
            # 分区类型分类
            featured_df['category_type'] = featured_df['tname'].apply(self._categorize_content_type)
        
        # 6. 质量指标
        print("6. 计算质量指标...")
        
        # 完播率估算（基于时长和互动的关系）
        featured_df['estimated_completion_rate'] = np.where(
            featured_df['duration'] > 0,
            np.minimum(1.0, featured_df['danmaku_rate'] * 10),  # 弹幕密度反映完播情况
            0
        )
        
        # 内容质量评分（综合多个指标）
        featured_df['content_quality_score'] = (
            featured_df['like_rate'] * 0.3 +
            featured_df['coin_rate'] * 0.25 +
            featured_df['favorite_rate'] * 0.25 +
            featured_df['reply_rate'] * 0.2
        )
        
        print(f"特征工程完成，新增 {len(featured_df.columns) - len(df.columns)} 个特征")
        return featured_df
    
    def _clean_text(self, text):
        """清洗文本"""
        if pd.isna(text):
            return ""
        
        text = str(text)
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text).strip()
        return text
    
    def _categorize_time_period(self, hour):
        """时段分类"""
        if 6 <= hour < 12:
            return '上午'
        elif 12 <= hour < 18:
            return '下午'
        elif 18 <= hour < 24:
            return '晚上'
        else:
            return '深夜'
    
    def _get_season(self, month):
        """获取季节"""
        if month in [12, 1, 2]:
            return '冬季'
        elif month in [3, 4, 5]:
            return '春季'
        elif month in [6, 7, 8]:
            return '夏季'
        else:
            return '秋季'
    
    def _categorize_content_type(self, category):
        """内容类型分类"""
        entertainment = ['娱乐', '搞笑', '鬼畜', '舞蹈', '音乐']
        knowledge = ['科技', '科普', '教程', '学习']
        lifestyle = ['生活', '美食', '时尚', '日常', '旅行']
        media = ['影视', '电影', '电视剧', '纪录片', '动画', '番剧', '国创']
        gaming = ['游戏', '单机游戏', '手机游戏', '电子竞技']
        
        if category in entertainment:
            return '娱乐类'
        elif category in knowledge:
            return '知识类'
        elif category in lifestyle:
            return '生活类'
        elif category in media:
            return '影视类'
        elif category in gaming:
            return '游戏类'
        else:
            return '其他类'
    
    def generate_data_summary(self, df):
        """生成数据摘要"""
        print("\n=== 生成数据摘要 ===")
        
        summary = {
            'basic_info': {
                'total_records': len(df),
                'total_features': len(df.columns),
                'data_collection_date': datetime.now().strftime('%Y-%m-%d'),
                'data_source': 'Bilibili API + Enhanced Simulation'
            },
            'numeric_statistics': {},
            'categorical_distributions': {},
            'time_analysis': {},
            'content_analysis': {}
        }
        
        # 数值字段统计
        numeric_cols = ['view', 'like', 'coin', 'favorite', 'share', 'reply', 'duration']
        for col in numeric_cols:
            if col in df.columns:
                summary['numeric_statistics'][col] = {
                    'mean': float(df[col].mean()),
                    'median': float(df[col].median()),
                    'std': float(df[col].std()),
                    'min': float(df[col].min()),
                    'max': float(df[col].max()),
                    'q25': float(df[col].quantile(0.25)),
                    'q75': float(df[col].quantile(0.75))
                }
        
        # 分类字段分布
        categorical_cols = ['tname', 'duration_category', 'view_level', 'category_type', 'time_period']
        for col in categorical_cols:
            if col in df.columns:
                summary['categorical_distributions'][col] = df[col].value_counts().to_dict()
        
        # 时间分析
        if 'publish_time' in df.columns:
            summary['time_analysis'] = {
                'date_range': {
                    'start': df['publish_time'].min().strftime('%Y-%m-%d') if pd.notna(df['publish_time'].min()) else None,
                    'end': df['publish_time'].max().strftime('%Y-%m-%d') if pd.notna(df['publish_time'].max()) else None
                },
                'hourly_distribution': df['publish_hour'].value_counts().to_dict(),
                'weekday_distribution': df['publish_weekday'].value_counts().to_dict(),
                'monthly_distribution': df['publish_month'].value_counts().to_dict()
            }
        
        # 内容分析
        if 'title_length' in df.columns:
            summary['content_analysis']['title_length'] = {
                'mean': float(df['title_length'].mean()),
                'median': float(df['title_length'].median())
            }
        
        if 'interaction_rate' in df.columns:
            summary['content_analysis']['interaction_rate'] = {
                'mean': float(df['interaction_rate'].mean()),
                'median': float(df['interaction_rate'].median())
            }
        
        self.data_summary = summary
        return summary
    
    def process_comprehensive_data(self, input_file, output_file):
        """
        完整的数据处理流程
        """
        print("=== 开始全面数据处理 ===")
        
        # 1. 加载和清洗数据
        df = self.load_and_clean_data(input_file)
        if df is None:
            return None
        
        # 2. 特征工程
        df = self.feature_engineering(df)
        
        # 3. 生成数据摘要
        summary = self.generate_data_summary(df)
        
        # 4. 保存处理后的数据
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"\n处理后的数据已保存到: {output_file}")
        
        # 5. 保存数据摘要
        summary_file = output_file.replace('.csv', '_summary.json')
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2, default=str)
        print(f"数据摘要已保存到: {summary_file}")
        
        self.processed_data = df
        
        print(f"\n=== 数据处理完成 ===")
        print(f"最终数据集: {len(df)} 条记录, {len(df.columns)} 个特征")
        
        return df


def main():
    """主函数"""
    processor = ComprehensiveDataProcessor()
    
    # 处理数据
    result = processor.process_comprehensive_data(
        'bilibili_comprehensive_data.csv',
        'bilibili_final_processed_data.csv'
    )
    
    if result is not None:
        print("\n=== 处理结果概览 ===")
        
        # 基本信息
        print(f"数据集大小: {len(result)} 行 × {len(result.columns)} 列")
        
        # 分区分布
        if 'tname' in result.columns:
            print(f"\n分区分布 (前10名):")
            for category, count in result['tname'].value_counts().head(10).items():
                print(f"  {category}: {count} 条")
        
        # 播放量统计
        if 'view' in result.columns:
            print(f"\n播放量统计:")
            print(f"  平均播放量: {result['view'].mean():.0f}")
            print(f"  中位数播放量: {result['view'].median():.0f}")
            print(f"  最高播放量: {result['view'].max():.0f}")
        
        # 互动率统计
        rate_cols = ['like_rate', 'coin_rate', 'favorite_rate', 'interaction_rate']
        print(f"\n互动率统计:")
        for col in rate_cols:
            if col in result.columns:
                print(f"  {col}: {result[col].mean():.4f}")
        
        print("\n数据预处理完成！可以开始数据可视化分析。")
        
    else:
        print("数据处理失败！")


if __name__ == "__main__":
    main()
