#!/usr/bin/env python
# coding: utf-8

# # Lecture 10: 层次关系&局部整体关系数据可视化

# In[25]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Sunburst, Tree, Pie
import random
import plotly.express as px
import plotly.graph_objects as go


# ## 层次关系图

# ### 旭日图

# In[2]:


# 使用pyecharts绘制

# 构造数据: 嵌套的字典列表 data，用于描述旭日图的数据结构。
# 每个字典表示一个层级的数据:
# - name 是节点名称，children 则是该节点的子节点。
# - 每个子节点包含 name 和 value（表示该节点的数值）
data = [
    {"name": "A", "children": [
        {"name": "A1", "value": 10},
        {"name": "A2", "value": 5}
    ]},
    {"name": "B", "children": [
        {"name": "B1", "value": 20},
        {"name": "B2", "value": 15}
    ]}
]

sunburst = (
    Sunburst() # Sunburst()：创建一个旭日图对象。
    .add(series_name="Sunburst", data_pair=data)
    # - series_name="Sunburst"：设置图表的系列名称为 "Sunburst"。
    # - data_pair=data：将前面定义的 data 数据传入图表。
    .set_global_opts(title_opts=opts.TitleOpts(title="旭日图（Sunburst）")) # 设置全局选项
)

sunburst.render('sunburst.html')
sunburst.render_notebook()


# In[10]:


# 利用plotly中express绘制

# 使用Plotly Express提供的示例数据tips()
# total_bill：账单总金额
# tip：小费金额
# sex：顾客性别
# smoker：是否吸烟
# day：星期几
# time：就餐时间（Lunch 或 Dinner）
# size：用餐人数

df=px.data.tips()	

df.head()


# In[12]:


# 创建旭日图，路径为'day','time','sex'，数值列为'total_bill'
# 用 total_bill（账单总金额）来决定每个区域的大小
fig1=px.sunburst(df,path=['day','time','sex'],values='total_bill')
fig1.show()

# 创建旭日图，并设置路径、数值列，根据'day'进行颜色着色
fig2=px.sunburst(df,path=['sex','day','time'],
						  values='total_bill',color='day')
fig2.show()

# 创建旭日图，并设置路径、数值列，根据'time'进行颜色着色
fig3=px.sunburst(df,path=['sex','day','time'],
					 values='total_bill',color='time')
fig3.show()

# 创建旭日图，并设置路径、数值列，根据'time'进行颜色着色，
# 并使用离散颜色映射(color_discrete_map)为不同的时间段(time)设置不同的颜色
# '(?)'：将缺失的 time 数据着色为黑色。
# 'Lunch'：将 "Lunch" 的区域着色为金色。
# 'Dinner'：将 "Dinner" 的区域着色为深蓝色。
fig4=px.sunburst(df,path=['sex','day','time'],
					values='total_bill',color='time',
                    color_discrete_map={'(?)':'black','Lunch':'gold',
										  'Dinner':'darkblue'})
fig4.show()


# ### 树状图与谱系图

# In[3]:


# 使用pyecharts绘制树状图
data = [
    {"name": "A", "children": [
        {"name": "A1", "value": 10},
        {"name": "A2", "value": 5}
    ]},
    {"name": "B", "children": [
        {"name": "B1", "value": 20},
        {"name": "B2", "value": 15}
    ]}
]

tree = Tree()
tree.add("Tree", data)
tree.set_global_opts(title_opts=opts.TitleOpts(title="树状图"))
tree.render('tree_chart.html')


# In[17]:


# 使用plotly绘制

# 使用列表推导式进行数据转换
data_flat = [
    {"parent": item["name"], "child": child["name"], "value": child["value"]}
    for item in data
    for child in item["children"]
]

print(data_flat)

# 创建 DataFrame
df = pd.DataFrame(data_flat)

print(df)


# 使用 plotly.express 创建 treemap 图
fig = px.treemap(df, 
                 path=['parent', 'child'],  # 设置路径的层次
                 values='value',            # 设置每个矩形的大小
                 title="树形图 (Treemap)")

# 显示图表
fig.show()


# In[26]:


# 从plotly中导入gapminder数据集，并选择2007年的数据
# 绘制树状图（矩形树状图）

# gapminder 数据集包含多个国家在不同年份的数据
# 涉及的变量包括 country、continent、year、lifeExp（预期寿命）、pop（人口）、gdpPercap（人均 GDP）等。
df=px.data.gapminder().query("year==2007")

# 使用treemap图表绘制
fig=px.treemap(df,
                 path=[px.Constant("world"),'continent','country'],
                 values='pop',color='lifeExp',
                 hover_data=['iso_alpha'],
                 color_continuous_scale='RdBu',
                 color_continuous_midpoint=np.average(df['lifeExp'],
						         weights=df['pop']))

fig.update_layout(margin=dict(t=50,l=25,r=25,b=25))	# 更新图表布局
fig.show()


# In[18]:


# 使用plotly绘制谱系图
import plotly.figure_factory as ff	# 导入P figure_factory模块，用于创建图表

# 生成随机数据，创建一个18x10的随机数组，表示18个样本，每个样本有10个维度
X=np.random.rand(18,10)
# print(X)
# 创建谱系图，使用随机数据X，设置颜色阈值为1.5
# create_dendrogram函数来创建谱系图。
# 该函数接受一个二维数组 X（即样本和特征矩阵），并基于该数据计算层次聚类结果。
# color_threshold=1.5 设置了一个颜色阈值，用于根据聚类的相似度为不同的簇分配不同的颜色。
# 如果聚类的相似度高于阈值，则会为它们分配相同的颜色。
fig=ff.create_dendrogram(X,color_threshold=1.5)
# 更新图表布局，设置宽度为800像素，高度为500像素
fig.update_layout(width=800,height=500)
fig.show()


# In[29]:


# matplotlib 本身不提供直接绘制谱系图的功能，但它可以结合 scipy（科学计算库）中的层次聚类（hierarchical clustering）模块来绘制谱系图。
# seaborn 是基于 matplotlib 的库，虽然 seaborn 本身没有提供直接绘制谱系图的函数，
# 但它提供了一个基于 scipy 层次聚类的 clustermap 函数，能够通过热图与层次聚类图组合的方式间接展示谱系图。
# pyecharts它本身不支持绘制传统意义上的谱系图（即层次聚类谱系图）


# ### 桑葚图

# In[27]:


from matplotlib.sankey import Sankey

# 创建Sankey对象
sankey = Sankey()

# 添加流动数据，格式为 (流入流出，流量)
# flows：指定了各个流的数值。正数表示流入，负数表示流出。
# labels：为每个流添加标签，用于标识每个流动的来源或去向。
# 指定每个箭头的方向。orientations 的数量应与 flows 和 labels 的数量一致。
# https://matplotlib.org/stable/api/sankey_api.html
sankey.add(flows=[10, -10, 5, -5], 
           labels=['Input1', 'Output1', 'Input2', 'Output2'], 
           orientations=[1, -1, 1, -1])

# 绘制图形
sankey.finish()

# 显示图表
plt.title('Sankey Diagram')
plt.show()


# In[15]:


from pyecharts.charts import Sankey

# 构造节点数据
nodes = [
    {"name": "A"},
    {"name": "B"},
    {"name": "C"},
    {"name": "D"}
]

# 构造流动数据
links = [
    {"source": "A", "target": "B", "value": 10},
    {"source": "A", "target": "C", "value": 20},
    {"source": "B", "target": "D", "value": 5},
    {"source": "C", "target": "D", "value": 15}
]

# 创建Sankey图
sankey = (
    Sankey()
    .add("Sankey Diagram", nodes=nodes, links=links)  # 传入 `nodes` 和 `links`
    .set_global_opts(title_opts=opts.TitleOpts(title="Sankey Diagram"))
)

# 渲染并展示图表
sankey.render("sankey_chart.html")


# In[17]:


import plotly.graph_objects as go

# 创建桑基图
fig=go.Figure(go.Sankey(
    arrangement="snap",							# 设置节点位置的排列方式
    node={"label":["A","B","C","D","E","F"],	# 节点标签
            "x":[0.2,0.1,0.5,0.7,0.3,0.5],		# 节点的x坐标
            "y":[0.7,0.5,0.2,0.4,0.2,0.3],		# 节点的y坐标
            'pad':10},							# 节点的间距
    link={"source":[0,0,1,2,5,4,3,5],			# 每条链接的源节点索引
           "target":[5,3,4,3,0,2,2,3],			# 每条链接的目标节点索引
           "value":[1,2,1,1,1,1,1,2]} 			# 每条链接的值，表示流动的数量
))
fig.show()


# ## 局部整体型数据

# ### 饼图

# In[42]:


# 随机生成分类和对应值
categories = ['A', 'B', 'C', 'D']
values = [random.randint(10, 100) for _ in categories]
print(categories)
print(values)


# In[43]:


# 使用matplotlib绘制
plt.figure(figsize=(6, 6))
plt.pie(values, # 饼图中的数据值，它代表每个类别的数量或占比
        labels=categories, # 设置每个扇区的标签，categories 是一个包含分类名称的列表
        autopct='%1.1f%%', # 自动显示每个扇区所占比例，格式为浮动小数点，保留 1 位小数
        startangle=140, #设置饼图的起始角度。默认情况下，饼图的第一个扇区从 0 度开始
       )
plt.title("Matplotlib Pie Chart")
plt.axis('equal')
plt.show()


# In[47]:


# 使用pyecharts绘制饼图
# 直接创建数据： [['A', 35], ['B', 99], ['C', 11], ['D', 48]]
data_pair = [['A', 35], ['B', 99], ['C', 11], ['D', 48]]

# 或者使用函数完成这项操作
# data_pair = [list(z) for z in zip(categories, values)]
print(data_pair)

pie = (
    Pie()
    .add("", data_pair)
    .set_global_opts(title_opts=opts.TitleOpts(title="Pyecharts Pie Chart"))
    .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {d}%"))
)

pie.render("random_pie_pyecharts.html")


# In[48]:


# 使用plotly绘制
df = pd.DataFrame({'Category': categories, 'Value': values})

fig = px.pie(df, names='Category', values='Value', title='Plotly Pie Chart')
fig.show()


# In[51]:


# 使用pandas内嵌plot进行绘图
# 准备数据

# 绘制饼图，将 Category 列设置为索引列，使得每个饼图的扇区都有对应的标签
df.set_index('Category')['Value'].plot(kind='pie', autopct='%1.1f%%', figsize=(6, 6), startangle=140)

# 添加标题
plt.title("Category Distribution")

# 显示图形
plt.show()


# In[53]:


# 创建一个大小为(6,4)的图，并设置子图属性为“等比例”
fig,ax=plt.subplots(figsize=(6,4),subplot_kw=dict(aspect="equal"))

# 配方和数据
recipe=["225 g flour","90 g sugar","1 egg",
          "60 g butter","100 ml milk","1/2 package of yeast"]
data=[225,90,50,60,100,5]
# 绘制饼图
wedges,texts=ax.pie(data,wedgeprops=dict(width=0.5),startangle=-40)

# 注释框的属性
bbox_props=dict(boxstyle="square,pad=0.3",fc="w",ec="k",lw=0.72)
kw=dict(arrowprops=dict(arrowstyle="-"),
          bbox=bbox_props,zorder=0,va="center")

# 遍历每个扇形并添加注释
for i,p in enumerate(wedges):
    # 计算注释的位置
    ang=(p.theta2-p.theta1)/2.+p.theta1
    y=np.sin(np.deg2rad(ang))
    x=np.cos(np.deg2rad(ang))
    # 水平对齐方式根据 x 坐标的正负确定
    horizontalalignment={-1:"right",1:"left"}[int(np.sign(x))]
    # 设置连接线的样式
    connectionstyle=f"angle,angleA=0,angleB={ang}"
    kw["arrowprops"].update({"connectionstyle":connectionstyle})
    # 添加注释
    ax.annotate(recipe[i],xy=(x,y),xytext=(1.35*np.sign(x),1.4*y),
                horizontalalignment=horizontalalignment,**kw)
ax.set_title("Matplotlib bakery:A donut")	# 设置标题
plt.show()


# ### 环形饼图

# In[3]:


# 使用matplotlib绘制
# 数据
labels = ['A', 'B', 'C', 'D']
sizes = [15, 30, 45, 10]

# 绘制环形饼图
# 修改饼图的 wedgeprops 来实现环形饼状图。
# 通过设置 wedgeprops 中的 width 参数，可以控制饼图的厚度。
plt.figure(figsize=(6, 6))
plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=140, wedgeprops={'width': 0.3})

# 添加标题
plt.title("Doughnut Chart")

# 显示图表
plt.axis('equal')  # 保证饼图是圆形
plt.show()


# In[4]:


# 使用pyecharts绘制
# 数据
data = [
    ("A", 30),
    ("B", 40),
    ("C", 50),
    ("D", 80)
]

# 创建环形饼图
# 通过设置 radius 属性来实现。radius 由两个值构成，第一个值是内半径，第二个值是外半径。
pie = (
    Pie()
    .add("", data, radius=["40%", "70%"])
    .set_global_opts(title_opts=opts.TitleOpts(title="Doughnut Chart"))
)

# 渲染并展示图表
pie.render("doughnut_chart.html")


# In[6]:


# 使用plotly绘制
# 数据
labels = ['A', 'B', 'C', 'D']
values = [15, 30, 45, 10]

# 绘制环形饼图
# 通过设置 hole 参数来创建一个环形图
fig = px.pie(names=labels, values=values, hole=0.5)

# 设置标题
fig.update_layout(title="Doughnut Chart")

# 显示图表
fig.show()


# ### 嵌套饼图

# In[20]:


# 使用matplotlib绘制
# 通过绘制多个饼图，并调整它们的半径来实现。
# 通常使用 axes 参数来控制多个饼图的显示。
# 示例数据
inner_labels = ['Fruit', 'Vegetable']
outer_labels = ['Apple', 'Banana', 'Carrot', 'Broccoli']

# 内圈：大分类
inner_sizes = [60, 40]

# 外圈：小分类
outer_sizes = [30, 30, 20, 20]  # 注意加起来要和对应内圈比例匹配

# 创建画布
fig, ax = plt.subplots(figsize=(6,6))

# 画内圈
ax.pie(inner_sizes, radius=1, labels=inner_labels, labeldistance=0.7, wedgeprops=dict(width=0.3))

# 画外圈
ax.pie(outer_sizes, radius=1.3, labels=outer_labels, labeldistance=0.85, wedgeprops=dict(width=0.3))

plt.title('Nested Pie Chart')
plt.show()


# In[21]:


# 使用pyecharts进行绘制

# 外层数据
data_outer = [
    ("A", 60),
    ("B", 30),
    ("C", 10)
]

# 内层数据
data_inner = [
    ("A1", 20),
    ("A2", 20),
    ("A3", 20),
    ("B1", 20),
    ("B2", 10),
    ("C", 10),
]

# 创建图表
pie = (
    Pie()
    .add("", data_outer, radius=["50%", "70%"])  # 外层饼图
    .add("", data_inner, radius=["20%", "50%"])  # 内层饼图
    .set_global_opts(title_opts=opts.TitleOpts(title="Nested Pie Chart"))
)

# 渲染并展示图表
pie.render("nested_pie_chart.html")


# In[24]:


# 使用plotly绘制
# 外层数据
labels_outer = ['A', 'B', 'C']
values_outer = [60, 30, 10]

# 内层数据
labels_inner = ['A1', 'A2', 'A3', 'B1', 'B2', 'C1']
values_inner = [20, 20, 20, 20, 10, 10]

# 创建外层饼图
fig = go.Figure(go.Pie(labels=labels_outer, values=values_outer, hole=0.5, title="Nested Pie Chart"))

# 创建内层饼图
# domain表示控制内层饼图的位置：内层饼图占据整个画布宽度（高度）的30%到70%，使其位于外层饼图的中央
# 去掉domain看下效果
fig.add_trace(go.Pie(labels=labels_inner, values=values_inner, hole=0.6, domain=dict(x=[0.3, 0.7], y=[0.3, 0.7])))

# 更新布局
fig.update_layout(
    title_text="Nested Pie Chart",
    showlegend=True
)

# 显示图表
fig.show()


# In[ ]:




