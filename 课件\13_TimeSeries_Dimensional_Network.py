#!/usr/bin/env python
# coding: utf-8

# # Lecture 13: 
# - 时间序列数据
# - 多维数据
# - 单个纬度数据
# - 网络数据

# In[1]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Line, Kline
from pyecharts.options import AreaStyleOpts, TitleOpts
import random
import plotly.express as px
import plotly.graph_objects as go

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号


# ## 时间序列可视化

# ### 折线图

# In[2]:


# 使用matplotlib绘制简单的折线图
x = [1, 2, 3, 4, 5]
y = [2, 3, 5, 7, 11]

plt.plot(x, y, marker='o')
plt.title("Line Chart - Matplotlib")
plt.xlabel("X Axis")
plt.ylabel("Y Axis")
plt.grid(True)
plt.show()


# In[3]:


# 构造 2025年1月的数据
# date_range用于生成日期范围的函数，start为初始日期，end为结束日期，freq是频率为“天”（Daily）
date_range = pd.date_range(start="2025-01-01", end="2025-01-31", freq="D")
df = pd.DataFrame({
    'date': date_range,
    'value': np.random.randint(0, 100, len(date_range))
})
plt.plot(df['date'].tolist(), df['value'].tolist(), marker='o')
plt.title("Line Chart （Time Series） - Matplotlib")
plt.xlabel("X Axis")
plt.ylabel("Y Axis")
plt.grid(True)
plt.show()


# In[6]:


# 创建 1x2 子图布局
fig, axes = plt.subplots(1, 2, figsize=(14, 5))

# 第一个子图：普通折线图
axes[0].plot(x, y, marker='o')
axes[0].set_title("Line Chart - Matplotlib")
axes[0].set_xlabel("X Axis")
axes[0].set_ylabel("Y Axis")
axes[0].grid(True)

# 第二个子图：时间序列折线图
axes[1].plot(df['date'].tolist(), df['value'].tolist(), marker='o')
axes[1].set_title("Line Chart (Time Series) - Matplotlib")
axes[1].set_xlabel("Date")
axes[1].set_ylabel("Value")
axes[1].grid(True)

# 注意下面的参数使得X轴标签有什么变化？
axes[1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()


# In[4]:


# 使用seaborn绘制
sns.lineplot(data=df, x='date',y='value')
plt.title("Line Chart - Seaborn")
plt.xlabel("X Axis")
plt.ylabel("Y Axis")
plt.grid(True)
plt.show()


# In[5]:


# 使用pyecharts绘制
line = Line()
line.add_xaxis(df['date'].tolist())
line.add_yaxis("Y-Value", df['value'].tolist())
# 在 Jupyter 中显示
# line.render_notebook()  
# 输出为HTML文件
line.render("line_chart.html")  


# In[6]:


# 使用plotly绘制
fig = px.line(df, 
              x='date', 
              y='value', 
              title='Line Chart - Plotly Express',
              markers=True)
fig.show()


# ### 面积图

# In[4]:


# 使用matplotlib绘制
plt.fill_between(df['date'], df['value'], color='skyblue', alpha=0.5)
plt.plot(df['date'].tolist(), df['value'].tolist(), color='blue')  # 边缘线
plt.title('Area Chart - Matplotlib')
plt.xlabel('X Axis')
plt.ylabel('Y Axis')
plt.grid(True)
plt.show()


# In[5]:


# 使用seaborn绘制
plt.figure(figsize=(6, 4))
plt.fill_between(df['date'], df['value'], color='lightgreen', alpha=0.6)
sns.lineplot(x='date', y='value', data=df, color='green')
plt.title('Area Chart - Seaborn Style')
plt.xlabel('X Axis')
plt.ylabel('Y Axis')
plt.show()


# In[40]:


# 使用pyecharts绘制
line = Line()
line.add_xaxis(df['date'].tolist())
line.add_yaxis("Series",
               df['value'].tolist(),
               is_smooth=True,
               areastyle_opts=AreaStyleOpts(opacity=0.5)
              )
line.set_global_opts(title_opts=TitleOpts(title="Area Chart - Pyecharts"))
line.render("area_chart.html")


# In[41]:


# 使用plotly中的express进行绘制
fig = px.area(df, 
              x='date', 
              y='value', 
              title='Area Chart - Plotly Express')
fig.show()


# ### K线图

# In[6]:


data = {
    'date': pd.date_range(start='2025-01-01', periods=5),
    'open': [100, 102, 101, 105, 107],
    'close': [102, 101, 105, 107, 106],
    'high': [103, 103, 106, 108, 109],
    'low': [99, 100, 100, 104, 105]
}
df = pd.DataFrame(data)
df


# In[7]:


df[['open', 'close', 'low', 'high']].values.tolist()


# In[46]:


# 使用pyecharts绘制
kline = Kline()
kline.add_xaxis(df['date'].dt.strftime("%Y-%m-%d").tolist())
kline.add_yaxis("Price", df[['open', 'close', 'low', 'high']].values.tolist())
kline.set_global_opts(title_opts=TitleOpts(title="K Line - Pyecharts"))
kline.render("kline.html")


# In[47]:


# 使用plotly中的graph_objects里的Candlestick绘制
fig = go.Figure(data=[go.Candlestick(
    x=df['date'],
    open=df['open'],
    high=df['high'],
    low=df['low'],
    close=df['close']
)])
fig.update_layout(title='K Line - Plotly', xaxis_title='Date', yaxis_title='Price')
fig.show()


# ### 日历图

# In[7]:


# 构造 2025年一整年的数据
date_range = pd.date_range(start="2025-01-01", end="2025-12-31", freq="D")
df = pd.DataFrame({
    'date': date_range,
    'value': np.random.randint(0, 100, len(date_range))
})
df


# In[8]:


# 使用matplotlib绘制_搭配 calmap
# !pip install calmap
import calmap

# 将 DataFrame 的 date 列设为索引
# calmap 要求输入数据的索引必须是 DatetimeIndex 或类似的时间类型
df_cal = df.set_index('date')
plt.figure(figsize=(16, 4))

# df_cal['value']： 要可视化的数据列
# 聚合方式： how='sum'，其他可选值如 mean（均值）、max（最大值）等
# cmap：颜色映射为 黄绿色渐变（Yellow-Green），颜色越深表示值越大
# fillcolor：无数据的日期格子填充为浅灰色
# 设置日历格子边框线的宽度为0.5
calmap.calendarplot(df_cal['value'], 
                    how='sum', 
                    cmap='YlGn', 
                    fillcolor='lightgrey', 
                    linewidth=0.5)
plt.title('Calendar Heatmap - Matplotlib (calmap)')
plt.show()


# In[19]:


# 使用pyecharts绘制
from pyecharts.charts import Calendar
from pyecharts import options as opts

# 将 df['date'] 和 df['value'] 组合成 PyEcharts 需要的格式
# data最终会变成： [("2023-01-01", 50), ("2023-01-02", 30), ...]
data = [(d.strftime("%Y-%m-%d"), int(v)) for d, v in zip(df['date'], df['value'])]

calendar = (
    Calendar() #创建一个日历热力图实例
    .add("", data, calendar_opts=opts.CalendarOpts(range_="2023"))
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Calendar Heatmap - Pyecharts"),
        visualmap_opts=opts.VisualMapOpts(
            max_=100,
            min_=0,
            orient="horizontal",
            is_piecewise=False,
        ),
    )
)
calendar.render("calendar.html")


# ## 多维数据可视化

# ### 热图

# In[10]:


# 创建 10x10 的随机整数矩阵
data = np.random.randint(0, 100, (10, 10))
df = pd.DataFrame(data, columns=[f"Col{i}" for i in range(10)], index=[f"Row{i}" for i in range(10)])
df


# In[12]:


# 使用matplotlib绘制
plt.figure(figsize=(8, 6))
plt.imshow(df, cmap='viridis')
plt.colorbar(label='Value')
plt.xticks(ticks=np.arange(df.shape[1]), labels=df.columns, rotation=45)
plt.yticks(ticks=np.arange(df.shape[0]), labels=df.index)
plt.title("Heatmap - Matplotlib")
plt.tight_layout()
plt.show()


# In[16]:


# 使用seaborn绘制
plt.figure(figsize=(8, 6))
sns.heatmap(df, annot=True, fmt="d", cmap="YlGnBu", linewidths=0.5)
plt.title("Heatmap - Seaborn")
plt.tight_layout()
plt.show()


# In[17]:


# 使用pyecharts绘制
from pyecharts.charts import HeatMap
from pyecharts import options as opts

# 转换数据为 Pyecharts 所需格式 [(x轴位置, y轴位置, 值), ...]
data_pair = [(j, i, int(df.iloc[i, j])) for i in range(df.shape[0]) for j in range(df.shape[1])]
# print(data_pair)

heatmap = (
    HeatMap()
    .add_xaxis(df.columns.tolist())
    .add_yaxis("Heat", df.index.tolist(), data_pair)
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Heatmap - Pyecharts"),
        visualmap_opts=opts.VisualMapOpts(min_=0, max_=100)
    )
)

heatmap.render("heatmap.html")


# In[18]:


print(df.values)
# 使用plotly.express绘制
fig = px.imshow(df.values, #二维数组
                labels=dict(x="Columns", y="Rows", color="Value"),
                x=df.columns,
                y=df.index,
                text_auto=True,
                color_continuous_scale='Viridis')

fig.update_layout(title="Heatmap - Plotly")
fig.show()


# ### 矩阵散点图

# In[19]:


# 加载数据
df = sns.load_dataset('iris')
df


# In[25]:


# 使用panda里带的模块绘制
from pandas.plotting import scatter_matrix

scatter_matrix(df.iloc[:, :-1], figsize=(10, 10), diagonal='hist')
# scatter_matrix是在绘制多个子图，因此调用 plt.title()，它只会对最后一个子图起作用
# “suptitle” 代表 “super title”——整个图（所有子图组合起来的大图）的主标题
plt.suptitle("Scatter Matrix - Matplotlib", fontsize=16)
plt.show()


# In[26]:


# 使用seaborn绘制
sns.pairplot(df, hue='species')
plt.suptitle("Pair Plot - Seaborn", y=1.02)
plt.show()


# In[28]:


# 使用plotly绘制
fig = px.scatter_matrix(df,
                        dimensions=["sepal_length", "sepal_width", "petal_length", "petal_width"],
                        color="species",
                        title="Scatter Matrix - Plotly")

# fig.update_traces(diagonal_visible=True)
fig.show()


# ### 平行坐标图

# In[36]:


# 加载数据
df = sns.load_dataset('iris')
df


# In[29]:


from pandas.plotting import parallel_coordinates
import matplotlib.pyplot as plt

plt.figure(figsize=(10, 6))
parallel_coordinates(df, 'species', colormap=plt.cm.Set2)
plt.title("Parallel Coordinates - Matplotlib")
plt.show()


# In[41]:


from pyecharts.charts import Parallel
from pyecharts import options as opts

# 将分类列species转为数字
df_copy = df.copy()
df_copy['species'] = df_copy['species'].astype('category').cat.codes

# 将数据封装成列表
data = df_copy.values.tolist()

# 维度定义
schema = [
    {"dim": 0, "name": "sepal_length"},
    {"dim": 1, "name": "sepal_width"},
    {"dim": 2, "name": "petal_length"},
    {"dim": 3, "name": "petal_width"},
    {"dim": 4, "name": "species"},
]

# 使用pyecharts绘图
parallel = (
    Parallel()
    .add_schema(schema)
    .add("Iris", data)
    .set_global_opts(title_opts=opts.TitleOpts(title="Parallel Coordinates - Pyecharts"))
)

parallel.render("parallel_pyecharts.html")


# In[39]:


fig = px.parallel_coordinates(
    df,
    dimensions=["sepal_length", "sepal_width", "petal_length", "petal_width"],
    title="Parallel Coordinates - Plotly"
)
fig.show()


# In[40]:


# 进阶
# 将species转换成数字编码
df['species_code'] = df['species'].astype('category').cat.codes

fig = px.parallel_coordinates(
    df,
    color='species_code',  # 传入数值列
    dimensions=["sepal_length", "sepal_width", "petal_length", "petal_width"],
    color_continuous_scale=px.colors.diverging.Tealrose,
    labels={"species_code": "Species"},
    title="Parallel Coordinates - Plotly"
)
fig.show()


# ## 单个维度比较图

# ### 子弹图

# In[31]:


# 参数说明：
# mode="number+gauge+delta"
# 显示数值、仪表盘和与目标的偏差。

# value
# 实际值。

# delta={'reference': xx}
# 指定目标值，并显示与其的差异。

# gauge.shape="bullet"
# 将仪表盘变成水平子弹图样式。

# steps
# 定义性能区间背景：如差、中、好。

# threshold
# 设置目标线（红线），视觉引导目标值。


# In[43]:


# 子弹图1：简单的子弹图，只显示了实际值和参考值之间的比较。
fig=go.Figure(go.Indicator(
    mode="number+gauge+delta",			# 指示器模式，包括数字、仪表盘和增减值
    gauge={'shape':"bullet"},			# 设置子弹图的形状为bullet
    value=220,							# 实际值
    delta={'reference':300},			# 增减值的参考值
    domain={'x':[0,1],'y':[0,1]},		# 子弹图所占的区域
    title={'text':"Profit"}))			# 子弹图的标题

fig.update_layout(height=250)			# 更新布局，设置图表的高度
fig.show()


# In[49]:


# 子弹图2：增加阈值和颜色阶梯，显示更详细的信息，包括颜色的变化表示不同的区间范围
fig=go.Figure(go.Indicator(
    mode="number+gauge+delta",value=220,	# 实际值
    domain={'x':[0.1,1],'y':[0,1]},			# 子弹图所占的区域
    title={'text':"<b>Profit</b>"},			# 子弹图的标题
    delta={'reference':200},				# 增减值的参考值
    gauge={'shape':"bullet",				# 设置子弹图的形状为bullet
         'axis':{'range':[None,300]},		# 指示器轴的范围
         'threshold':{'line':{'color':"red",'width':2},		# 阈值线的样式
					  'thickness':0.75,'value':280},			# 阈值的样式和值
         'steps':[{'range':[0,150],'color':"lightgray"},	# 不同范围的颜色
                  {'range':[150,250],'color':"gray"}]}))
fig.update_layout(height=250)				# 更新布局，设置图表的高度
fig.show()


# In[27]:


# matplotlib也可以绘制，但是需要自制
fig, ax = plt.subplots(figsize=(8, 2))

# 背景色条（分段）
ax.barh(0, ranges[2], color='lightgray', height=0.6)
ax.barh(0, ranges[1], color='gray', height=0.6)
ax.barh(0, ranges[0], color='darkgray', height=0.6)

# 实际值
ax.barh(0, performance, color='steelblue', height=0.3)

# 目标线
ax.plot([target, target], [-0.3, 0.3], color='red', linewidth=3)

ax.set_yticks([])
ax.set_xlim(0, 110)
ax.set_title("子弹图 - Matplotlib")
plt.show()


# ### 仪表盘图

# In[45]:


# 示例 1:创建一个简单的仪表图
fig=go.Figure(go.Indicator(
    mode="gauge+number",				# 模式设置为仪表盘模式并显示数值
    value=270,							# 设定指示器的数值为270
    domain={'x':[0,1],'y':[0,1]},		# 指示器的位置占据整个图表空间
    title={'text':"Speed"}))			# 指示器的标题为"Speed"
fig.show()


# In[46]:


# 示例 2:创建一个带有增量和阈值的仪表图
fig=go.Figure(go.Indicator(
    domain={'x':[0,1],'y':[0,1]},		# 指示器的位置占据整个图表空间
    value=450,							# 设定指示器的数值为450
    mode="gauge+number+delta",			# 模式设置为仪表盘模式、显示数值和增量
    title={'text':"Speed"},			# 指示器的标题为"Speed"
    delta={'reference':380},			# 增量设置为380
    gauge={
        'axis':{'range':[None,500]},		# 指示器轴范围设定为0到500
        'steps' :[			# 阶梯设置，将范围划分为两段，分别设定为灰色和深灰色
            {'range':[0,250],'color':"lightgray"},
            {'range':[250,400],'color':"gray"}],
        'threshold' :{'line':{'color':"red",'width':4},
             'thickness':0.75,'value':490}  	# 设定阈值，超过阈值时显示红色
    }))
fig.show()



# In[47]:


# 示例 3:创建一个带有增量和阈值的仪表图，样式定制更多
fig=go.Figure(go.Indicator(
    mode="gauge+number+delta",			# 模式设置为仪表盘模式、显示数值和增量
    value=420,							# 设定指示器的数值为420
    domain={'x':[0,1],'y':[0,1]},		# 指示器的位置占据整个图表空间
    title={'text':"Speed",'font':{'size':24}},		# 指示器标题，字体大小
    delta={'reference':400,'increasing':{'color':"RebeccaPurple"}},
     										# 增量设置为400，且增大时显示紫色
    gauge={
        'axis':{'range':[None,500],'tickwidth':1,
                'tickcolor':"darkblue"},		# 指示器轴范围设定，设置刻度宽度和颜色
        'bar':{'color':"darkblue"},			# 指示器条颜色设定为深蓝色
        'bgcolor':"white",					# 背景色设定为白色
        'borderwidth':2,					# 边框宽度设定为2
        'bordercolor':"gray",				# 边框颜色设定为灰色
        'steps':[    			# 阶梯设置，将范围划分为两段，分别设定为青色和皇家蓝
            {'range':[0,250],'color':'cyan'},
            {'range':[250,400],'color':'royalblue'}],
        'threshold':{		# 设定阈值为490，超过阈值时指示器显示红色
            'line':{'color':"red",'width':4},
            'thickness':0.75,'value':490 }}))

# 更新图表布局，设置背景色和字体颜色
fig.update_layout(paper_bgcolor="lavender",
                 font={'color':"darkblue",'family':"Arial"})
fig.show()


# ## 网络关系图

# ### 节点链接图

# In[51]:


# !pip3 install networkx
import matplotlib.pyplot as plt
import networkx as nx

plt.figure(figsize=(6, 6))

G = nx.Graph()
# 添加节点和边
G.add_edges_from([
    ('A', 'B'),
    ('A', 'C'),
    ('B', 'D'),
    ('C', 'D'),
    ('C', 'E')
])

pos = nx.spring_layout(G)  # 计算节点布局

nx.draw(G, 
        pos, 
        with_labels=True, 
        node_color='skyblue', 
        edge_color='gray', 
        node_size=1000, 
        font_size=16)
plt.title("Network Graph - Matplotlib + NetworkX")
plt.show()


# In[52]:


from pyecharts import options as opts
from pyecharts.charts import Graph

nodes = [
    {"name": "A", "symbolSize": 40},
    {"name": "B", "symbolSize": 30},
    {"name": "C", "symbolSize": 30},
    {"name": "D", "symbolSize": 20},
    {"name": "E", "symbolSize": 20},
]

links = [
    {"source": "A", "target": "B"},
    {"source": "A", "target": "C"},
    {"source": "B", "target": "D"},
    {"source": "C", "target": "D"},
    {"source": "C", "target": "E"},
]

graph = (
    Graph()
    .add("", nodes, links, repulsion=4000)
    .set_global_opts(title_opts=opts.TitleOpts(title="Network Graph - Pyecharts"))
)

# graph.render_notebook()  # Jupyter环境显示
graph.render("graph.html")


# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:




