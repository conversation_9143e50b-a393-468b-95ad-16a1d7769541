{"cells": [{"cell_type": "markdown", "id": "6b7a8fc2-002b-4a2a-9f5a-1de78608331a", "metadata": {}, "source": ["# 14 Dash<PERSON> with <PERSON>"]}, {"cell_type": "markdown", "id": "af702ba8-6112-4037-b427-ba2d3b58bbd6", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## 词云图"]}, {"cell_type": "code", "execution_count": 6, "id": "53bd5d5e-7826-45e8-a6e6-9b3f2d84598b", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 静态图\n", "from wordcloud import WordCloud\n", "import matplotlib.pyplot as plt\n", "\n", "# 词频数据：字典（键：单词，值：数值）\n", "text = {\"Python\": 100, \"数据\": 80, \"可视化\": 60, \"机器学习\": 90}\n", "\n", "# 创建词云对象\n", "wordcloud = WordCloud(font_path=\"simhei.ttf\",  # 中文字体\n", "                      width=800, \n", "                      height=400, \n", "                      background_color='white'\n", "                     ).generate_from_frequencies(text)\n", "\n", "# 展示词云图\n", "plt.figure(figsize=(10, 5))\n", "plt.imshow(wordcloud)\n", "plt.axis(\"off\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 2, "id": "a4bf3ca0-78da-41da-814f-243bde1ec81b", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "<script>\n", "    require.config({\n", "        paths: {\n", "            'echarts':'https://assets.pyecharts.org/assets/v5/echarts.min', 'echarts-wordcloud':'https://assets.pyecharts.org/assets/v5/echarts-wordcloud.min'\n", "        }\n", "    });\n", "</script>\n", "\n", "        <div id=\"8be41d68295a4e2299a28fb7354407b7\" style=\"width:900px; height:500px;\"></div>\n", "\n", "<script>\n", "        require(['echarts', 'echarts-wordcloud'], function(echarts) {\n", "                var chart_8be41d68295a4e2299a28fb7354407b7 = echarts.init(\n", "                    document.getElementById('8be41d68295a4e2299a28fb7354407b7'), 'white', {renderer: 'canvas'});\n", "                var option_8be41d68295a4e2299a28fb7354407b7 = {\n", "    \"animation\": true,\n", "    \"animationThreshold\": 2000,\n", "    \"animationDuration\": 1000,\n", "    \"animationEasing\": \"cubicOut\",\n", "    \"animationDelay\": 0,\n", "    \"animationDurationUpdate\": 300,\n", "    \"animationEasingUpdate\": \"cubicOut\",\n", "    \"animationDelayUpdate\": 0,\n", "    \"aria\": {\n", "        \"enabled\": false\n", "    },\n", "    \"color\": [\n", "        \"#5470c6\",\n", "        \"#91cc75\",\n", "        \"#fac858\",\n", "        \"#ee6666\",\n", "        \"#73c0de\",\n", "        \"#3ba272\",\n", "        \"#fc8452\",\n", "        \"#9a60b4\",\n", "        \"#ea7ccc\"\n", "    ],\n", "    \"series\": [\n", "        {\n", "            \"type\": \"wordCloud\",\n", "            \"name\": \"\\u8bcd\\u4e91\",\n", "            \"shape\": \"circle\",\n", "            \"rotationRange\": [\n", "                -90,\n", "                90\n", "            ],\n", "            \"rotationStep\": 45,\n", "            \"girdSize\": 20,\n", "            \"sizeRange\": [\n", "                20,\n", "                100\n", "            ],\n", "            \"data\": [\n", "                {\n", "                    \"name\": \"<PERSON>\",\n", "                    \"value\": 100,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(121,129,25)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6570\\u636e\",\n", "                    \"value\": 80,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(44,158,48)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u53ef\\u89c6\\u5316\",\n", "                    \"value\": 60,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(135,17,82)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u673a\\u5668\\u5b66\\u4e60\",\n", "                    \"value\": 90,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(66,96,95)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"\\u6df1\\u5ea6\\u5b66\\u4e60\",\n", "                    \"value\": 70,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(153,9,149)\"\n", "                    }\n", "                },\n", "                {\n", "                    \"name\": \"AI\",\n", "                    \"value\": 95,\n", "                    \"textStyle\": {\n", "                        \"color\": \"rgb(124,139,110)\"\n", "                    }\n", "                }\n", "            ],\n", "            \"drawOutOfBound\": false,\n", "            \"textStyle\": {\n", "                \"emphasis\": {}\n", "            }\n", "        }\n", "    ],\n", "    \"legend\": [\n", "        {\n", "            \"data\": [],\n", "            \"selected\": {},\n", "            \"show\": true,\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"itemWidth\": 25,\n", "            \"itemHeight\": 14,\n", "            \"backgroundColor\": \"transparent\",\n", "            \"borderColor\": \"#ccc\",\n", "            \"borderRadius\": 0,\n", "            \"pageButtonItemGap\": 5,\n", "            \"pageButtonPosition\": \"end\",\n", "            \"pageFormatter\": \"{current}/{total}\",\n", "            \"pageIconColor\": \"#2f4554\",\n", "            \"pageIconInactiveColor\": \"#aaa\",\n", "            \"pageIconSize\": 15,\n", "            \"animationDurationUpdate\": 800,\n", "            \"selector\": false,\n", "            \"selectorPosition\": \"auto\",\n", "            \"selectorItemGap\": 7,\n", "            \"selectorButtonGap\": 10\n", "        }\n", "    ],\n", "    \"tooltip\": {\n", "        \"show\": true,\n", "        \"trigger\": \"item\",\n", "        \"triggerOn\": \"mousemove|click\",\n", "        \"axisPointer\": {\n", "            \"type\": \"line\"\n", "        },\n", "        \"showContent\": true,\n", "        \"alwaysShowContent\": false,\n", "        \"showDelay\": 0,\n", "        \"hideDelay\": 100,\n", "        \"enterable\": false,\n", "        \"confine\": false,\n", "        \"appendToBody\": false,\n", "        \"transitionDuration\": 0.4,\n", "        \"textStyle\": {\n", "            \"fontSize\": 14\n", "        },\n", "        \"borderWidth\": 0,\n", "        \"padding\": 5,\n", "        \"order\": \"seriesAsc\"\n", "    },\n", "    \"title\": [\n", "        {\n", "            \"show\": true,\n", "            \"text\": \"\\u8bcd\\u4e91\\u56fe\",\n", "            \"target\": \"blank\",\n", "            \"subtarget\": \"blank\",\n", "            \"padding\": 5,\n", "            \"itemGap\": 10,\n", "            \"textAlign\": \"auto\",\n", "            \"textVerticalAlign\": \"auto\",\n", "            \"triggerEvent\": false\n", "        }\n", "    ]\n", "};\n", "                chart_8be41d68295a4e2299a28fb7354407b7.setOption(option_8be41d68295a4e2299a28fb7354407b7);\n", "        });\n", "    </script>\n"], "text/plain": ["<pyecharts.render.display.HTML at 0x240505681f0>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# pyecharts绘制\n", "from pyecharts.charts import WordCloud\n", "from pyecharts import options as opts\n", "\n", "# 词云数据：元组列表\n", "# 每个元组第一个元素是“单词”，第二个元素是个数\n", "words = [(\"Python\", 100), \n", "         (\"数据\", 80), \n", "         (\"可视化\", 60), \n", "         (\"机器学习\", 90), \n", "         (\"深度学习\", 70), \n", "         (\"AI\", 95)]\n", "\n", "# 创建词云图\n", "wc = (\n", "    WordCloud()\n", "    .add(series_name=\"词云\", data_pair=words, word_size_range=[20, 100])\n", "    .set_global_opts(title_opts=opts.TitleOpts(title=\"词云图\"))\n", ")\n", "\n", "# 在jupyter notebook显示\n", "wc.render_notebook()"]}, {"cell_type": "markdown", "id": "cf8caa1f-2df4-418e-8a69-deabf7369a6c", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## 数据大屏"]}, {"cell_type": "markdown", "id": "3a3168f8-4cb2-45e4-a23b-49675fdb02db", "metadata": {}, "source": ["### 简单的dash页面"]}, {"cell_type": "code", "execution_count": 7, "id": "8ea00678-1297-46d8-b81c-c395d3fd3d80", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8050/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x24063a791e0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import dash\n", "from dash import html\n", "\n", "app = dash.Dash(__name__)  # 初始化 app\n", "\n", "app.layout = html.Div([    # 设置网页结构\n", "    html.H1(\"欢迎来到数据大屏\"),\n", "    html.P(\"这个页面由 Python 构建\")\n", "])\n", "\n", "app.run_server(debug=True) # 启动网页\n"]}, {"cell_type": "markdown", "id": "dcbf2207-397f-4bd3-982d-831115937778", "metadata": {}, "source": ["### 添加图表"]}, {"cell_type": "code", "execution_count": 8, "id": "f1f2539c-c3b9-47a2-876a-0bb5f8e6b4cb", "metadata": {}, "outputs": [], "source": ["from dash import dcc\n", "import plotly.express as px\n", "\n", "df = px.data.iris()\n", "fig = px.scatter(df, x=\"sepal_width\", y=\"sepal_length\", color=\"species\")\n", "\n", "app.layout = html.Div([\n", "    html.H1(\"Iris 数据可视化\"),\n", "    dcc.Graph(figure=fig)\n", "])"]}, {"cell_type": "code", "execution_count": 10, "id": "f65ed506-3a82-4ba1-8596-8d2cbb0534a6", "metadata": {}, "outputs": [], "source": ["# 上述代码会覆盖掉之前的信息，因此采用下面的合并方式"]}, {"cell_type": "code", "execution_count": 9, "id": "373e9930-59bb-41e4-8a0c-4a3c69ce634c", "metadata": {}, "outputs": [], "source": ["app.layout = html.Div([\n", "    html.H1(\"欢迎来到数据大屏\"),\n", "    html.P(\"这个页面由 Python 构建\"),\n", "    \n", "    html.Hr(),  # 添加分隔线\n", "\n", "    html.H1(\"Iris 数据可视化\"),\n", "    dcc.Graph(figure=fig)\n", "])"]}, {"cell_type": "markdown", "id": "6a6fed03-09b6-44ed-94e2-007ce10e1e5c", "metadata": {}, "source": ["### 实现交互"]}, {"cell_type": "code", "execution_count": 12, "id": "0f4480fc-b0a1-4026-97c6-6677ae4bfe6d", "metadata": {}, "outputs": [], "source": ["# 可以在该界面通过 “关闭”按钮先关闭之前的网页应用，释放端口"]}, {"cell_type": "code", "execution_count": 14, "id": "2e7ed51b-4ebe-4363-9241-1f8666da3df5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>City</th>\n", "      <th>Product</th>\n", "      <th>Sales</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Beijing</td>\n", "      <td>A</td>\n", "      <td>100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Beijing</td>\n", "      <td>B</td>\n", "      <td>150</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Beijing</td>\n", "      <td>C</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Beijing</td>\n", "      <td>D</td>\n", "      <td>70</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Shanghai</td>\n", "      <td>A</td>\n", "      <td>120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Shanghai</td>\n", "      <td>B</td>\n", "      <td>90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Shanghai</td>\n", "      <td>C</td>\n", "      <td>60</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Shanghai</td>\n", "      <td>D</td>\n", "      <td>130</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Shenzhen</td>\n", "      <td>A</td>\n", "      <td>110</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Shenzhen</td>\n", "      <td>B</td>\n", "      <td>100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Shenzhen</td>\n", "      <td>C</td>\n", "      <td>85</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Shenzhen</td>\n", "      <td>D</td>\n", "      <td>95</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        City Product  Sales\n", "0    Beijing       A    100\n", "1    Beijing       B    150\n", "2    Beijing       C     80\n", "3    Beijing       D     70\n", "4   Shanghai       A    120\n", "5   Shanghai       B     90\n", "6   Shanghai       C     60\n", "7   Shanghai       D    130\n", "8   Shenzhen       A    110\n", "9   Shenzhen       B    100\n", "10  Shenzhen       C     85\n", "11  Shenzhen       D     95"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["import dash\n", "from dash import html, dcc, Input, Output\n", "import plotly.express as px\n", "import pandas as pd\n", "\n", "# 创建数据，为后续绘制饼图做准备\n", "# 假设我们这里设置三个城市每个产品（A B C D）的销量\n", "data = {\n", "    'City': ['Beijing'] * 4 + ['Shanghai'] * 4 + ['Shenzhen'] * 4,\n", "    'Product': ['A', 'B', 'C', 'D'] * 3,\n", "    'Sales': [100, 150, 80, 70, 120, 90, 60, 130, 110, 100, 85, 95]\n", "}\n", "df = pd.DataFrame(data)\n", "df"]}, {"cell_type": "code", "execution_count": 15, "id": "c22be580-d43d-4dc4-b45b-c6a10c2f8a18", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8050/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x24067357df0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "# 初始化app\n", "app = dash.Dash(__name__)\n", "\n", "# 设置页面布局，注意这里我们开始使用图表\n", "app.layout = html.Div([\n", "    html.H1(\"产品销售分布饼图\", style={'textAlign': 'center'}),\n", "    \n", "    html.P(\"请选择城市：\"),\n", "    dcc.Dropdown(\n", "        id='city-dropdown',\n", "        options=[{'label': city, 'value': city} for city in df['City'].unique()],\n", "        value='Beijing'\n", "    ),\n", "    \n", "    dcc.Graph(id='pie-chart')\n", "])\n", "\n", "# 回调函数 callback\n", "@app.callback(\n", "    Output('pie-chart', 'figure'),\n", "    Input('city-dropdown', 'value')\n", ")\n", "\n", "def update_pie_chart(selected_city):\n", "    filtered_df = df[df['City'] == selected_city]\n", "    fig = px.pie(filtered_df, names='Product', values='Sales',\n", "                 title=f\"{selected_city} 的产品销售占比\")\n", "    return fig\n", "\n", "# 启动应用\n", "if __name__ == '__main__':\n", "    app.run_server(debug=True)"]}, {"cell_type": "markdown", "id": "a2183410-4044-4a9c-ab3a-66c396f21feb", "metadata": {}, "source": ["### 拓展布局，实现：\n", "- html.Div(style=...) 实现\n", "- 使用 dcc.Dropdown 控制数据变化\n", "- @app.callback 多个输出（卡片 + 图表联动更新）"]}, {"cell_type": "code", "execution_count": 17, "id": "4346155a-431b-4fac-9e6b-901bc5460f43", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8050/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x240674922f0>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import dash\n", "from dash import html, dcc, Input, Output\n", "import pandas as pd\n", "import plotly.express as px\n", "\n", "# 创建数据\n", "data = {\n", "    'City': ['Beijing'] * 4 + ['Shanghai'] * 4 + ['Shenzhen'] * 4,\n", "    'Product': ['A', 'B', 'C', 'D'] * 3,\n", "    'Sales': [100, 150, 80, 70, 120, 90, 60, 130, 110, 100, 85, 95]\n", "}\n", "df = pd.DataFrame(data)\n", "\n", "# 初始化 Dash 应用\n", "app = dash.Dash(__name__)\n", "\n", "# 页面布局\n", "app.layout = html.Div([\n", "    # 顶部标题，设置格式\n", "    html.H1(\"产品销售数据大屏\", style={'textAlign': 'center', 'marginBottom': '30px'}),\n", "\n", "    # 控件区域\n", "    html.Div([\n", "        html.Label(\"请选择城市：\"),\n", "        dcc.Dropdown(\n", "            id='city-dropdown',\n", "            options=[{'label': c, 'value': c} for c in df['City'].unique()],\n", "            value='Beijing',\n", "            style={'width': '300px'}\n", "        )\n", "    ], style={'marginBottom': '30px'}),\n", "\n", "    # 统计卡片\n", "    html.Div(id='stats-cards', style={\n", "        'display': 'flex',\n", "        'justifyContent': 'space-around',\n", "        'marginBottom': '40px'\n", "    }),\n", "\n", "    # 图表区域\n", "    html.Div([\n", "        html.Div([\n", "            dcc.Graph(id='bar-chart')\n", "        ], style={'width': '48%', 'display': 'inline-block'}),\n", "\n", "        html.Div([\n", "            dcc.Graph(id='pie-chart')\n", "        ], style={'width': '48%', 'display': 'inline-block'})\n", "    ])\n", "], style={'padding': '20px'})\n", "\n", "# 回调：更新卡片 & 图表\n", "@app.callback(\n", "    [Output('stats-cards', 'children'), # id是 'stats-cards' 组件的 children 属性（就是这个区域里面显示的内容，通常是子组件）\n", "     Output('bar-chart', 'figure'),\n", "     Output('pie-chart', 'figure')],\n", "    [Input('city-dropdown', 'value')]\n", ")\n", "\n", "def update_dashboard(city):\n", "    filtered = df[df['City'] == city]\n", "\n", "    total_sales = filtered['Sales'].sum()\n", "    product_count = filtered['Product'].nunique()\n", "    avg_sales = round(filtered['Sales'].mean(), 2)\n", "\n", "    # 卡片组件——注意这里存储的是CSS样式\n", "    # padding：内边距，卡片内容离边框距离，20像素。\n", "    # borderRadius：圆角，10像素圆滑边角。\n", "    # backgroundColor：背景色，浅灰色 #f0f0f0。\n", "    # width：卡片宽度，这里设置为父容器的 30%。\n", "    # textAlign：文字居中。\n", "    # boxShadow：阴影效果，使卡片有立体感。\n", "    card_style = {\n", "        'padding': '20px',\n", "        'borderRadius': '10px',\n", "        'backgroundColor': '#f0f0f0',\n", "        'width': '30%',\n", "        'textAlign': 'center',\n", "        'boxShadow': '2px 2px 8px rgba(0,0,0,0.1)'\n", "    }\n", "\n", "    # 这是一个列表，包含了 3 个 html.Div，代表 3 个统计卡片。\n", "    # 每个卡片结构一样：\n", "    # - html.Div 作为卡片容器\n", "    # - 里面有标题（html.H4）和具体数值（html.H2）\n", "    # - 通过 style=card_style，这 3 个卡片都共享刚才定义的样式。\n", "    # # 由于外层容器用的是 flex 布局且 justifyContent: space-around，3 个卡片会横排且间距均匀。\n", "    cards = [\n", "        html.Div([\n", "            html.H4(\"总销量\"),\n", "            html.H2(f\"{total_sales}\")\n", "        ], style=card_style),\n", "\n", "        html.Div([\n", "            html.H4(\"产品种类\"),\n", "            html.H2(f\"{product_count}\")\n", "        ], style=card_style),\n", "\n", "        html.Div([\n", "            html.H4(\"平均销量\"),\n", "            html.H2(f\"{avg_sales}\")\n", "        ], style=card_style)\n", "    ]\n", "\n", "    # 图表\n", "    bar_fig = px.bar(filtered, x='Product', y='Sales', title=f\"{city} 产品销量柱状图\")\n", "    pie_fig = px.pie(filtered, names='Product', values='Sales', title=f\"{city} 产品销量占比饼图\")\n", "\n", "    return cards, bar_fig, pie_fig\n", "\n", "# 启动应用\n", "if __name__ == '__main__':\n", "    app.run_server(debug=True)\n"]}, {"cell_type": "markdown", "id": "7415bed1-1b0b-4127-9904-e7b61573dfec", "metadata": {}, "source": ["## 实时刷新数据大屏"]}, {"cell_type": "markdown", "id": "c2ddae32-f08b-414f-a0b7-d6eadc74d59b", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["### 模拟数据"]}, {"cell_type": "code", "execution_count": 1, "id": "aff27aa0-a1f1-4612-9bd6-fec0e3b911c6", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8050/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x7f7dc366cf28>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import dash\n", "from dash import html, dcc, Output, Input\n", "import plotly.express as px\n", "import random\n", "from collections import deque\n", "import pandas as pd\n", "\n", "# 初始化数据（maxlen：最多保存20个点）\n", "# 用于存储滑动窗口的数据（避免内存爆炸）\n", "X = deque(maxlen=20)\n", "Y = deque(maxlen=20)\n", "X.append(0)\n", "Y.append(random.randint(0, 100))\n", "\n", "app = dash.Dash(__name__)\n", "\n", "app.layout = html.Div([\n", "    html.H2(\"模拟实时数据展示\", style={'textAlign': 'center'}),\n", "    \n", "    dcc.Graph(id='live-update-graph'),\n", "\n", "    # 用于定时更新（如1秒刷新一次）\n", "    dcc.<PERSON>(\n", "        id='interval-component',\n", "        interval=1000,  # 每秒更新一次\n", "        n_intervals=0\n", "    )\n", "])\n", "\n", "# 每次触发时，n_intervals自增，可驱动图表刷新\n", "@app.callback(\n", "    Output('live-update-graph', 'figure'),\n", "    Input('interval-component', 'n_intervals')\n", ")\n", "def update_graph(n):\n", "    X.append(X[-1] + 1)\n", "    Y.append(Y[-1] + random.randint(-10, 10))\n", "\n", "    data = pd.DataFrame({'x': X, 'y': Y})\n", "\n", "    fig = px.scatter(\n", "        data, \n", "        x='x', \n", "        y='y', \n", "        title='模拟数据散点图',\n", "        labels={'x': '时间', 'y': '数值'},\n", "    )\n", "\n", "    # uirevision='static'：保持缩放、拖动状态不会因为刷新而重置\n", "    fig.update_layout(title='实时数据折线图', \n", "                      xaxis_title='时间步', \n", "                      yaxis_title='值', uirevision='static')\n", "\n", "    return fig\n", "\n", "if __name__ == '__main__':\n", "    app.run_server(debug=True)\n"]}, {"cell_type": "markdown", "id": "f8f2694f-d18e-4b80-98f8-34f9806159fb", "metadata": {}, "source": ["### 通过API获取数据"]}, {"cell_type": "code", "execution_count": 3, "id": "e93ff07f-8a3d-4118-a4e2-f750871d857c", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <iframe\n", "            width=\"100%\"\n", "            height=\"650\"\n", "            src=\"http://127.0.0.1:8051/\"\n", "            frameborder=\"0\"\n", "            allowfullscreen\n", "            \n", "        ></iframe>\n", "        "], "text/plain": ["<IPython.lib.display.IFrame at 0x1d7ff7f6080>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import dash\n", "from dash import dcc, html, Input, Output\n", "import plotly.graph_objs as go\n", "import plotly.express as px\n", "import requests\n", "from datetime import datetime\n", "\n", "app = dash.Dash(__name__)\n", "\n", "# 初始化数据存储\n", "price_history = {'time': [], 'price': []}\n", "\n", "app.layout = html.Div([\n", "    html.H1(\"比特币实时价格\", style={'textAlign': 'center'}),\n", "    dcc.Graph(id='price-chart'),\n", "    dcc.<PERSON>(\n", "        id='refresh', \n", "        interval=10*1000)  # 10秒刷新\n", "])\n", "\n", "@app.callback(\n", "    Output('price-chart', 'figure'),\n", "    Input('refresh', 'n_intervals')\n", ")\n", "def update_chart(n):\n", "    # 获取实时数据\n", "    try:\n", "        response = requests.get(\n", "            \"https://api.coingecko.com/api/v3/simple/price\",\n", "            params={'ids': 'bitcoin', 'vs_currencies': 'usd'}\n", "        )\n", "        price = response.json()['bitcoin']['usd']\n", "    except:\n", "        price = price_history['price'][-1] if price_history['price'] else 0\n", "\n", "    # 更新数据记录（保留最近30个点）\n", "    price_history['time'].append(datetime.now().strftime(\"%H:%M:%S\"))\n", "    price_history['price'].append(price)\n", "    if len(price_history['time']) > 30:\n", "        price_history['time'] = price_history['time'][-30:]\n", "        price_history['price'] = price_history['price'][-30:]\n", "\n", "    # 构建图表\n", "    fig = px.line(\n", "        price_history,\n", "        x='time',\n", "        y='price',\n", "        color_discrete_sequence=['#FF4B4B'],  # 设置线条颜色\n", "        labels={\n", "            'time': '时间',\n", "            'price': '价格 (USD)'\n", "        }\n", "    )\n", "\n", "    # 更新布局保持原样式\n", "    fig.update_layout(\n", "        plot_bgcolor='#1E1E1E',\n", "        paper_bgcolor='#1E1E1E',\n", "        xaxis=dict(color='white'),\n", "        yaxis=dict(color='white'),\n", "        margin=dict(t=40),\n", "        showlegend=False  # 隐藏自动生成的图例\n", "    )\n", "    \n", "    # 设置线条宽度（需单独更新）\n", "    fig.update_traces(line=dict(width=2))\n", "    \n", "    return fig\n", "\n", "if __name__ == '__main__':\n", "    # 默认端口：8050\n", "    app.run_server(debug=True, port=8051)"]}, {"cell_type": "code", "execution_count": null, "id": "08643807-29ad-422d-8139-b9ff4e515966", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}