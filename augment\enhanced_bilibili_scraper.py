"""
增强版B站数据爬虫
收集更多样化的数据用于分析
"""

import requests
import json
import pandas as pd
import time
import random
from datetime import datetime, timedelta
import numpy as np


class EnhancedBilibiliScraper:
    """
    增强版B站数据爬虫类
    """
    
    def __init__(self):
        """初始化爬虫"""
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://www.bilibili.com/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # API接口
        self.api_urls = {
            'popular': 'https://api.bilibili.com/x/web-interface/popular',
            'ranking': 'https://api.bilibili.com/x/web-interface/ranking/v2',
            'search': 'https://api.bilibili.com/x/web-interface/search/all/v2',
            'video_info': 'https://api.bilibili.com/x/web-interface/view',
            'newlist': 'https://api.bilibili.com/x/web-interface/newlist',
        }
    
    def get_popular_videos(self, pages=3, page_size=20):
        """获取多页热门视频"""
        all_videos = []
        
        for page in range(1, pages + 1):
            try:
                print(f"获取热门视频第 {page} 页...")
                params = {'ps': page_size, 'pn': page}
                
                response = self.session.get(self.api_urls['popular'], params=params, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                if data['code'] == 0:
                    videos = data['data']['list']
                    all_videos.extend(videos)
                    print(f"第 {page} 页获取 {len(videos)} 条数据")
                else:
                    print(f"第 {page} 页获取失败: {data['message']}")
                
                self.delay_request(1, 3)
                
            except Exception as e:
                print(f"获取第 {page} 页失败: {e}")
                continue
        
        return all_videos
    
    def get_ranking_by_categories(self):
        """获取各分区排行榜数据"""
        # B站主要分区
        categories = [
            (0, "全站"),
            (1, "动画"), (13, "番剧"), (167, "国创"), (3, "音乐"), (129, "舞蹈"),
            (4, "游戏"), (17, "单机游戏"), (171, "电子竞技"), (172, "手机游戏"),
            (36, "科技"), (188, "科普"), (234, "极客DIY"),
            (160, "生活"), (138, "搞笑"), (21, "日常"), (76, "美妆护肤"),
            (211, "美食"), (76, "动物圈"), (75, "体育"), (163, "体育竞技"),
            (5, "娱乐"), (71, "综艺"), (241, "娱乐杂谈"),
            (181, "影视"), (182, "影视杂谈"), (183, "预告·资讯"),
            (177, "纪录片"), (23, "电影"), (11, "电视剧"),
            (119, "鬼畜"), (155, "时尚"), (202, "资讯"), (124, "社科法律心理")
        ]
        
        all_videos = []
        
        for tid, tname in categories:
            try:
                print(f"获取 {tname} 分区排行榜...")
                
                # 获取三日榜
                params = {'rid': tid, 'day': 3, 'type': 1, 'arc_type': 0}
                response = self.session.get(self.api_urls['ranking'], params=params, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                if data['code'] == 0:
                    videos = data['data']['list']
                    all_videos.extend(videos)
                    print(f"{tname} 分区获取 {len(videos)} 条数据")
                else:
                    print(f"{tname} 分区获取失败: {data['message']}")
                
                self.delay_request(0.5, 2)
                
            except Exception as e:
                print(f"获取 {tname} 分区失败: {e}")
                continue
        
        return all_videos
    
    def search_trending_keywords(self):
        """搜索热门关键词"""
        keywords = [
            # 科技类
            "人工智能", "AI", "ChatGPT", "机器学习", "深度学习", "Python", "编程", "算法",
            "数据分析", "大数据", "云计算", "区块链", "元宇宙", "VR", "AR",
            
            # 生活类
            "美食", "旅行", "健身", "减肥", "护肤", "化妆", "穿搭", "家居",
            "宠物", "猫", "狗", "摄影", "手工", "DIY",
            
            # 娱乐类
            "游戏", "电影", "电视剧", "动漫", "音乐", "舞蹈", "综艺", "明星",
            "搞笑", "段子", "鬼畜", "二次元", "cosplay",
            
            # 学习类
            "教程", "学习", "考试", "英语", "数学", "物理", "化学", "历史",
            "地理", "生物", "考研", "高考", "留学",
            
            # 热点类
            "新闻", "时事", "科普", "解说", "测评", "开箱", "评测"
        ]
        
        all_videos = []
        
        for keyword in keywords[:20]:  # 限制搜索数量
            try:
                print(f"搜索关键词: {keyword}")
                
                params = {
                    'keyword': keyword,
                    'page': 1,
                    'page_size': 15,
                    'order': 'totalrank',
                    'duration': 0,
                    'tids': 0
                }
                
                response = self.session.get(self.api_urls['search'], params=params, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                if data['code'] == 0 and 'result' in data['data']:
                    videos = data['data']['result'].get('video', [])
                    all_videos.extend(videos)
                    print(f"搜索 {keyword}: {len(videos)} 条数据")
                else:
                    print(f"搜索 {keyword} 失败")
                
                self.delay_request(1, 2)
                
            except Exception as e:
                print(f"搜索 {keyword} 失败: {e}")
                continue
        
        return all_videos
    
    def get_new_videos(self):
        """获取最新投稿视频"""
        try:
            print("获取最新投稿视频...")
            
            params = {'ps': 50, 'rid': 0}
            response = self.session.get(self.api_urls['newlist'], params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            if data['code'] == 0:
                videos = data['data']['archives']
                print(f"获取最新视频: {len(videos)} 条")
                return videos
            else:
                print(f"获取最新视频失败: {data['message']}")
                return []
                
        except Exception as e:
            print(f"获取最新视频失败: {e}")
            return []
    
    def extract_video_data(self, video_list):
        """提取视频数据"""
        extracted_data = []
        
        for video in video_list:
            try:
                # 处理不同来源的数据格式
                if 'stat' in video:
                    # 标准格式
                    stats = video['stat']
                else:
                    # 搜索结果格式
                    stats = {
                        'view': video.get('play', 0),
                        'danmaku': video.get('video_review', 0),
                        'reply': 0,
                        'favorite': video.get('favorites', 0),
                        'coin': 0,
                        'share': 0,
                        'like': 0
                    }
                
                video_data = {
                    'bvid': video.get('bvid', ''),
                    'aid': video.get('aid', 0),
                    'title': video.get('title', ''),
                    'desc': video.get('desc', video.get('description', '')),
                    'duration': self._parse_duration(video.get('duration', 0)),
                    'pubdate': video.get('pubdate', video.get('created', 0)),
                    'view': stats.get('view', 0),
                    'danmaku': stats.get('danmaku', 0),
                    'reply': stats.get('reply', 0),
                    'favorite': stats.get('favorite', 0),
                    'coin': stats.get('coin', 0),
                    'share': stats.get('share', 0),
                    'like': stats.get('like', 0),
                    'owner_name': video.get('owner', {}).get('name', video.get('author', '')),
                    'owner_mid': video.get('owner', {}).get('mid', 0),
                    'tname': video.get('tname', ''),
                    'tid': video.get('tid', 0),
                    'pic': video.get('pic', ''),
                }
                
                extracted_data.append(video_data)
                
            except Exception as e:
                print(f"提取视频数据失败: {e}")
                continue
        
        return pd.DataFrame(extracted_data)
    
    def _parse_duration(self, duration):
        """解析视频时长"""
        if isinstance(duration, str):
            # 格式如 "1:23" 或 "12:34"
            try:
                parts = duration.split(':')
                if len(parts) == 2:
                    return int(parts[0]) * 60 + int(parts[1])
                elif len(parts) == 3:
                    return int(parts[0]) * 3600 + int(parts[1]) * 60 + int(parts[2])
            except:
                return 0
        return int(duration) if duration else 0
    
    def delay_request(self, min_delay=0.5, max_delay=2.0):
        """请求延迟"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    def create_enhanced_sample_data(self, base_count=500):
        """创建增强的示例数据"""
        print(f"创建 {base_count} 条增强示例数据...")
        
        # 更真实的分区数据
        categories_with_weights = [
            ("生活", 160, 0.15), ("游戏", 4, 0.12), ("娱乐", 5, 0.10),
            ("科技", 36, 0.08), ("音乐", 3, 0.08), ("影视", 181, 0.08),
            ("动画", 1, 0.07), ("美食", 211, 0.06), ("搞笑", 138, 0.06),
            ("舞蹈", 129, 0.05), ("体育", 75, 0.04), ("时尚", 155, 0.04),
            ("电影", 23, 0.03), ("电视剧", 11, 0.03), ("纪录片", 177, 0.03),
            ("国创", 167, 0.02), ("鬼畜", 119, 0.02), ("番剧", 13, 0.02),
            ("单机游戏", 17, 0.02), ("手机游戏", 172, 0.02)
        ]
        
        # 热门关键词用于标题生成
        title_keywords = [
            "教程", "测评", "开箱", "解说", "攻略", "技巧", "分享", "体验",
            "推荐", "盘点", "合集", "日常", "vlog", "挑战", "实验", "制作",
            "学习", "科普", "分析", "评测", "对比", "揭秘", "探索", "发现"
        ]
        
        sample_data = []
        
        for i in range(base_count):
            # 根据权重选择分区
            rand = random.random()
            cumulative = 0
            selected_category = categories_with_weights[0]
            
            for category, tid, weight in categories_with_weights:
                cumulative += weight
                if rand <= cumulative:
                    selected_category = (category, tid, weight)
                    break
            
            category_name, category_id, _ = selected_category
            
            # 生成更真实的播放量分布
            # 80%普通视频，15%热门视频，5%爆款视频
            rand_view = random.random()
            if rand_view < 0.8:  # 普通视频
                view_base = random.randint(1000, 50000)
            elif rand_view < 0.95:  # 热门视频
                view_base = random.randint(50000, 500000)
            else:  # 爆款视频
                view_base = random.randint(500000, 5000000)
            
            # 基于播放量计算其他指标（更真实的比例）
            like_rate = random.uniform(0.02, 0.15)
            coin_rate = random.uniform(0.005, 0.05)
            favorite_rate = random.uniform(0.01, 0.08)
            share_rate = random.uniform(0.001, 0.02)
            reply_rate = random.uniform(0.001, 0.05)
            danmaku_rate = random.uniform(0.001, 0.1)
            
            like = int(view_base * like_rate)
            coin = int(view_base * coin_rate)
            favorite = int(view_base * favorite_rate)
            share = int(view_base * share_rate)
            reply = int(view_base * reply_rate)
            danmaku = int(view_base * danmaku_rate)
            
            # 生成更真实的时长分布
            if category_name in ["音乐", "舞蹈"]:
                duration = random.randint(60, 300)  # 1-5分钟
            elif category_name in ["电影", "电视剧", "纪录片"]:
                duration = random.randint(1800, 7200)  # 30分钟-2小时
            elif category_name in ["游戏", "科技", "教程"]:
                duration = random.randint(300, 1800)  # 5-30分钟
            else:
                duration = random.randint(60, 1200)  # 1-20分钟
            
            # 生成标题
            keyword = random.choice(title_keywords)
            title = f"【{category_name}】{keyword}：{random.choice(['超详细', '最新', '完整版', '高质量', '实用', '有趣的'])}{category_name}内容_{i+1}"
            
            # 生成发布时间（最近一年内）
            days_ago = random.randint(1, 365)
            pubdate = int((datetime.now() - timedelta(days=days_ago)).timestamp())
            
            video_data = {
                'bvid': f'BV{random.randint(100000000000, 999999999999)}',
                'aid': random.randint(100000000, 999999999),
                'title': title,
                'desc': f'这是一个关于{category_name}的{keyword}视频，内容丰富有趣，欢迎观看！',
                'duration': duration,
                'pubdate': pubdate,
                'view': view_base,
                'danmaku': danmaku,
                'reply': reply,
                'favorite': favorite,
                'coin': coin,
                'share': share,
                'like': like,
                'owner_name': f'{category_name}UP主_{random.randint(1, 1000)}',
                'owner_mid': random.randint(1000000, 99999999),
                'tname': category_name,
                'tid': category_id,
                'pic': f'http://i{random.randint(0, 2)}.hdslb.com/bfs/archive/sample_{i}.jpg',
            }
            
            sample_data.append(video_data)
        
        return pd.DataFrame(sample_data)


def collect_comprehensive_bilibili_data():
    """收集全面的B站数据"""
    scraper = EnhancedBilibiliScraper()
    all_videos = []
    
    print("=== 开始收集全面的B站数据 ===")
    
    try:
        # 1. 获取热门视频（多页）
        print("\n1. 获取热门视频...")
        popular_videos = scraper.get_popular_videos(pages=3, page_size=20)
        if popular_videos:
            all_videos.extend(popular_videos)
            print(f"热门视频总计: {len(popular_videos)} 条")
        
        # 2. 获取各分区排行榜
        print("\n2. 获取各分区排行榜...")
        ranking_videos = scraper.get_ranking_by_categories()
        if ranking_videos:
            all_videos.extend(ranking_videos)
            print(f"分区排行榜总计: {len(ranking_videos)} 条")
        
        # 3. 搜索热门关键词
        print("\n3. 搜索热门关键词...")
        search_videos = scraper.search_trending_keywords()
        if search_videos:
            all_videos.extend(search_videos)
            print(f"搜索结果总计: {len(search_videos)} 条")
        
        # 4. 获取最新视频
        print("\n4. 获取最新视频...")
        new_videos = scraper.get_new_videos()
        if new_videos:
            all_videos.extend(new_videos)
            print(f"最新视频总计: {len(new_videos)} 条")
        
        print(f"\n真实数据收集完成，共 {len(all_videos)} 条")
        
    except Exception as e:
        print(f"真实数据收集出现问题: {e}")
        all_videos = []
    
    # 5. 提取真实数据
    real_df = pd.DataFrame()
    if all_videos:
        print("\n5. 提取真实数据...")
        real_df = scraper.extract_video_data(all_videos)
        # 去重
        real_df = real_df.drop_duplicates(subset=['bvid'], keep='first')
        print(f"提取并去重后的真实数据: {len(real_df)} 条")
    
    # 6. 创建增强示例数据
    print("\n6. 创建增强示例数据...")
    sample_df = scraper.create_enhanced_sample_data(base_count=800)
    print(f"创建示例数据: {len(sample_df)} 条")
    
    # 7. 合并数据
    if not real_df.empty:
        final_df = pd.concat([real_df, sample_df], ignore_index=True)
    else:
        final_df = sample_df
    
    # 最终去重
    final_df = final_df.drop_duplicates(subset=['bvid'], keep='first')
    
    print(f"\n=== 数据收集完成 ===")
    print(f"最终数据集: {len(final_df)} 条记录")
    
    # 保存数据
    final_df.to_csv('bilibili_comprehensive_data.csv', index=False, encoding='utf-8-sig')
    print("数据已保存到: bilibili_comprehensive_data.csv")
    
    return final_df


if __name__ == "__main__":
    try:
        data = collect_comprehensive_bilibili_data()
        
        print("\n=== 数据概览 ===")
        print(f"总记录数: {len(data)}")
        print(f"字段数: {len(data.columns)}")
        
        # 显示分区分布
        if 'tname' in data.columns:
            print("\n分区分布 (前15名):")
            category_counts = data['tname'].value_counts().head(15)
            for category, count in category_counts.items():
                print(f"  {category}: {count} 条")
        
        # 显示数值统计
        numeric_cols = ['view', 'like', 'coin', 'favorite', 'duration']
        print("\n数值字段统计:")
        for col in numeric_cols:
            if col in data.columns:
                print(f"  {col}: 平均={data[col].mean():.0f}, 最大={data[col].max():.0f}")
        
        print("\n数据收集完成！可以进行预处理和可视化分析。")
        
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
