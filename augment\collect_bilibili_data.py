"""
B站数据采集主程序
获取多种类型的视频数据用于分析
"""

import time
import pandas as pd
from bilibili_scraper import BilibiliScraper
from data_processor import DataProcessor


def collect_comprehensive_data():
    """
    收集全面的B站数据
    包括热门视频、排行榜、不同分区数据等
    """
    scraper = BilibiliScraper()
    all_videos = []
    
    print("=== 开始收集B站数据 ===")
    
    # 1. 获取热门视频
    print("\n1. 获取热门视频...")
    popular_videos = scraper.get_popular_videos(20)
    if popular_videos:
        all_videos.extend(popular_videos)
        print(f"获取热门视频: {len(popular_videos)} 条")
    
    scraper.delay_request()
    
    # 2. 获取全站排行榜
    print("\n2. 获取全站排行榜...")
    ranking_videos = scraper.get_ranking_videos(tid=0, day=3)  # 三日榜
    if ranking_videos:
        all_videos.extend(ranking_videos)
        print(f"获取排行榜视频: {len(ranking_videos)} 条")
    
    scraper.delay_request()
    
    # 3. 获取不同分区的排行榜数据
    categories = [
        (1, "动画"),
        (3, "音乐"),
        (4, "游戏"),
        (36, "科技"),
        (188, "科普"),
        (160, "生活"),
        (211, "美食"),
        (217, "动物圈"),
        (119, "鬼畜"),
        (155, "时尚"),
        (5, "娱乐"),
        (181, "影视"),
        (177, "纪录片"),
        (23, "电影"),
        (11, "电视剧"),
        (129, "舞蹈"),
        (168, "国创"),
        (167, "国产动画"),
        (13, "番剧"),
        (165, "广告"),
        (138, "搞笑"),
        (75, "体育"),
        (76, "体育竞技"),
        (17, "单机游戏"),
        (171, "电子竞技"),
        (172, "手机游戏"),
        (65, "网络游戏"),
        (121, "GMV"),
        (136, "音乐现场"),
        (130, "音乐综合"),
        (59, "演奏"),
        (193, "MV"),
        (29, "音乐选集"),
        (28, "原创音乐"),
        (31, "翻唱"),
        (30, "VOCALOID·UTAU"),
        (194, "电音"),
        (243, "乐评盘点"),
        (244, "音乐教学"),
    ]
    
    print("\n3. 获取各分区数据...")
    for tid, tname in categories[:10]:  # 限制获取前10个分区避免请求过多
        try:
            print(f"正在获取 {tname} 分区数据...")
            category_videos = scraper.get_ranking_videos(tid=tid, day=3)
            if category_videos:
                all_videos.extend(category_videos)
                print(f"获取 {tname} 分区视频: {len(category_videos)} 条")
            scraper.delay_request(1, 3)  # 增加延迟避免被限制
        except Exception as e:
            print(f"获取 {tname} 分区失败: {e}")
            continue
    
    # 4. 搜索热门关键词
    print("\n4. 搜索热门关键词...")
    keywords = ["数据可视化", "Python", "编程", "AI", "人工智能", "机器学习", "科技", "教程"]
    for keyword in keywords[:5]:  # 限制搜索数量
        try:
            print(f"搜索关键词: {keyword}")
            search_results = scraper.search_videos(keyword, page=1, page_size=10)
            if search_results:
                all_videos.extend(search_results)
                print(f"搜索 {keyword}: {len(search_results)} 条")
            scraper.delay_request(1, 2)
        except Exception as e:
            print(f"搜索 {keyword} 失败: {e}")
            continue
    
    print(f"\n=== 数据收集完成，共获取 {len(all_videos)} 条原始数据 ===")
    
    # 5. 提取和标准化数据
    print("\n5. 提取和标准化数据...")
    df = scraper.extract_video_data(all_videos)
    
    # 去重（基于bvid）
    initial_count = len(df)
    df = df.drop_duplicates(subset=['bvid'], keep='first')
    print(f"去重后剩余: {len(df)} 条数据 (去除 {initial_count - len(df)} 条重复)")
    
    # 保存原始数据
    df.to_csv('bilibili_raw_data.csv', index=False, encoding='utf-8-sig')
    print("原始数据已保存到: bilibili_raw_data.csv")
    
    return df


def process_data():
    """
    处理收集到的数据
    """
    print("\n=== 开始数据预处理 ===")
    
    processor = DataProcessor()
    
    # 处理数据
    processed_df = processor.process_pipeline(
        'bilibili_raw_data.csv',
        'bilibili_processed_data.csv'
    )
    
    if not processed_df.empty:
        print("\n=== 数据摘要 ===")
        summary = processor.get_data_summary(processed_df)
        print(f"总记录数: {summary['total_records']}")
        print(f"总列数: {summary['total_columns']}")
        print(f"缺失值总数: {summary['missing_values']}")
        
        # 显示基本统计信息
        print("\n=== 数值字段统计 ===")
        numeric_cols = ['view', 'like', 'coin', 'favorite', 'share', 'reply', 'duration']
        for col in numeric_cols:
            if col in processed_df.columns:
                print(f"{col}: 平均值={processed_df[col].mean():.0f}, 最大值={processed_df[col].max():.0f}")
        
        # 显示分区分布
        print("\n=== 分区分布 ===")
        if 'tname' in processed_df.columns:
            category_counts = processed_df['tname'].value_counts().head(10)
            for category, count in category_counts.items():
                print(f"{category}: {count} 条")
    
    return processed_df


if __name__ == "__main__":
    try:
        # 收集数据
        raw_data = collect_comprehensive_data()
        
        # 处理数据
        processed_data = process_data()
        
        print(f"\n=== 数据收集和处理完成 ===")
        print(f"最终数据集包含 {len(processed_data)} 条记录")
        print("可以开始进行数据可视化分析了！")
        
    except Exception as e:
        print(f"数据收集过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
