#!/usr/bin/env python
# coding: utf-8

# ## 爬虫介绍
# * 使用`requests`库

# ### 1. 获取学院的首页内容
# * 单个网页
# * 简单网站，不需要设置请求头

# In[1]:


import requests
url = 'http://www.sbm.shisu.edu.cn/'
req = requests.get(url)
content = req.text
print(content[:1000])


# In[2]:


# 解决中文编码问题
print('website encoding: ', req.encoding)
req.encoding ='utf-8' # 人工纠正为utf-8，对中文进行编码
content = req.text
print(content[:1000])


# ### 2. 抓取京东某个网页的内容
# * 设置请求头(Headers)

# In[3]:


url = "https://item.jd.com/497227.html" 
req = requests.get(url, timeout=10)
content1 = req.text
headers = req.request.headers
print('content:',content1)  # 不设置请求头时，请求成功，但没有返回页面html内容
print('headers: ',headers)  # 当前的header不是真实的浏览器，而是python的requests爬虫程序


# In[4]:


# 设置请求头
url = "https://item.jd.com/497227.html" #空气净化器
headers = {'User-Agent':'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36'}
req = requests.get(url, headers=headers) # add header information for user-agent
print(req.status_code)
req.encoding = 'utf-8'
print(req.text[:1000])
print(req.request.headers)


# ### 3. BeautifulSoup解析

# In[5]:


from bs4 import BeautifulSoup
content = """
<html><head><title>The Dormouse's story</title></head>
<body>
<p class="title" name="dromouse"><b>The Dormouse's story</b></p>
<p class="story">Once upon a time there were three little sisters; and their names were
<a href="http://example.com/elsie" class="sister" id="link1"><!-- Elsie --></a>,
<a href="http://example.com/lacie" class="sister" id="link2">Lacie</a> and
<a href="http://example.com/tillie" class="sister" id="link3">Tillie</a>;
and they lived at the bottom of a well.</p>
<p class="story">...</p>
"""
soup = BeautifulSoup(content,"html.parser")
# 格式化输出
print(soup.prettify())


# #### 3.1 Tag对象

# In[81]:


# 获取标签
print(soup.title)
print(soup.a)
print(type(soup.title))


# In[85]:


# Tag的两个重要属性，name和attrs
tag = soup.a
tag.name


# In[86]:


tag.attrs


# In[87]:


# 获取tag中的某个属性，类比字典的使用方法
tag['href']


# #### 3.2 NavigableString对象

# In[89]:


# 获取标签内部的文字
title = soup.title.string
print(title)
print(type(title))


# #### 3.3 搜索文档树

# In[92]:


from IPython.display import HTML
content = """
<html><head><title>The Dormouse's story</title></head>
<body>
<p class="title" name="dromouse"><b>The Dormouse's story</b></p>
<p class="story">Once upon a time there were three little sisters; and their names were
<a href="http://example.com/elsie" class="sister" id="link1"><!-- Elsie --></a>,
<a href="http://example.com/lacie" class="sister" id="link2">Lacie</a> and
<a href="http://example.com/tillie" class="sister" id="link3">Tillie</a>;
and they lived at the bottom of a well.</p>
<p class="story">...</p>
"""
output = HTML(content)
output


# In[93]:


soup = BeautifulSoup(content)
soup.find_all('a')


# In[94]:


soup.find_all(['a','b'])  # 对于列表，任一匹配均可


# In[95]:


# 根据标签的属性查询
soup.find_all(id ='link2') # 所有id为link2的Tag


# In[96]:


soup.find_all(id=True)  # 所有含有id属性的标签


# In[98]:


soup.find_all(attrs={"class":"story"})


# In[102]:


soup.find_all(class_ = 'title')   # 通过class，需要用class_，因为class为系统保留字


# In[97]:


# 同时指定tag及其属性
soup.find_all('a',id='link1')


# In[103]:


for each in soup.find_all('a',class_='sister'):
    print(each)
    print(each.string)


# In[105]:


# 同时指定tag及其属性，几种方式均可
print(soup.find_all(name = 'a',attrs ={'id':'link1'}))
print(soup.find_all('a',attrs ={'id':'link1'}))
print(soup.find_all('a',id = 'link1'))


# ### 4. 简单实例
# * 爬取学院网站的新闻公告内容

# In[6]:


import requests
from bs4 import BeautifulSoup


# In[7]:


# 获取网页内容
url = 'http://www.sbm.shisu.edu.cn/7290/list.htm'
req = requests.get(url)
req.encoding = 'utf-8'
content = req.text
print(content)


# In[8]:


# 解析
soup = BeautifulSoup(content, 'html.parser')
all_news = soup.find('div', id='wp_news_w24')
for news in all_news.find_all('li'):
    news_title = news.find('span', class_='news_title').string
    date = news.find('span', class_ = 'news_meta').string
    print(date,news_title)


# In[10]:


# 转成DataFrame，并存入csv文件
import pandas as pd
# 创建空的数据框
df = pd.DataFrame({'date': [],
                'news_title': []
               })
for news in all_news.find_all('li'):
    news_title = news.find('span', class_='news_title').string  # 从html中获取新闻标题
    date = news.find('span', class_ = 'news_meta').string  # 从html中获取对应的新闻时间
    record = pd.DataFrame({'date': [date],
                'news_title': [news_title]
               })   # 包装成一个数据框
    df = pd.concat([df, record], ignore_index=True)  # 循环追加每一条新闻的内容到数据框
df = df.reset_index()   # 重新索引
df = df[['date','news_title']]
df.to_csv('news.csv',encoding="utf_8_sig")
df.head()


# #### 4.1 翻页

# In[133]:


import requests
from bs4 import BeautifulSoup
# 方法一
# 观察网站，发现一共有11页，通过不同页码之间的url规律构建url池，遍历抓取每一页
for page_no in range(1,12):
    print("抓取第 ",page_no," 页的新闻内容...")
    # 获取网页内容
    url = 'http://www.sbm.shisu.edu.cn/7290/list' + str(page_no) + '.htm'
    req = requests.get(url)
    req.encoding = 'utf-8'
    content = req.text
    
    # 解析网页内容
    soup = BeautifulSoup(content, 'html.parser')
    all_news = soup.find('div', id='wp_news_w24')
    for news in all_news.find_all('li'):
        news_title = news.find('span', class_='news_title').string   # 新闻标题
        date = news.find('span', class_ = 'news_meta').string  # 发布日期
        print(date,news_title)


# In[134]:


import requests
from bs4 import BeautifulSoup
# 方法二
# 从当前网页中找到下一页的url
href = '/7290/list.htm'   # 起始页url后缀
count = 0 # 用于记录当前的页码
while 'htm' in href:
    count += 1
    print("抓取第 ",count," 页的新闻内容...")
    # 获取网页内容
    url = 'http://www.sbm.shisu.edu.cn' + href   # 每一次爬取时完整的url
    req = requests.get(url)
    req.encoding = 'utf-8'
    content = req.text
    
    # 解析网页内容
    soup = BeautifulSoup(content, 'html.parser')
    all_news = soup.find('div', id='wp_news_w24')
    for news in all_news.find_all('li'):
        news_title = news.find('span', class_='news_title').string   # 新闻标题
        date = news.find('span', class_ = 'news_meta').string  # 发布日期
        print(date,news_title)
    
    # 获取下一页的url，更新herf变量
    next_url = soup.find('a',class_='next')
    href = next_url['href']


# In[136]:


# 将解析后的内容存储到csv文件中

import requests
from bs4 import BeautifulSoup
import pandas as pd

saved_data = {'date':[],'news_title':[]}  # 将所有内容存储到一个字典中
# 方法二
# 从当前网页中找到下一页的url
href = '/7290/list.htm'   # 起始页url后缀
count = 0 # 用于记录当前的页码
while 'htm' in href:
    count += 1
    print("抓取第 ",count," 页的新闻内容...")
    # 获取网页内容
    url = 'http://www.sbm.shisu.edu.cn' + href   # 每一次爬取时完整的url
    req = requests.get(url)
    req.encoding = 'utf-8'
    content = req.text
    
    # 解析网页内容
    soup = BeautifulSoup(content, 'html.parser')
    all_news = soup.find('div', id='wp_news_w24')
    for news in all_news.find_all('li'):
        news_title = news.find('span', class_='news_title').string   # 新闻标题
        date = news.find('span', class_ = 'news_meta').string  # 发布日期
        # print(date,news_title)
        # 更新字典
        saved_data['date'].append(date)
        saved_data['news_title'].append(news_title)
        
    
    # 获取下一页的url，更新herf变量
    next_url = soup.find('a',class_='next')
    href = next_url['href']

# 解析完成，将所有内容存入csv
df = pd.DataFrame(saved_data)
df.to_csv('news.csv',encoding="utf_8_sig")
df

