{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Lecture 3: 序列和数据框处理 `Pandas`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 关于Pandas\n", "* 主要的数据结构有三种，Series, DataFrame, Panel"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 创建Series"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1）Data是多维数组"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a    1.020899\n", "b   -1.408410\n", "c   -1.090347\n", "d    0.696804\n", "e    0.168239\n", "dtype: float64\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "s = pd.Series(np.random.randn(5),index = ['a','b','c','d','e'])  # 索引index长度必须与data长度一致\n", "print(s)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0   -1.619622\n", "1    0.499349\n", "2    0.453698\n", "3    1.770802\n", "4    0.097577\n", "dtype: float64\n"]}], "source": ["t = pd.Series(np.random.randn(5)) # 没有指定index参数时，默认创建数值型索引\n", "print(t)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0     Nanjing\n", "1    Hangzhou\n", "2      Fuzhou\n", "dtype: object\n"]}], "source": ["n = pd.Series(['Nanjing','Hangzhou','Fuzhou'])\n", "print(n)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2）Data是字典"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 未设置index参数时，如果Python版本>=3.6且Pandas版本>=0.23，Series按照字典的插入顺序排列索引；否则按字母顺序排序字典的key列表\n", "d = {'b': 1, 'a': 0, 'c': 2}\n", "d1 = pd.Series(d)\n", "print(d1)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b    1.0\n", "c    2.0\n", "d    <PERSON><PERSON>\n", "a    0.0\n", "dtype: float64\n"]}], "source": ["# 如果设置了index参数，则按索引标签提取data里面的值。使用NaN (Not a Number)表示缺失数据\n", "d = {'b': 1, 'a': 0, 'c': 2}\n", "d2 = pd.Series(d, index=['b', 'c', 'd', 'a'])\n", "print(d2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3）Data是标量值"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["a    5.0\n", "b    5.0\n", "c    5.0\n", "d    5.0\n", "e    5.0\n", "dtype: float64\n"]}], "source": ["# 必须提供索引。Series按索引长度重复该标量值\n", "e = pd.Series(5., index=['a', 'b', 'c', 'd', 'e'])\n", "print(e)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 读取Series数据"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[5. 5. 5. 5. 5.]\n", "Index(['a', 'b', 'c', 'd', 'e'], dtype='object')\n"]}], "source": ["# 使用序列的属性 values 和 index 得到数据值和索引值\n", "import pandas as pd\n", "e = pd.Series(5., index=['a', 'b', 'c', 'd', 'e'])\n", "print(e.values)\n", "print(e.index)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["English\n"]}], "source": ["# 通过索引的方式选择 Series序列对象中的值\n", "s = pd.Series(['Python','SQL','Java','English'], index=['a', 'b', 'c', 'd'])\n", "print(s['d'])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["c       Java\n", "d    English\n", "dtype: object\n"]}], "source": ["# 支持索引切片\n", "print(s['c':'d'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 创建DataFrame"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1) 从字典中创建DataFrame"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>one</th>\n", "      <th>two</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3.0</td>\n", "      <td>8.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.0</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   one  two\n", "0  1.0  6.0\n", "1  2.0  7.0\n", "2  3.0  8.0\n", "3  4.0  9.0"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["d = {'one': [1., 2., 3., 4.],\n", "     'two': [6., 7., 8., 9.]}\n", "df = pd.DataFrame(d)\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2）从Series字典中创建DataFrame"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>one</th>\n", "      <th>two</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>a</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b</th>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c</th>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>d</th>\n", "      <td>NaN</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   one  two\n", "a  1.0  1.0\n", "b  2.0  2.0\n", "c  3.0  3.0\n", "d  NaN  4.0"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["d = {'one': pd.Series([1., 2., 3.], index=['a', 'b', 'c']),\n", "     'two': pd.Series([1., 2., 3., 4.], index=['a', 'b', 'c', 'd'])}\n", "pd.<PERSON><PERSON><PERSON><PERSON>(d)"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>city</th>\n", "      <th>province</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Nanjing</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Hangzhou</td>\n", "      <td>Zhejiang</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Fuzhou</td>\n", "      <td>Fujian</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       city  province\n", "0   Nanjing   Jiangsu\n", "1  Hangzhou  Zhejiang\n", "2    Fuzhou    Fujian"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["s1 = pd.Series({'city': 'Nanjing',\n", "                'province': 'Jiangsu',\n", "                'population': 8.335})\n", "s2 = pd.Series({'city': 'Hangzhou',\n", "                'province': 'Zhejiang',\n", "                'population': 9.468})\n", "s3 = pd.Series({'city': 'Fuzhou',\n", "                'province': 'Fujian',\n", "                'population': 7.64})\n", "df = pd.DataFrame([s1, s2, s3])\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3) 从Numpy二维数据中创建数据框。"]}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>语文</th>\n", "      <th>数学</th>\n", "      <th>英语</th>\n", "      <th>python</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>张三</th>\n", "      <td>112</td>\n", "      <td>40</td>\n", "      <td>125</td>\n", "      <td>128</td>\n", "    </tr>\n", "    <tr>\n", "      <th>李四</th>\n", "      <td>89</td>\n", "      <td>109</td>\n", "      <td>23</td>\n", "      <td>71</td>\n", "    </tr>\n", "    <tr>\n", "      <th>王五</th>\n", "      <td>142</td>\n", "      <td>43</td>\n", "      <td>49</td>\n", "      <td>88</td>\n", "    </tr>\n", "    <tr>\n", "      <th>赵六</th>\n", "      <td>119</td>\n", "      <td>5</td>\n", "      <td>99</td>\n", "      <td>31</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     语文   数学   英语  python\n", "张三  112   40  125     128\n", "<PERSON>四   89  109   23      71\n", "<PERSON>五  142   43   49      88\n", "赵六  119    5   99      31"]}, "execution_count": 120, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "data = np.random.randint(0,150,size=(4,4))\n", "index = ['张三','李四','王五','赵六']\n", "columns = ['语文','数学','英语','python']\n", "df = pd.DataFrame(data=data, index = index, columns = columns)\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 4）指定index和columns"]}, {"cell_type": "code", "execution_count": 52, "metadata": {"scrolled": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>one</th>\n", "      <th>two</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>a</th>\n", "      <td>1.0</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b</th>\n", "      <td>2.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c</th>\n", "      <td>3.0</td>\n", "      <td>8.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>d</th>\n", "      <td>4.0</td>\n", "      <td>9.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   one  two\n", "a  1.0  6.0\n", "b  2.0  7.0\n", "c  3.0  8.0\n", "d  4.0  9.0"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["# 原本数据集不包含index时，指定index将为其赋予新的index\n", "d = {'one': [1., 2., 3., 4.],\n", "     'two': [6., 7., 8., 9.]}\n", "pd.DataFrame(d, index = ['a','b','c','d'])"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>two</th>\n", "      <th>three</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>a</th>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>b</th>\n", "      <td>2.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>c</th>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>e</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   two three\n", "a  1.0   NaN\n", "b  2.0   NaN\n", "c  3.0   NaN\n", "e  NaN   NaN"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["# 原本数据集包含index时，指定index将仅展示所指定的index（行）\n", "d = {'one': pd.Series([1., 2., 3.], index=['a', 'b', 'c']),\n", "     'two': pd.Series([1., 2., 3., 4.], index=['a', 'b', 'c', 'd'])}\n", "df = pd.DataFrame(d, index = ['a','b','c','e'], columns = ['two','three'])\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 5) 访问行和列标签"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['a', 'b', 'c', 'e'], dtype='object')"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["df.index"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['two', 'three'], dtype='object')"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 读取数据：CSV， EXCEL"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Bream</td>\n", "      <td>242.0</td>\n", "      <td>23.2</td>\n", "      <td>25.4</td>\n", "      <td>30.0</td>\n", "      <td>11.5200</td>\n", "      <td>4.0200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bream</td>\n", "      <td>290.0</td>\n", "      <td>24.0</td>\n", "      <td>26.3</td>\n", "      <td>31.2</td>\n", "      <td>12.4800</td>\n", "      <td>4.3056</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bream</td>\n", "      <td>340.0</td>\n", "      <td>23.9</td>\n", "      <td>26.5</td>\n", "      <td>31.1</td>\n", "      <td>12.3778</td>\n", "      <td>4.6961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Bream</td>\n", "      <td>363.0</td>\n", "      <td>26.3</td>\n", "      <td>29.0</td>\n", "      <td>33.5</td>\n", "      <td>12.7300</td>\n", "      <td>4.4555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bream</td>\n", "      <td>430.0</td>\n", "      <td>26.5</td>\n", "      <td>29.0</td>\n", "      <td>34.0</td>\n", "      <td>12.4440</td>\n", "      <td>5.1340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154</th>\n", "      <td>Smelt</td>\n", "      <td>12.2</td>\n", "      <td>11.5</td>\n", "      <td>12.2</td>\n", "      <td>13.4</td>\n", "      <td>2.0904</td>\n", "      <td>1.3936</td>\n", "    </tr>\n", "    <tr>\n", "      <th>155</th>\n", "      <td>Smelt</td>\n", "      <td>13.4</td>\n", "      <td>11.7</td>\n", "      <td>12.4</td>\n", "      <td>13.5</td>\n", "      <td>2.4300</td>\n", "      <td>1.2690</td>\n", "    </tr>\n", "    <tr>\n", "      <th>156</th>\n", "      <td>Smelt</td>\n", "      <td>12.2</td>\n", "      <td>12.1</td>\n", "      <td>13.0</td>\n", "      <td>13.8</td>\n", "      <td>2.2770</td>\n", "      <td>1.2558</td>\n", "    </tr>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>Smelt</td>\n", "      <td>19.7</td>\n", "      <td>13.2</td>\n", "      <td>14.3</td>\n", "      <td>15.2</td>\n", "      <td>2.8728</td>\n", "      <td>2.0672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>158</th>\n", "      <td>Smelt</td>\n", "      <td>19.9</td>\n", "      <td>13.8</td>\n", "      <td>15.0</td>\n", "      <td>16.2</td>\n", "      <td>2.9322</td>\n", "      <td>1.8792</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>159 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    Species  Weight  Length1  Length2  Length3   Height   Width\n", "0     Bream   242.0     23.2     25.4     30.0  11.5200  4.0200\n", "1     Bream   290.0     24.0     26.3     31.2  12.4800  4.3056\n", "2     Bream   340.0     23.9     26.5     31.1  12.3778  4.6961\n", "3     Bream   363.0     26.3     29.0     33.5  12.7300  4.4555\n", "4     Bream   430.0     26.5     29.0     34.0  12.4440  5.1340\n", "..      ...     ...      ...      ...      ...      ...     ...\n", "154   Smelt    12.2     11.5     12.2     13.4   2.0904  1.3936\n", "155   Smelt    13.4     11.7     12.4     13.5   2.4300  1.2690\n", "156   Smelt    12.2     12.1     13.0     13.8   2.2770  1.2558\n", "157   Smelt    19.7     13.2     14.3     15.2   2.8728  2.0672\n", "158   Smelt    19.9     13.8     15.0     16.2   2.9322  1.8792\n", "\n", "[159 rows x 7 columns]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# 从csv文件中读取数据\n", "r = pd.read_csv('data/Fish.csv')\n", "r"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Bream</td>\n", "      <td>242.0</td>\n", "      <td>23.2</td>\n", "      <td>25.4</td>\n", "      <td>30.0</td>\n", "      <td>11.5200</td>\n", "      <td>4.0200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bream</td>\n", "      <td>290.0</td>\n", "      <td>24.0</td>\n", "      <td>26.3</td>\n", "      <td>31.2</td>\n", "      <td>12.4800</td>\n", "      <td>4.3056</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bream</td>\n", "      <td>340.0</td>\n", "      <td>23.9</td>\n", "      <td>26.5</td>\n", "      <td>31.1</td>\n", "      <td>12.3778</td>\n", "      <td>4.6961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Bream</td>\n", "      <td>363.0</td>\n", "      <td>26.3</td>\n", "      <td>29.0</td>\n", "      <td>33.5</td>\n", "      <td>12.7300</td>\n", "      <td>4.4555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bream</td>\n", "      <td>430.0</td>\n", "      <td>26.5</td>\n", "      <td>29.0</td>\n", "      <td>34.0</td>\n", "      <td>12.4440</td>\n", "      <td>5.1340</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Species  Weight  Length1  Length2  Length3   Height   Width\n", "0   Bream   242.0     23.2     25.4     30.0  11.5200  4.0200\n", "1   Bream   290.0     24.0     26.3     31.2  12.4800  4.3056\n", "2   Bream   340.0     23.9     26.5     31.1  12.3778  4.6961\n", "3   Bream   363.0     26.3     29.0     33.5  12.7300  4.4555\n", "4   Bream   430.0     26.5     29.0     34.0  12.4440  5.1340"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# 只输出前几行\n", "r.head()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Bream</td>\n", "      <td>242.0</td>\n", "      <td>23.2</td>\n", "      <td>25.4</td>\n", "      <td>30.0</td>\n", "      <td>11.5200</td>\n", "      <td>4.0200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bream</td>\n", "      <td>290.0</td>\n", "      <td>24.0</td>\n", "      <td>26.3</td>\n", "      <td>31.2</td>\n", "      <td>12.4800</td>\n", "      <td>4.3056</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bream</td>\n", "      <td>340.0</td>\n", "      <td>23.9</td>\n", "      <td>26.5</td>\n", "      <td>31.1</td>\n", "      <td>12.3778</td>\n", "      <td>4.6961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Bream</td>\n", "      <td>363.0</td>\n", "      <td>26.3</td>\n", "      <td>29.0</td>\n", "      <td>33.5</td>\n", "      <td>12.7300</td>\n", "      <td>4.4555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bream</td>\n", "      <td>430.0</td>\n", "      <td>26.5</td>\n", "      <td>29.0</td>\n", "      <td>34.0</td>\n", "      <td>12.4440</td>\n", "      <td>5.1340</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Species  Weight  Length1  Length2  Length3   Height   Width\n", "0   Bream   242.0     23.2     25.4     30.0  11.5200  4.0200\n", "1   Bream   290.0     24.0     26.3     31.2  12.4800  4.3056\n", "2   Bream   340.0     23.9     26.5     31.1  12.3778  4.6961\n", "3   Bream   363.0     26.3     29.0     33.5  12.7300  4.4555\n", "4   Bream   430.0     26.5     29.0     34.0  12.4440  5.1340"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# 从Excel文件中读取数据\n", "r = pd.read_excel('data/fish_new.xlsx',sheet_name = 'Fish')\n", "r.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 写入数据：CSV"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 写入到csv文件中"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>x</th>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>y</th>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>z</th>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   A  B  C\n", "x  1  4  7\n", "y  2  5  8\n", "z  3  6  9"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "df = pd.DataFrame(\n", "    dict(A=range(1, 4), B=range(4, 7), C=range(7, 10)),\n", "    columns=['A','B','C'],\n", "    index=['x','y','z'],\n", ")\n", "df"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["df.to_csv('data/test1.csv')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 切片和查询"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1）查看DataFrame的头部和尾部数据"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Bream</td>\n", "      <td>242.0</td>\n", "      <td>23.2</td>\n", "      <td>25.4</td>\n", "      <td>30.0</td>\n", "      <td>11.5200</td>\n", "      <td>4.0200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bream</td>\n", "      <td>290.0</td>\n", "      <td>24.0</td>\n", "      <td>26.3</td>\n", "      <td>31.2</td>\n", "      <td>12.4800</td>\n", "      <td>4.3056</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bream</td>\n", "      <td>340.0</td>\n", "      <td>23.9</td>\n", "      <td>26.5</td>\n", "      <td>31.1</td>\n", "      <td>12.3778</td>\n", "      <td>4.6961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Bream</td>\n", "      <td>363.0</td>\n", "      <td>26.3</td>\n", "      <td>29.0</td>\n", "      <td>33.5</td>\n", "      <td>12.7300</td>\n", "      <td>4.4555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bream</td>\n", "      <td>430.0</td>\n", "      <td>26.5</td>\n", "      <td>29.0</td>\n", "      <td>34.0</td>\n", "      <td>12.4440</td>\n", "      <td>5.1340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Bream</td>\n", "      <td>450.0</td>\n", "      <td>26.8</td>\n", "      <td>29.7</td>\n", "      <td>34.7</td>\n", "      <td>13.6024</td>\n", "      <td>4.9274</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Bream</td>\n", "      <td>500.0</td>\n", "      <td>26.8</td>\n", "      <td>29.7</td>\n", "      <td>34.5</td>\n", "      <td>14.1795</td>\n", "      <td>5.2785</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Bream</td>\n", "      <td>390.0</td>\n", "      <td>27.6</td>\n", "      <td>30.0</td>\n", "      <td>35.0</td>\n", "      <td>12.6700</td>\n", "      <td>4.6900</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Species  Weight  Length1  Length2  Length3   Height   Width\n", "0   Bream   242.0     23.2     25.4     30.0  11.5200  4.0200\n", "1   Bream   290.0     24.0     26.3     31.2  12.4800  4.3056\n", "2   Bream   340.0     23.9     26.5     31.1  12.3778  4.6961\n", "3   Bream   363.0     26.3     29.0     33.5  12.7300  4.4555\n", "4   Bream   430.0     26.5     29.0     34.0  12.4440  5.1340\n", "5   Bream   450.0     26.8     29.7     34.7  13.6024  4.9274\n", "6   Bream   500.0     26.8     29.7     34.5  14.1795  5.2785\n", "7   Bream   390.0     27.6     30.0     35.0  12.6700  4.6900"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "r = pd.read_csv('data/Fish.csv')\n", "r.head(8)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>154</th>\n", "      <td>Smelt</td>\n", "      <td>12.2</td>\n", "      <td>11.5</td>\n", "      <td>12.2</td>\n", "      <td>13.4</td>\n", "      <td>2.0904</td>\n", "      <td>1.3936</td>\n", "    </tr>\n", "    <tr>\n", "      <th>155</th>\n", "      <td>Smelt</td>\n", "      <td>13.4</td>\n", "      <td>11.7</td>\n", "      <td>12.4</td>\n", "      <td>13.5</td>\n", "      <td>2.4300</td>\n", "      <td>1.2690</td>\n", "    </tr>\n", "    <tr>\n", "      <th>156</th>\n", "      <td>Smelt</td>\n", "      <td>12.2</td>\n", "      <td>12.1</td>\n", "      <td>13.0</td>\n", "      <td>13.8</td>\n", "      <td>2.2770</td>\n", "      <td>1.2558</td>\n", "    </tr>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>Smelt</td>\n", "      <td>19.7</td>\n", "      <td>13.2</td>\n", "      <td>14.3</td>\n", "      <td>15.2</td>\n", "      <td>2.8728</td>\n", "      <td>2.0672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>158</th>\n", "      <td>Smelt</td>\n", "      <td>19.9</td>\n", "      <td>13.8</td>\n", "      <td>15.0</td>\n", "      <td>16.2</td>\n", "      <td>2.9322</td>\n", "      <td>1.8792</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Species  Weight  Length1  Length2  Length3  Height   Width\n", "154   Smelt    12.2     11.5     12.2     13.4  2.0904  1.3936\n", "155   Smelt    13.4     11.7     12.4     13.5  2.4300  1.2690\n", "156   Smelt    12.2     12.1     13.0     13.8  2.2770  1.2558\n", "157   Smelt    19.7     13.2     14.3     15.2  2.8728  2.0672\n", "158   Smelt    19.9     13.8     15.0     16.2  2.9322  1.8792"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["r.tail()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2) 描述与排序"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>159.000000</td>\n", "      <td>159.000000</td>\n", "      <td>159.000000</td>\n", "      <td>159.000000</td>\n", "      <td>159.000000</td>\n", "      <td>159.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>398.326415</td>\n", "      <td>26.247170</td>\n", "      <td>28.415723</td>\n", "      <td>31.227044</td>\n", "      <td>8.970994</td>\n", "      <td>4.417486</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>357.978317</td>\n", "      <td>9.996441</td>\n", "      <td>10.716328</td>\n", "      <td>11.610246</td>\n", "      <td>4.286208</td>\n", "      <td>1.685804</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.000000</td>\n", "      <td>7.500000</td>\n", "      <td>8.400000</td>\n", "      <td>8.800000</td>\n", "      <td>1.728400</td>\n", "      <td>1.047600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>120.000000</td>\n", "      <td>19.050000</td>\n", "      <td>21.000000</td>\n", "      <td>23.150000</td>\n", "      <td>5.944800</td>\n", "      <td>3.385650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>273.000000</td>\n", "      <td>25.200000</td>\n", "      <td>27.300000</td>\n", "      <td>29.400000</td>\n", "      <td>7.786000</td>\n", "      <td>4.248500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>650.000000</td>\n", "      <td>32.700000</td>\n", "      <td>35.500000</td>\n", "      <td>39.650000</td>\n", "      <td>12.365900</td>\n", "      <td>5.584500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1650.000000</td>\n", "      <td>59.000000</td>\n", "      <td>63.400000</td>\n", "      <td>68.000000</td>\n", "      <td>18.957000</td>\n", "      <td>8.142000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            Weight     Length1     Length2     Length3      Height       Width\n", "count   159.000000  159.000000  159.000000  159.000000  159.000000  159.000000\n", "mean    398.326415   26.247170   28.415723   31.227044    8.970994    4.417486\n", "std     357.978317    9.996441   10.716328   11.610246    4.286208    1.685804\n", "min       0.000000    7.500000    8.400000    8.800000    1.728400    1.047600\n", "25%     120.000000   19.050000   21.000000   23.150000    5.944800    3.385650\n", "50%     273.000000   25.200000   27.300000   29.400000    7.786000    4.248500\n", "75%     650.000000   32.700000   35.500000   39.650000   12.365900    5.584500\n", "max    1650.000000   59.000000   63.400000   68.000000   18.957000    8.142000"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["r.describe()"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "      <th>4</th>\n", "      <th>5</th>\n", "      <th>6</th>\n", "      <th>7</th>\n", "      <th>8</th>\n", "      <th>9</th>\n", "      <th>...</th>\n", "      <th>149</th>\n", "      <th>150</th>\n", "      <th>151</th>\n", "      <th>152</th>\n", "      <th>153</th>\n", "      <th>154</th>\n", "      <th>155</th>\n", "      <th>156</th>\n", "      <th>157</th>\n", "      <th>158</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Species</th>\n", "      <td>Bream</td>\n", "      <td>Bream</td>\n", "      <td>Bream</td>\n", "      <td>Bream</td>\n", "      <td>Bream</td>\n", "      <td>Bream</td>\n", "      <td>Bream</td>\n", "      <td>Bream</td>\n", "      <td>Bream</td>\n", "      <td>Bream</td>\n", "      <td>...</td>\n", "      <td>Smelt</td>\n", "      <td>Smelt</td>\n", "      <td>Smelt</td>\n", "      <td>Smelt</td>\n", "      <td>Smelt</td>\n", "      <td>Smelt</td>\n", "      <td>Smelt</td>\n", "      <td>Smelt</td>\n", "      <td>Smelt</td>\n", "      <td>Smelt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Weight</th>\n", "      <td>242</td>\n", "      <td>290</td>\n", "      <td>340</td>\n", "      <td>363</td>\n", "      <td>430</td>\n", "      <td>450</td>\n", "      <td>500</td>\n", "      <td>390</td>\n", "      <td>450</td>\n", "      <td>500</td>\n", "      <td>...</td>\n", "      <td>9.8</td>\n", "      <td>8.7</td>\n", "      <td>10</td>\n", "      <td>9.9</td>\n", "      <td>9.8</td>\n", "      <td>12.2</td>\n", "      <td>13.4</td>\n", "      <td>12.2</td>\n", "      <td>19.7</td>\n", "      <td>19.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Length1</th>\n", "      <td>23.2</td>\n", "      <td>24</td>\n", "      <td>23.9</td>\n", "      <td>26.3</td>\n", "      <td>26.5</td>\n", "      <td>26.8</td>\n", "      <td>26.8</td>\n", "      <td>27.6</td>\n", "      <td>27.6</td>\n", "      <td>28.5</td>\n", "      <td>...</td>\n", "      <td>10.7</td>\n", "      <td>10.8</td>\n", "      <td>11.3</td>\n", "      <td>11.3</td>\n", "      <td>11.4</td>\n", "      <td>11.5</td>\n", "      <td>11.7</td>\n", "      <td>12.1</td>\n", "      <td>13.2</td>\n", "      <td>13.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Length2</th>\n", "      <td>25.4</td>\n", "      <td>26.3</td>\n", "      <td>26.5</td>\n", "      <td>29</td>\n", "      <td>29</td>\n", "      <td>29.7</td>\n", "      <td>29.7</td>\n", "      <td>30</td>\n", "      <td>30</td>\n", "      <td>30.7</td>\n", "      <td>...</td>\n", "      <td>11.2</td>\n", "      <td>11.3</td>\n", "      <td>11.8</td>\n", "      <td>11.8</td>\n", "      <td>12</td>\n", "      <td>12.2</td>\n", "      <td>12.4</td>\n", "      <td>13</td>\n", "      <td>14.3</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Length3</th>\n", "      <td>30</td>\n", "      <td>31.2</td>\n", "      <td>31.1</td>\n", "      <td>33.5</td>\n", "      <td>34</td>\n", "      <td>34.7</td>\n", "      <td>34.5</td>\n", "      <td>35</td>\n", "      <td>35.1</td>\n", "      <td>36.2</td>\n", "      <td>...</td>\n", "      <td>12.4</td>\n", "      <td>12.6</td>\n", "      <td>13.1</td>\n", "      <td>13.1</td>\n", "      <td>13.2</td>\n", "      <td>13.4</td>\n", "      <td>13.5</td>\n", "      <td>13.8</td>\n", "      <td>15.2</td>\n", "      <td>16.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Height</th>\n", "      <td>11.52</td>\n", "      <td>12.48</td>\n", "      <td>12.3778</td>\n", "      <td>12.73</td>\n", "      <td>12.444</td>\n", "      <td>13.6024</td>\n", "      <td>14.1795</td>\n", "      <td>12.67</td>\n", "      <td>14.0049</td>\n", "      <td>14.2266</td>\n", "      <td>...</td>\n", "      <td>2.0832</td>\n", "      <td>1.9782</td>\n", "      <td>2.2139</td>\n", "      <td>2.2139</td>\n", "      <td>2.2044</td>\n", "      <td>2.0904</td>\n", "      <td>2.43</td>\n", "      <td>2.277</td>\n", "      <td>2.8728</td>\n", "      <td>2.9322</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Width</th>\n", "      <td>4.02</td>\n", "      <td>4.3056</td>\n", "      <td>4.6961</td>\n", "      <td>4.4555</td>\n", "      <td>5.134</td>\n", "      <td>4.9274</td>\n", "      <td>5.2785</td>\n", "      <td>4.69</td>\n", "      <td>4.8438</td>\n", "      <td>4.9594</td>\n", "      <td>...</td>\n", "      <td>1.2772</td>\n", "      <td>1.2852</td>\n", "      <td>1.2838</td>\n", "      <td>1.1659</td>\n", "      <td>1.1484</td>\n", "      <td>1.3936</td>\n", "      <td>1.269</td>\n", "      <td>1.2558</td>\n", "      <td>2.0672</td>\n", "      <td>1.8792</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>7 rows × 159 columns</p>\n", "</div>"], "text/plain": ["           0       1        2       3       4        5        6      7    \\\n", "Species  Bream   Bream    Bream   Bream   Bream    Bream    Bream  Bream   \n", "Weight     242     290      340     363     430      450      500    390   \n", "Length1   23.2      24     23.9    26.3    26.5     26.8     26.8   27.6   \n", "Length2   25.4    26.3     26.5      29      29     29.7     29.7     30   \n", "Length3     30    31.2     31.1    33.5      34     34.7     34.5     35   \n", "Height   11.52   12.48  12.3778   12.73  12.444  13.6024  14.1795  12.67   \n", "Width     4.02  4.3056   4.6961  4.4555   5.134   4.9274   5.2785   4.69   \n", "\n", "             8        9    ...     149     150     151     152     153  \\\n", "Species    Bream    Bream  ...   Smelt   Smelt   Smelt   Smelt   Smelt   \n", "Weight       450      500  ...     9.8     8.7      10     9.9     9.8   \n", "Length1     27.6     28.5  ...    10.7    10.8    11.3    11.3    11.4   \n", "Length2       30     30.7  ...    11.2    11.3    11.8    11.8      12   \n", "Length3     35.1     36.2  ...    12.4    12.6    13.1    13.1    13.2   \n", "Height   14.0049  14.2266  ...  2.0832  1.9782  2.2139  2.2139  2.2044   \n", "Width     4.8438   4.9594  ...  1.2772  1.2852  1.2838  1.1659  1.1484   \n", "\n", "            154    155     156     157     158  \n", "Species   Smelt  Smelt   Smelt   Smelt   Smelt  \n", "Weight     12.2   13.4    12.2    19.7    19.9  \n", "Length1    11.5   11.7    12.1    13.2    13.8  \n", "Length2    12.2   12.4      13    14.3      15  \n", "Length3    13.4   13.5    13.8    15.2    16.2  \n", "Height   2.0904   2.43   2.277  2.8728  2.9322  \n", "Width    1.3936  1.269  1.2558  2.0672  1.8792  \n", "\n", "[7 rows x 159 columns]"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["# 转置数据\n", "r.T"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Width</th>\n", "      <th>Weight</th>\n", "      <th>Species</th>\n", "      <th>Length3</th>\n", "      <th>Length2</th>\n", "      <th>Length1</th>\n", "      <th>Height</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4.0200</td>\n", "      <td>242.0</td>\n", "      <td>Bream</td>\n", "      <td>30.0</td>\n", "      <td>25.4</td>\n", "      <td>23.2</td>\n", "      <td>11.5200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.3056</td>\n", "      <td>290.0</td>\n", "      <td>Bream</td>\n", "      <td>31.2</td>\n", "      <td>26.3</td>\n", "      <td>24.0</td>\n", "      <td>12.4800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.6961</td>\n", "      <td>340.0</td>\n", "      <td>Bream</td>\n", "      <td>31.1</td>\n", "      <td>26.5</td>\n", "      <td>23.9</td>\n", "      <td>12.3778</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.4555</td>\n", "      <td>363.0</td>\n", "      <td>Bream</td>\n", "      <td>33.5</td>\n", "      <td>29.0</td>\n", "      <td>26.3</td>\n", "      <td>12.7300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.1340</td>\n", "      <td>430.0</td>\n", "      <td>Bream</td>\n", "      <td>34.0</td>\n", "      <td>29.0</td>\n", "      <td>26.5</td>\n", "      <td>12.4440</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154</th>\n", "      <td>1.3936</td>\n", "      <td>12.2</td>\n", "      <td>Smelt</td>\n", "      <td>13.4</td>\n", "      <td>12.2</td>\n", "      <td>11.5</td>\n", "      <td>2.0904</td>\n", "    </tr>\n", "    <tr>\n", "      <th>155</th>\n", "      <td>1.2690</td>\n", "      <td>13.4</td>\n", "      <td>Smelt</td>\n", "      <td>13.5</td>\n", "      <td>12.4</td>\n", "      <td>11.7</td>\n", "      <td>2.4300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>156</th>\n", "      <td>1.2558</td>\n", "      <td>12.2</td>\n", "      <td>Smelt</td>\n", "      <td>13.8</td>\n", "      <td>13.0</td>\n", "      <td>12.1</td>\n", "      <td>2.2770</td>\n", "    </tr>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>2.0672</td>\n", "      <td>19.7</td>\n", "      <td>Smelt</td>\n", "      <td>15.2</td>\n", "      <td>14.3</td>\n", "      <td>13.2</td>\n", "      <td>2.8728</td>\n", "    </tr>\n", "    <tr>\n", "      <th>158</th>\n", "      <td>1.8792</td>\n", "      <td>19.9</td>\n", "      <td>Smelt</td>\n", "      <td>16.2</td>\n", "      <td>15.0</td>\n", "      <td>13.8</td>\n", "      <td>2.9322</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>159 rows × 7 columns</p>\n", "</div>"], "text/plain": ["      Width  Weight Species  Length3  Length2  Length1   Height\n", "0    4.0200   242.0   Bream     30.0     25.4     23.2  11.5200\n", "1    4.3056   290.0   Bream     31.2     26.3     24.0  12.4800\n", "2    4.6961   340.0   <PERSON><PERSON><PERSON>     31.1     26.5     23.9  12.3778\n", "3    4.4555   363.0   <PERSON>ream     33.5     29.0     26.3  12.7300\n", "4    5.1340   430.0   Bream     34.0     29.0     26.5  12.4440\n", "..      ...     ...     ...      ...      ...      ...      ...\n", "154  1.3936    12.2   Smelt     13.4     12.2     11.5   2.0904\n", "155  1.2690    13.4   Smelt     13.5     12.4     11.7   2.4300\n", "156  1.2558    12.2   Smelt     13.8     13.0     12.1   2.2770\n", "157  2.0672    19.7   Smelt     15.2     14.3     13.2   2.8728\n", "158  1.8792    19.9   Smelt     16.2     15.0     13.8   2.9322\n", "\n", "[159 rows x 7 columns]"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["# 按轴排序 （行标签 or 列标签）\n", "r.sort_index(axis = 1, ascending = False)"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>145</th>\n", "      <td>Smelt</td>\n", "      <td>6.7</td>\n", "      <td>9.3</td>\n", "      <td>9.8</td>\n", "      <td>10.8</td>\n", "      <td>1.7388</td>\n", "      <td>1.0476</td>\n", "    </tr>\n", "    <tr>\n", "      <th>153</th>\n", "      <td>Smelt</td>\n", "      <td>9.8</td>\n", "      <td>11.4</td>\n", "      <td>12.0</td>\n", "      <td>13.2</td>\n", "      <td>2.2044</td>\n", "      <td>1.1484</td>\n", "    </tr>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>Smelt</td>\n", "      <td>7.0</td>\n", "      <td>10.1</td>\n", "      <td>10.6</td>\n", "      <td>11.6</td>\n", "      <td>1.7284</td>\n", "      <td>1.1484</td>\n", "    </tr>\n", "    <tr>\n", "      <th>146</th>\n", "      <td>Smelt</td>\n", "      <td>7.5</td>\n", "      <td>10.0</td>\n", "      <td>10.5</td>\n", "      <td>11.6</td>\n", "      <td>1.9720</td>\n", "      <td>1.1600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>152</th>\n", "      <td>Smelt</td>\n", "      <td>9.9</td>\n", "      <td>11.3</td>\n", "      <td>11.8</td>\n", "      <td>13.1</td>\n", "      <td>2.2139</td>\n", "      <td>1.1659</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>144</th>\n", "      <td>Pike</td>\n", "      <td>1650.0</td>\n", "      <td>59.0</td>\n", "      <td>63.4</td>\n", "      <td>68.0</td>\n", "      <td>10.8120</td>\n", "      <td>7.4800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>116</th>\n", "      <td>Perch</td>\n", "      <td>900.0</td>\n", "      <td>36.5</td>\n", "      <td>39.0</td>\n", "      <td>41.4</td>\n", "      <td>11.1366</td>\n", "      <td>7.4934</td>\n", "    </tr>\n", "    <tr>\n", "      <th>127</th>\n", "      <td>Perch</td>\n", "      <td>1000.0</td>\n", "      <td>41.1</td>\n", "      <td>44.0</td>\n", "      <td>46.6</td>\n", "      <td>12.4888</td>\n", "      <td>7.5958</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>Perch</td>\n", "      <td>840.0</td>\n", "      <td>32.5</td>\n", "      <td>35.0</td>\n", "      <td>37.3</td>\n", "      <td>11.4884</td>\n", "      <td>7.7957</td>\n", "    </tr>\n", "    <tr>\n", "      <th>126</th>\n", "      <td>Perch</td>\n", "      <td>1000.0</td>\n", "      <td>40.2</td>\n", "      <td>43.5</td>\n", "      <td>46.0</td>\n", "      <td>12.6040</td>\n", "      <td>8.1420</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>159 rows × 7 columns</p>\n", "</div>"], "text/plain": ["    Species  Weight  Length1  Length2  Length3   Height   Width\n", "145   Smelt     6.7      9.3      9.8     10.8   1.7388  1.0476\n", "153   Smelt     9.8     11.4     12.0     13.2   2.2044  1.1484\n", "147   Smelt     7.0     10.1     10.6     11.6   1.7284  1.1484\n", "146   Smelt     7.5     10.0     10.5     11.6   1.9720  1.1600\n", "152   Smelt     9.9     11.3     11.8     13.1   2.2139  1.1659\n", "..      ...     ...      ...      ...      ...      ...     ...\n", "144    Pike  1650.0     59.0     63.4     68.0  10.8120  7.4800\n", "116   Perch   900.0     36.5     39.0     41.4  11.1366  7.4934\n", "127   Perch  1000.0     41.1     44.0     46.6  12.4888  7.5958\n", "111   Perch   840.0     32.5     35.0     37.3  11.4884  7.7957\n", "126   Perch  1000.0     40.2     43.5     46.0  12.6040  8.1420\n", "\n", "[159 rows x 7 columns]"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["# 按某一列的值排序\n", "r.sort_values(by  = 'Width')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3） 选择列：使用列索引"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>242.0</td>\n", "      <td>23.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>290.0</td>\n", "      <td>24.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>340.0</td>\n", "      <td>23.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>363.0</td>\n", "      <td>26.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>430.0</td>\n", "      <td>26.5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Weight  Length1\n", "0   242.0     23.2\n", "1   290.0     24.0\n", "2   340.0     23.9\n", "3   363.0     26.3\n", "4   430.0     26.5"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["r['Weight'].head()\n", "r[['Weight','Length1']].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 4) 选择行：使用行号"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Bream</td>\n", "      <td>242.0</td>\n", "      <td>23.2</td>\n", "      <td>25.4</td>\n", "      <td>30.0</td>\n", "      <td>11.5200</td>\n", "      <td>4.0200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bream</td>\n", "      <td>340.0</td>\n", "      <td>23.9</td>\n", "      <td>26.5</td>\n", "      <td>31.1</td>\n", "      <td>12.3778</td>\n", "      <td>4.6961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bream</td>\n", "      <td>430.0</td>\n", "      <td>26.5</td>\n", "      <td>29.0</td>\n", "      <td>34.0</td>\n", "      <td>12.4440</td>\n", "      <td>5.1340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Bream</td>\n", "      <td>500.0</td>\n", "      <td>26.8</td>\n", "      <td>29.7</td>\n", "      <td>34.5</td>\n", "      <td>14.1795</td>\n", "      <td>5.2785</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Bream</td>\n", "      <td>450.0</td>\n", "      <td>27.6</td>\n", "      <td>30.0</td>\n", "      <td>35.1</td>\n", "      <td>14.0049</td>\n", "      <td>4.8438</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Species  Weight  Length1  Length2  Length3   Height   Width\n", "0   Bream   242.0     23.2     25.4     30.0  11.5200  4.0200\n", "2   Bream   340.0     23.9     26.5     31.1  12.3778  4.6961\n", "4   Bream   430.0     26.5     29.0     34.0  12.4440  5.1340\n", "6   Bream   500.0     26.8     29.7     34.5  14.1795  5.2785\n", "8   Bream   450.0     27.6     30.0     35.1  14.0049  4.8438"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["r[0:10:2]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 5) 同时选择行和列，使用属性 `loc`，按索引值选择"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>242.0</td>\n", "      <td>23.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>340.0</td>\n", "      <td>23.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>430.0</td>\n", "      <td>26.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>500.0</td>\n", "      <td>26.8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>450.0</td>\n", "      <td>27.6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>475.0</td>\n", "      <td>28.4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Weight  Length1\n", "0    242.0     23.2\n", "2    340.0     23.9\n", "4    430.0     26.5\n", "6    500.0     26.8\n", "8    450.0     27.6\n", "10   475.0     28.4"]}, "execution_count": 80, "metadata": {}, "output_type": "execute_result"}], "source": ["r.loc[0:10:2,['Weight','Length1']]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 6) 同时选择行和列，使用属性 `iloc`，按位置选择"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Length1</th>\n", "      <th>Length3</th>\n", "      <th>Width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Bream</td>\n", "      <td>23.2</td>\n", "      <td>30.0</td>\n", "      <td>4.0200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bream</td>\n", "      <td>23.9</td>\n", "      <td>31.1</td>\n", "      <td>4.6961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bream</td>\n", "      <td>26.5</td>\n", "      <td>34.0</td>\n", "      <td>5.1340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Bream</td>\n", "      <td>26.8</td>\n", "      <td>34.5</td>\n", "      <td>5.2785</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Bream</td>\n", "      <td>27.6</td>\n", "      <td>35.1</td>\n", "      <td>4.8438</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Species  Length1  Length3   Width\n", "0   Bream     23.2     30.0  4.0200\n", "2   Bream     23.9     31.1  4.6961\n", "4   Bream     26.5     34.0  5.1340\n", "6   Bream     26.8     34.5  5.2785\n", "8   Bream     27.6     35.1  4.8438"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["<PERSON><PERSON>il<PERSON>[0:10:2,0:10:2]"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Bream</td>\n", "      <td>475.0</td>\n", "      <td>28.4</td>\n", "      <td>31.0</td>\n", "      <td>36.2</td>\n", "      <td>14.2628</td>\n", "      <td>5.1042</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Bream</td>\n", "      <td>600.0</td>\n", "      <td>29.4</td>\n", "      <td>32.0</td>\n", "      <td>37.2</td>\n", "      <td>15.4380</td>\n", "      <td>5.5800</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Species  Weight  Length1  Length2  Length3   Height   Width\n", "10   Bream   475.0     28.4     31.0     36.2  14.2628  5.1042\n", "15   Bream   600.0     29.4     32.0     37.2  15.4380  5.5800"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["r.il<PERSON>[[10,15],]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 7) 布尔索引，找出满足某个条件的值"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Bream</td>\n", "      <td>600.0</td>\n", "      <td>29.4</td>\n", "      <td>32.0</td>\n", "      <td>37.2</td>\n", "      <td>14.9544</td>\n", "      <td>5.1708</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Bream</td>\n", "      <td>600.0</td>\n", "      <td>29.4</td>\n", "      <td>32.0</td>\n", "      <td>37.2</td>\n", "      <td>15.4380</td>\n", "      <td>5.5800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Bream</td>\n", "      <td>700.0</td>\n", "      <td>30.4</td>\n", "      <td>33.0</td>\n", "      <td>38.3</td>\n", "      <td>14.8604</td>\n", "      <td>5.2854</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Bream</td>\n", "      <td>700.0</td>\n", "      <td>30.4</td>\n", "      <td>33.0</td>\n", "      <td>38.5</td>\n", "      <td>14.9380</td>\n", "      <td>5.1975</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Bream</td>\n", "      <td>610.0</td>\n", "      <td>30.9</td>\n", "      <td>33.5</td>\n", "      <td>38.6</td>\n", "      <td>15.6330</td>\n", "      <td>5.1338</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Species  Weight  Length1  Length2  Length3   Height   Width\n", "14   Bream   600.0     29.4     32.0     37.2  14.9544  5.1708\n", "15   Bream   600.0     29.4     32.0     37.2  15.4380  5.5800\n", "16   Bream   700.0     30.4     33.0     38.3  14.8604  5.2854\n", "17   Bream   700.0     30.4     33.0     38.5  14.9380  5.1975\n", "18   Bream   610.0     30.9     33.5     38.6  15.6330  5.1338"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["r[r.Weight>500].head()"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Bream</td>\n", "      <td>242.0</td>\n", "      <td>23.2</td>\n", "      <td>25.4</td>\n", "      <td>30.0</td>\n", "      <td>11.5200</td>\n", "      <td>4.0200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bream</td>\n", "      <td>290.0</td>\n", "      <td>24.0</td>\n", "      <td>26.3</td>\n", "      <td>31.2</td>\n", "      <td>12.4800</td>\n", "      <td>4.3056</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bream</td>\n", "      <td>340.0</td>\n", "      <td>23.9</td>\n", "      <td>26.5</td>\n", "      <td>31.1</td>\n", "      <td>12.3778</td>\n", "      <td>4.6961</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Bream</td>\n", "      <td>363.0</td>\n", "      <td>26.3</td>\n", "      <td>29.0</td>\n", "      <td>33.5</td>\n", "      <td>12.7300</td>\n", "      <td>4.4555</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bream</td>\n", "      <td>430.0</td>\n", "      <td>26.5</td>\n", "      <td>29.0</td>\n", "      <td>34.0</td>\n", "      <td>12.4440</td>\n", "      <td>5.1340</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Bream</td>\n", "      <td>450.0</td>\n", "      <td>26.8</td>\n", "      <td>29.7</td>\n", "      <td>34.7</td>\n", "      <td>13.6024</td>\n", "      <td>4.9274</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Bream</td>\n", "      <td>500.0</td>\n", "      <td>26.8</td>\n", "      <td>29.7</td>\n", "      <td>34.5</td>\n", "      <td>14.1795</td>\n", "      <td>5.2785</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Bream</td>\n", "      <td>390.0</td>\n", "      <td>27.6</td>\n", "      <td>30.0</td>\n", "      <td>35.0</td>\n", "      <td>12.6700</td>\n", "      <td>4.6900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Bream</td>\n", "      <td>450.0</td>\n", "      <td>27.6</td>\n", "      <td>30.0</td>\n", "      <td>35.1</td>\n", "      <td>14.0049</td>\n", "      <td>4.8438</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Bream</td>\n", "      <td>500.0</td>\n", "      <td>28.5</td>\n", "      <td>30.7</td>\n", "      <td>36.2</td>\n", "      <td>14.2266</td>\n", "      <td>4.9594</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Bream</td>\n", "      <td>475.0</td>\n", "      <td>28.4</td>\n", "      <td>31.0</td>\n", "      <td>36.2</td>\n", "      <td>14.2628</td>\n", "      <td>5.1042</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Bream</td>\n", "      <td>500.0</td>\n", "      <td>28.7</td>\n", "      <td>31.0</td>\n", "      <td>36.2</td>\n", "      <td>14.3714</td>\n", "      <td>4.8146</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Bream</td>\n", "      <td>500.0</td>\n", "      <td>29.1</td>\n", "      <td>31.5</td>\n", "      <td>36.4</td>\n", "      <td>13.7592</td>\n", "      <td>4.3680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Bream</td>\n", "      <td>340.0</td>\n", "      <td>29.5</td>\n", "      <td>32.0</td>\n", "      <td>37.3</td>\n", "      <td>13.9129</td>\n", "      <td>5.0728</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Bream</td>\n", "      <td>600.0</td>\n", "      <td>29.4</td>\n", "      <td>32.0</td>\n", "      <td>37.2</td>\n", "      <td>14.9544</td>\n", "      <td>5.1708</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Bream</td>\n", "      <td>600.0</td>\n", "      <td>29.4</td>\n", "      <td>32.0</td>\n", "      <td>37.2</td>\n", "      <td>15.4380</td>\n", "      <td>5.5800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Bream</td>\n", "      <td>700.0</td>\n", "      <td>30.4</td>\n", "      <td>33.0</td>\n", "      <td>38.3</td>\n", "      <td>14.8604</td>\n", "      <td>5.2854</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Bream</td>\n", "      <td>700.0</td>\n", "      <td>30.4</td>\n", "      <td>33.0</td>\n", "      <td>38.5</td>\n", "      <td>14.9380</td>\n", "      <td>5.1975</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Bream</td>\n", "      <td>610.0</td>\n", "      <td>30.9</td>\n", "      <td>33.5</td>\n", "      <td>38.6</td>\n", "      <td>15.6330</td>\n", "      <td>5.1338</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Bream</td>\n", "      <td>650.0</td>\n", "      <td>31.0</td>\n", "      <td>33.5</td>\n", "      <td>38.7</td>\n", "      <td>14.4738</td>\n", "      <td>5.7276</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Bream</td>\n", "      <td>575.0</td>\n", "      <td>31.3</td>\n", "      <td>34.0</td>\n", "      <td>39.5</td>\n", "      <td>15.1285</td>\n", "      <td>5.5695</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Bream</td>\n", "      <td>685.0</td>\n", "      <td>31.4</td>\n", "      <td>34.0</td>\n", "      <td>39.2</td>\n", "      <td>15.9936</td>\n", "      <td>5.3704</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Bream</td>\n", "      <td>620.0</td>\n", "      <td>31.5</td>\n", "      <td>34.5</td>\n", "      <td>39.7</td>\n", "      <td>15.5227</td>\n", "      <td>5.2801</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Bream</td>\n", "      <td>680.0</td>\n", "      <td>31.8</td>\n", "      <td>35.0</td>\n", "      <td>40.6</td>\n", "      <td>15.4686</td>\n", "      <td>6.1306</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>Bream</td>\n", "      <td>700.0</td>\n", "      <td>31.9</td>\n", "      <td>35.0</td>\n", "      <td>40.5</td>\n", "      <td>16.2405</td>\n", "      <td>5.5890</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>Bream</td>\n", "      <td>725.0</td>\n", "      <td>31.8</td>\n", "      <td>35.0</td>\n", "      <td>40.9</td>\n", "      <td>16.3600</td>\n", "      <td>6.0532</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>Bream</td>\n", "      <td>720.0</td>\n", "      <td>32.0</td>\n", "      <td>35.0</td>\n", "      <td>40.6</td>\n", "      <td>16.3618</td>\n", "      <td>6.0900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>Bream</td>\n", "      <td>714.0</td>\n", "      <td>32.7</td>\n", "      <td>36.0</td>\n", "      <td>41.5</td>\n", "      <td>16.5170</td>\n", "      <td>5.8515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>Bream</td>\n", "      <td>850.0</td>\n", "      <td>32.8</td>\n", "      <td>36.0</td>\n", "      <td>41.6</td>\n", "      <td>16.8896</td>\n", "      <td>6.1984</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>Bream</td>\n", "      <td>1000.0</td>\n", "      <td>33.5</td>\n", "      <td>37.0</td>\n", "      <td>42.6</td>\n", "      <td>18.9570</td>\n", "      <td>6.6030</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>Bream</td>\n", "      <td>920.0</td>\n", "      <td>35.0</td>\n", "      <td>38.5</td>\n", "      <td>44.1</td>\n", "      <td>18.0369</td>\n", "      <td>6.3063</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>Bream</td>\n", "      <td>955.0</td>\n", "      <td>35.0</td>\n", "      <td>38.5</td>\n", "      <td>44.0</td>\n", "      <td>18.0840</td>\n", "      <td>6.2920</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>Bream</td>\n", "      <td>925.0</td>\n", "      <td>36.2</td>\n", "      <td>39.5</td>\n", "      <td>45.3</td>\n", "      <td>18.7542</td>\n", "      <td>6.7497</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>Bream</td>\n", "      <td>975.0</td>\n", "      <td>37.4</td>\n", "      <td>41.0</td>\n", "      <td>45.9</td>\n", "      <td>18.6354</td>\n", "      <td>6.7473</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>Bream</td>\n", "      <td>950.0</td>\n", "      <td>38.0</td>\n", "      <td>41.0</td>\n", "      <td>46.5</td>\n", "      <td>17.6235</td>\n", "      <td>6.3705</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Species  Weight  Length1  Length2  Length3   Height   Width\n", "0    Bream   242.0     23.2     25.4     30.0  11.5200  4.0200\n", "1    Bream   290.0     24.0     26.3     31.2  12.4800  4.3056\n", "2    Bream   340.0     23.9     26.5     31.1  12.3778  4.6961\n", "3    Bream   363.0     26.3     29.0     33.5  12.7300  4.4555\n", "4    Bream   430.0     26.5     29.0     34.0  12.4440  5.1340\n", "5    Bream   450.0     26.8     29.7     34.7  13.6024  4.9274\n", "6    Bream   500.0     26.8     29.7     34.5  14.1795  5.2785\n", "7    Bream   390.0     27.6     30.0     35.0  12.6700  4.6900\n", "8    Bream   450.0     27.6     30.0     35.1  14.0049  4.8438\n", "9    Bream   500.0     28.5     30.7     36.2  14.2266  4.9594\n", "10   Bream   475.0     28.4     31.0     36.2  14.2628  5.1042\n", "11   Bream   500.0     28.7     31.0     36.2  14.3714  4.8146\n", "12   Bream   500.0     29.1     31.5     36.4  13.7592  4.3680\n", "13   Bream   340.0     29.5     32.0     37.3  13.9129  5.0728\n", "14   Bream   600.0     29.4     32.0     37.2  14.9544  5.1708\n", "15   Bream   600.0     29.4     32.0     37.2  15.4380  5.5800\n", "16   Bream   700.0     30.4     33.0     38.3  14.8604  5.2854\n", "17   Bream   700.0     30.4     33.0     38.5  14.9380  5.1975\n", "18   Bream   610.0     30.9     33.5     38.6  15.6330  5.1338\n", "19   Bream   650.0     31.0     33.5     38.7  14.4738  5.7276\n", "20   Bream   575.0     31.3     34.0     39.5  15.1285  5.5695\n", "21   Bream   685.0     31.4     34.0     39.2  15.9936  5.3704\n", "22   Bream   620.0     31.5     34.5     39.7  15.5227  5.2801\n", "23   Bream   680.0     31.8     35.0     40.6  15.4686  6.1306\n", "24   Bream   700.0     31.9     35.0     40.5  16.2405  5.5890\n", "25   Bream   725.0     31.8     35.0     40.9  16.3600  6.0532\n", "26   Bream   720.0     32.0     35.0     40.6  16.3618  6.0900\n", "27   Bream   714.0     32.7     36.0     41.5  16.5170  5.8515\n", "28   Bream   850.0     32.8     36.0     41.6  16.8896  6.1984\n", "29   Bream  1000.0     33.5     37.0     42.6  18.9570  6.6030\n", "30   Bream   920.0     35.0     38.5     44.1  18.0369  6.3063\n", "31   B<PERSON>m   955.0     35.0     38.5     44.0  18.0840  6.2920\n", "32   <PERSON><PERSON>m   925.0     36.2     39.5     45.3  18.7542  6.7497\n", "33   Bream   975.0     37.4     41.0     45.9  18.6354  6.7473\n", "34   Bream   950.0     38.0     41.0     46.5  17.6235  6.3705"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["r[r.Species == 'Bream']\n", "r[r['Species'] == 'Bream']\n", "r[r['Species'].isin(['Bream','Pike'])]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 数据框的运算"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1）简单的运算：加减乘除"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"data": {"text/plain": ["a    5.2\n", "c    1.1\n", "d    <PERSON><PERSON>\n", "e    0.0\n", "f    NaN\n", "g    NaN\n", "dtype: float64"]}, "execution_count": 91, "metadata": {}, "output_type": "execute_result"}], "source": ["# Series的运算，将在index上对齐后计算\n", "s1 = pd.Series([7.3, -2.5, 3.4, 1.5], index = ['a', 'c', 'd', 'e'])\n", "s2 = pd.Series([-2.1, 3.6, -1.5, 4, 3.1], index = ['a', 'c', 'e', 'f', 'g'])\n", "s1 + s2"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "      <th>d</th>\n", "      <th>e</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Colorado</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ohio</th>\n", "      <td>3.0</td>\n", "      <td>NaN</td>\n", "      <td>6.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Oregon</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Texas</th>\n", "      <td>9.0</td>\n", "      <td>NaN</td>\n", "      <td>12.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Utah</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            b   c     d   e\n", "Colorado  NaN NaN   NaN NaN\n", "Ohio      3.0 NaN   6.0 NaN\n", "Oregon    NaN NaN   NaN NaN\n", "Texas     9.0 NaN  12.0 NaN\n", "Utah      NaN NaN   NaN NaN"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["# DataFrame的运算，将分别在行和列的索引上对齐后计算\n", "df1 = pd.DataFrame(np.arange(9).reshape((3,3)), columns = list('bcd'), index = ['Ohio', 'Texas', 'Colorado'])\n", "df2 = pd.DataFrame(np.arange(12).reshape((4,3)), columns = list('bde'), index = ['Utah', 'Ohio', 'Texas', 'Oregon'])\n", "df1 + df2"]}, {"cell_type": "code", "execution_count": 93, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "      <th>d</th>\n", "      <th>e</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Colorado</th>\n", "      <td>-94.0</td>\n", "      <td>-93.0</td>\n", "      <td>-92.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ohio</th>\n", "      <td>3.0</td>\n", "      <td>-99.0</td>\n", "      <td>6.0</td>\n", "      <td>-95.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Oregon</th>\n", "      <td>-91.0</td>\n", "      <td>NaN</td>\n", "      <td>-90.0</td>\n", "      <td>-89.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Texas</th>\n", "      <td>9.0</td>\n", "      <td>-96.0</td>\n", "      <td>12.0</td>\n", "      <td>-92.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Utah</th>\n", "      <td>-100.0</td>\n", "      <td>NaN</td>\n", "      <td>-99.0</td>\n", "      <td>-98.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              b     c     d     e\n", "Colorado  -94.0 -93.0 -92.0   NaN\n", "Ohio        3.0 -99.0   6.0 -95.0\n", "Oregon    -91.0   NaN -90.0 -89.0\n", "Texas       9.0 -96.0  12.0 -92.0\n", "Utah     -100.0   NaN -99.0 -98.0"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["# 填充默认值\n", "df1 = pd.DataFrame(np.arange(9).reshape((3,3)), columns = list('bcd'), index = ['Ohio', 'Texas', 'Colorado'])\n", "df2 = pd.DataFrame(np.arange(12).reshape((4,3)), columns = list('bde'), index = ['Utah', 'Ohio', 'Texas', 'Oregon'])\n", "df1.add(df2, fill_value = -100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2) 一些常见的统计函数"]}, {"cell_type": "code", "execution_count": 96, "metadata": {}, "outputs": [{"data": {"text/plain": ["Weight     398.326415\n", "Length1     26.247170\n", "Length2     28.415723\n", "Length3     31.227044\n", "Height       8.970994\n", "Width        4.417486\n", "dtype: float64"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('data/Fish.csv')\n", "df.mean()"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"data": {"text/plain": ["4.417485534591194"]}, "execution_count": 97, "metadata": {}, "output_type": "execute_result"}], "source": ["df['Width'].mean()"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"data": {"text/plain": ["0      56.023333\n", "1      64.714267\n", "2      73.095650\n", "3      78.164250\n", "4      89.513000\n", "         ...    \n", "154     8.797333\n", "155     9.116500\n", "156     9.105467\n", "157    11.223333\n", "158    11.618567\n", "Length: 159, dtype: float64"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["df.mean(1)   # 在轴1上执行均值计算（即，每行计算均值）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3）聚合：Groupby函数"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [{"data": {"text/plain": ["<pandas.core.groupby.generic.DataFrameGroupBy object at 0x0000020B0BFAFB20>"]}, "execution_count": 105, "metadata": {}, "output_type": "execute_result"}], "source": ["grouped = df.groupby(by='Species')  # 按照列 Species进行分组\n", "grouped  # 返回一个groupby分组对象"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Species</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th><PERSON>ream</th>\n", "      <td>617.828571</td>\n", "      <td>30.305714</td>\n", "      <td>33.108571</td>\n", "      <td>38.354286</td>\n", "      <td>15.183211</td>\n", "      <td>5.427614</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>154.818182</td>\n", "      <td>18.727273</td>\n", "      <td>20.345455</td>\n", "      <td>22.790909</td>\n", "      <td>8.962427</td>\n", "      <td>3.220736</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Perch</th>\n", "      <td>382.239286</td>\n", "      <td>25.735714</td>\n", "      <td>27.892857</td>\n", "      <td>29.571429</td>\n", "      <td>7.861870</td>\n", "      <td>4.745723</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Pike</th>\n", "      <td>718.705882</td>\n", "      <td>42.476471</td>\n", "      <td>45.482353</td>\n", "      <td>48.717647</td>\n", "      <td>7.713771</td>\n", "      <td>5.086382</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON></th>\n", "      <td>152.050000</td>\n", "      <td>20.645000</td>\n", "      <td>22.275000</td>\n", "      <td>24.970000</td>\n", "      <td>6.694795</td>\n", "      <td>3.657850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Smelt</th>\n", "      <td>11.178571</td>\n", "      <td>11.257143</td>\n", "      <td>11.921429</td>\n", "      <td>13.035714</td>\n", "      <td>2.209371</td>\n", "      <td>1.340093</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Whitefish</th>\n", "      <td>531.000000</td>\n", "      <td>28.800000</td>\n", "      <td>31.316667</td>\n", "      <td>34.316667</td>\n", "      <td>10.027167</td>\n", "      <td>5.473050</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               Weight    Length1    Length2    Length3     Height     Width\n", "Species                                                                    \n", "Bream      617.828571  30.305714  33.108571  38.354286  15.183211  5.427614\n", "Parkki     154.818182  18.727273  20.345455  22.790909   8.962427  3.220736\n", "Perch      382.239286  25.735714  27.892857  29.571429   7.861870  4.745723\n", "Pike       718.705882  42.476471  45.482353  48.717647   7.713771  5.086382\n", "Roach      152.050000  20.645000  22.275000  24.970000   6.694795  3.657850\n", "Smelt       11.178571  11.257143  11.921429  13.035714   2.209371  1.340093\n", "Whitefish  531.000000  28.800000  31.316667  34.316667  10.027167  5.473050"]}, "execution_count": 107, "metadata": {}, "output_type": "execute_result"}], "source": ["# 对groupby分组对象使用统计函数\n", "grouped.size()\n", "grouped.sum()\n", "grouped.mean()"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>x</th>\n", "      <td>Ana</td>\n", "      <td>English</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>y</th>\n", "      <td>Ana</td>\n", "      <td>English</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>z</th>\n", "      <td>Bob</td>\n", "      <td>English</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>t</th>\n", "      <td>Bob</td>\n", "      <td>Math</td>\n", "      <td>10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     A        B   C\n", "x  Ana  English   7\n", "y  Ana  English   8\n", "z  Bob  English   9\n", "t  <PERSON>  10"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["# 按照多个列/变量进行分组\n", "df1 = pd.DataFrame(\n", "    dict(A=['<PERSON>','<PERSON>','<PERSON>','<PERSON>'], \n", "         B=['English','English','English','Math'],\n", "         C=range(7, 11)),\n", "    columns=['A','B','C'],\n", "    index=['x','y','z','t'],\n", ")\n", "df1"]}, {"cell_type": "code", "execution_count": 116, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>C</th>\n", "    </tr>\n", "    <tr>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Ana</th>\n", "      <th>English</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"2\" valign=\"top\">Bob</th>\n", "      <th>English</th>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Math</th>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             C\n", "A   B         \n", "Ana English  2\n", "<PERSON>  1\n", "    Math     1"]}, "execution_count": 116, "metadata": {}, "output_type": "execute_result"}], "source": ["df1.groupby(['A','B']).count()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 添加、删除、修改操作"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1）添加新列"]}, {"cell_type": "code", "execution_count": 123, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "      <th>Width</th>\n", "      <th>new_column</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Bream</td>\n", "      <td>242.0</td>\n", "      <td>23.2</td>\n", "      <td>25.4</td>\n", "      <td>30.0</td>\n", "      <td>11.5200</td>\n", "      <td>4.0200</td>\n", "      <td>-100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bream</td>\n", "      <td>290.0</td>\n", "      <td>24.0</td>\n", "      <td>26.3</td>\n", "      <td>31.2</td>\n", "      <td>12.4800</td>\n", "      <td>4.3056</td>\n", "      <td>-100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bream</td>\n", "      <td>340.0</td>\n", "      <td>23.9</td>\n", "      <td>26.5</td>\n", "      <td>31.1</td>\n", "      <td>12.3778</td>\n", "      <td>4.6961</td>\n", "      <td>-100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Bream</td>\n", "      <td>363.0</td>\n", "      <td>26.3</td>\n", "      <td>29.0</td>\n", "      <td>33.5</td>\n", "      <td>12.7300</td>\n", "      <td>4.4555</td>\n", "      <td>-100</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bream</td>\n", "      <td>430.0</td>\n", "      <td>26.5</td>\n", "      <td>29.0</td>\n", "      <td>34.0</td>\n", "      <td>12.4440</td>\n", "      <td>5.1340</td>\n", "      <td>-100</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Species  Weight  Length1  Length2  Length3   Height   Width  new_column\n", "0   Bream   242.0     23.2     25.4     30.0  11.5200  4.0200        -100\n", "1   Bream   290.0     24.0     26.3     31.2  12.4800  4.3056        -100\n", "2   Bream   340.0     23.9     26.5     31.1  12.3778  4.6961        -100\n", "3   Bream   363.0     26.3     29.0     33.5  12.7300  4.4555        -100\n", "4   Bream   430.0     26.5     29.0     34.0  12.4440  5.1340        -100"]}, "execution_count": 123, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('data/Fish.csv')\n", "df['new_column'] = -100    # 添加一列，具有相同的取值\n", "df.head()"]}, {"cell_type": "code", "execution_count": 131, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Width</th>\n", "      <th>new_column</th>\n", "      <th>total_length</th>\n", "      <th>ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4.0200</td>\n", "      <td>-100</td>\n", "      <td>78.6</td>\n", "      <td>2.865672</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.3056</td>\n", "      <td>-100</td>\n", "      <td>81.5</td>\n", "      <td>2.898551</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.6961</td>\n", "      <td>-100</td>\n", "      <td>81.5</td>\n", "      <td>2.635762</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.4555</td>\n", "      <td>-100</td>\n", "      <td>88.8</td>\n", "      <td>2.857143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.1340</td>\n", "      <td>-100</td>\n", "      <td>89.5</td>\n", "      <td>2.423841</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Width  new_column  total_length     ratio\n", "0  4.0200        -100          78.6  2.865672\n", "1  4.3056        -100          81.5  2.898551\n", "2  4.6961        -100          81.5  2.635762\n", "3  4.4555        -100          88.8  2.857143\n", "4  5.1340        -100          89.5  2.423841"]}, "execution_count": 131, "metadata": {}, "output_type": "execute_result"}], "source": ["# 可添加多个具有实际含义的列\n", "df['total_length'] = df['Length1'] + df['Length2'] + df['Length3']\n", "df['ratio'] = df['Height']/df['Width']\n", "df.il<PERSON>[0:5,-4:]   # 只展示前5行，最后4列"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2) 添加新行"]}, {"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>D</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A0</td>\n", "      <td>B0</td>\n", "      <td>C0</td>\n", "      <td>D0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A1</td>\n", "      <td>B1</td>\n", "      <td>C1</td>\n", "      <td>D1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A2</td>\n", "      <td>B2</td>\n", "      <td>C2</td>\n", "      <td>D2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A3</td>\n", "      <td>B3</td>\n", "      <td>C3</td>\n", "      <td>D3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    A   B   C   D\n", "0  A0  B0  C0  D0\n", "1  A1  B1  C1  D1\n", "2  A2  B2  C2  D2\n", "3  A3  B3  C3  D3"]}, "execution_count": 132, "metadata": {}, "output_type": "execute_result"}], "source": ["df1 = pd.DataFrame(\n", "    {\n", "    \"A\": [\"A0\", \"A1\", \"A2\", \"A3\"],\n", "    \"B\": [\"B0\", \"B1\", \"B2\", \"B3\"],\n", "    \"C\": [\"C0\", \"C1\", \"C2\", \"C3\"],\n", "    \"D\": [\"D0\", \"D1\", \"D2\", \"D3\"],},\n", "    index=[0, 1, 2, 3],\n", ")\n", "df1"]}, {"cell_type": "code", "execution_count": 133, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>D</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A4</td>\n", "      <td>B4</td>\n", "      <td>C4</td>\n", "      <td>D4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A5</td>\n", "      <td>B5</td>\n", "      <td>C5</td>\n", "      <td>D5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A6</td>\n", "      <td>B6</td>\n", "      <td>C6</td>\n", "      <td>D6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A7</td>\n", "      <td>B7</td>\n", "      <td>C7</td>\n", "      <td>D7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    A   B   C   D\n", "0  A4  B4  C4  D4\n", "1  A5  B5  C5  D5\n", "2  A6  B6  C6  D6\n", "3  A7  B7  C7  D7"]}, "execution_count": 133, "metadata": {}, "output_type": "execute_result"}], "source": ["df2 = pd.DataFrame(\n", "    {\n", "    \"A\": [\"A4\", \"A5\", \"A6\", \"A7\"],\n", "    \"B\": [\"B4\", \"B5\", \"B6\", \"B7\"],\n", "    \"C\": [\"C4\", \"C5\", \"C6\", \"C7\"],\n", "    \"D\": [\"D4\", \"D5\", \"D6\", \"D7\"],},\n", "    index=[0, 1, 2, 3],\n", ")\n", "df2"]}, {"cell_type": "code", "execution_count": 134, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>D</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A0</td>\n", "      <td>B0</td>\n", "      <td>C0</td>\n", "      <td>D0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A1</td>\n", "      <td>B1</td>\n", "      <td>C1</td>\n", "      <td>D1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A2</td>\n", "      <td>B2</td>\n", "      <td>C2</td>\n", "      <td>D2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A3</td>\n", "      <td>B3</td>\n", "      <td>C3</td>\n", "      <td>D3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A4</td>\n", "      <td>B4</td>\n", "      <td>C4</td>\n", "      <td>D4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A5</td>\n", "      <td>B5</td>\n", "      <td>C5</td>\n", "      <td>D5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A6</td>\n", "      <td>B6</td>\n", "      <td>C6</td>\n", "      <td>D6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A7</td>\n", "      <td>B7</td>\n", "      <td>C7</td>\n", "      <td>D7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    A   B   C   D\n", "0  A0  B0  C0  D0\n", "1  A1  B1  C1  D1\n", "2  A2  B2  C2  D2\n", "3  A3  B3  C3  D3\n", "0  A4  B4  C4  D4\n", "1  A5  B5  C5  D5\n", "2  A6  B6  C6  D6\n", "3  A7  B7  C7  D7"]}, "execution_count": 134, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将两个数据集按照行连接：df1与df2的变量名完全相同\n", "result = df1.append(df2)\n", "result"]}, {"cell_type": "code", "execution_count": 135, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>B</th>\n", "      <th>D</th>\n", "      <th>F</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>B2</td>\n", "      <td>D2</td>\n", "      <td>F2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>B3</td>\n", "      <td>D3</td>\n", "      <td>F3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>B6</td>\n", "      <td>D6</td>\n", "      <td>F6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>B7</td>\n", "      <td>D7</td>\n", "      <td>F7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    B   D   F\n", "2  B2  D2  F2\n", "3  B3  D3  F3\n", "6  B6  D6  F6\n", "7  B7  D7  F7"]}, "execution_count": 135, "metadata": {}, "output_type": "execute_result"}], "source": ["df4 = pd.DataFrame(\n", "    {\n", "        \"B\": [\"B2\", \"B3\", \"B6\", \"B7\"],\n", "        \"D\": [\"D2\", \"D3\", \"D6\", \"D7\"],\n", "        \"F\": [\"F2\", \"F3\", \"F6\", \"F7\"],},\n", "    index=[2, 3, 6, 7],\n", ")\n", "df4"]}, {"cell_type": "code", "execution_count": 139, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>D</th>\n", "      <th>F</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A0</td>\n", "      <td>B0</td>\n", "      <td>C0</td>\n", "      <td>D0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A1</td>\n", "      <td>B1</td>\n", "      <td>C1</td>\n", "      <td>D1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A2</td>\n", "      <td>B2</td>\n", "      <td>C2</td>\n", "      <td>D2</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A3</td>\n", "      <td>B3</td>\n", "      <td>C3</td>\n", "      <td>D3</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>NaN</td>\n", "      <td>B2</td>\n", "      <td>NaN</td>\n", "      <td>D2</td>\n", "      <td>F2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>NaN</td>\n", "      <td>B3</td>\n", "      <td>NaN</td>\n", "      <td>D3</td>\n", "      <td>F3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>NaN</td>\n", "      <td>B6</td>\n", "      <td>NaN</td>\n", "      <td>D6</td>\n", "      <td>F6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>NaN</td>\n", "      <td>B7</td>\n", "      <td>NaN</td>\n", "      <td>D7</td>\n", "      <td>F7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     A   B    C   D    F\n", "0   A0  B0   C0  D0  NaN\n", "1   A1  B1   C1  D1  NaN\n", "2   A2  B2   C2  D2  NaN\n", "3   A3  B3   C3  D3  NaN\n", "4  NaN  B2  NaN  D2   F2\n", "5  NaN  B3  NaN  D3   F3\n", "6  NaN  B6  NaN  D6   F6\n", "7  NaN  B7  NaN  D7   F7"]}, "execution_count": 139, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将df1与df4按照行连接，对索引值index重新编号，且两个数据集的列不完全相同时，生成的新数据集为所有列的并集\n", "result = df1.append(df4, ignore_index=True)\n", "result "]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>D</th>\n", "      <th>B</th>\n", "      <th>D</th>\n", "      <th>F</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A0</td>\n", "      <td>B0</td>\n", "      <td>C0</td>\n", "      <td>D0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A1</td>\n", "      <td>B1</td>\n", "      <td>C1</td>\n", "      <td>D1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A2</td>\n", "      <td>B2</td>\n", "      <td>C2</td>\n", "      <td>D2</td>\n", "      <td>B2</td>\n", "      <td>D2</td>\n", "      <td>F2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A3</td>\n", "      <td>B3</td>\n", "      <td>C3</td>\n", "      <td>D3</td>\n", "      <td>B3</td>\n", "      <td>D3</td>\n", "      <td>F3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>B6</td>\n", "      <td>D6</td>\n", "      <td>F6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     A    B    C    D    B    D    F\n", "0   A0   B0   C0   D0  NaN  NaN  NaN\n", "1   A1   B1   C1   D1  NaN  NaN  NaN\n", "2   A2   B2   C2   D2   B2   D2   F2\n", "3   A3   B3   C3   D3   B3   D3   F3\n", "6  NaN  NaN  NaN  NaN   B6   D6   F6"]}, "execution_count": 146, "metadata": {}, "output_type": "execute_result"}], "source": ["# 也可以使用concat进行行或者列的添加\n", "result = pd.concat([df1,df4[0:3]], axis = 1)  # 沿着轴1（列）进行添加\n", "result"]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>D</th>\n", "      <th>F</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>A0</td>\n", "      <td>B0</td>\n", "      <td>C0</td>\n", "      <td>D0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>A1</td>\n", "      <td>B1</td>\n", "      <td>C1</td>\n", "      <td>D1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>A2</td>\n", "      <td>B2</td>\n", "      <td>C2</td>\n", "      <td>D2</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>A3</td>\n", "      <td>B3</td>\n", "      <td>C3</td>\n", "      <td>D3</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>B2</td>\n", "      <td>NaN</td>\n", "      <td>D2</td>\n", "      <td>F2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NaN</td>\n", "      <td>B3</td>\n", "      <td>NaN</td>\n", "      <td>D3</td>\n", "      <td>F3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>NaN</td>\n", "      <td>B6</td>\n", "      <td>NaN</td>\n", "      <td>D6</td>\n", "      <td>F6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     A   B    C   D    F\n", "0   A0  B0   C0  D0  NaN\n", "1   A1  B1   C1  D1  NaN\n", "2   A2  B2   C2  D2  NaN\n", "3   A3  B3   C3  D3  NaN\n", "2  NaN  B2  NaN  D2   F2\n", "3  NaN  B3  NaN  D3   F3\n", "6  NaN  B6  NaN  D6   F6"]}, "execution_count": 147, "metadata": {}, "output_type": "execute_result"}], "source": ["# 也可以使用concat进行行或者列的添加\n", "result = pd.concat([df1,df4[0:3]], axis = 0)  # 沿着轴0（行）进行添加\n", "result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3）删除数据"]}, {"cell_type": "code", "execution_count": 161, "metadata": {}, "outputs": [{"data": {"text/plain": ["0      4.0200\n", "1      4.3056\n", "2      4.6961\n", "3      4.4555\n", "4      5.1340\n", "        ...  \n", "154    1.3936\n", "155    1.2690\n", "156    1.2558\n", "157    2.0672\n", "158    1.8792\n", "Name: Width, Length: 159, dtype: float64"]}, "execution_count": 161, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('data/Fish.csv')\n", "df['new_column'] = -100    \n", "del df['new_column']  # 使用del来删除列\n", "# 使用pop来删除列，并返回删除的内容\n", "popped_var = df.pop('Width')\n", "popped_var"]}, {"cell_type": "code", "execution_count": 168, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bream</td>\n", "      <td>290.0</td>\n", "      <td>24.0</td>\n", "      <td>26.3</td>\n", "      <td>31.2</td>\n", "      <td>12.4800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bream</td>\n", "      <td>340.0</td>\n", "      <td>23.9</td>\n", "      <td>26.5</td>\n", "      <td>31.1</td>\n", "      <td>12.3778</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bream</td>\n", "      <td>430.0</td>\n", "      <td>26.5</td>\n", "      <td>29.0</td>\n", "      <td>34.0</td>\n", "      <td>12.4440</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Bream</td>\n", "      <td>450.0</td>\n", "      <td>26.8</td>\n", "      <td>29.7</td>\n", "      <td>34.7</td>\n", "      <td>13.6024</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Bream</td>\n", "      <td>500.0</td>\n", "      <td>26.8</td>\n", "      <td>29.7</td>\n", "      <td>34.5</td>\n", "      <td>14.1795</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Species  Weight  Length1  Length2  Length3   Height\n", "1   Bream   290.0     24.0     26.3     31.2  12.4800\n", "2   Bream   340.0     23.9     26.5     31.1  12.3778\n", "4   Bream   430.0     26.5     29.0     34.0  12.4440\n", "5   Bream   450.0     26.8     29.7     34.7  13.6024\n", "6   Bream   500.0     26.8     29.7     34.5  14.1795"]}, "execution_count": 168, "metadata": {}, "output_type": "execute_result"}], "source": ["# 使用drop进行删除行或者列\n", "df.drop([0,3]).loc[0:6,]"]}, {"cell_type": "code", "execution_count": 170, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Bream</td>\n", "      <td>23.2</td>\n", "      <td>25.4</td>\n", "      <td>30.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bream</td>\n", "      <td>24.0</td>\n", "      <td>26.3</td>\n", "      <td>31.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bream</td>\n", "      <td>23.9</td>\n", "      <td>26.5</td>\n", "      <td>31.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Bream</td>\n", "      <td>26.3</td>\n", "      <td>29.0</td>\n", "      <td>33.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bream</td>\n", "      <td>26.5</td>\n", "      <td>29.0</td>\n", "      <td>34.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Species  Length1  Length2  Length3\n", "0   Bream     23.2     25.4     30.0\n", "1   Bream     24.0     26.3     31.2\n", "2   Bream     23.9     26.5     31.1\n", "3   <PERSON>ream     26.3     29.0     33.5\n", "4   Bream     26.5     29.0     34.0"]}, "execution_count": 170, "metadata": {}, "output_type": "execute_result"}], "source": ["df.drop(['Height','Weight'],axis=1).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 4) 修改行或者列标签（索引名）"]}, {"cell_type": "code", "execution_count": 180, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight_new</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height_new</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Bream</td>\n", "      <td>242.0</td>\n", "      <td>23.2</td>\n", "      <td>25.4</td>\n", "      <td>30.0</td>\n", "      <td>11.5200</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bream</td>\n", "      <td>290.0</td>\n", "      <td>24.0</td>\n", "      <td>26.3</td>\n", "      <td>31.2</td>\n", "      <td>12.4800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bream</td>\n", "      <td>340.0</td>\n", "      <td>23.9</td>\n", "      <td>26.5</td>\n", "      <td>31.1</td>\n", "      <td>12.3778</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Bream</td>\n", "      <td>363.0</td>\n", "      <td>26.3</td>\n", "      <td>29.0</td>\n", "      <td>33.5</td>\n", "      <td>12.7300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bream</td>\n", "      <td>430.0</td>\n", "      <td>26.5</td>\n", "      <td>29.0</td>\n", "      <td>34.0</td>\n", "      <td>12.4440</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Species  Weight_new  Length1  Length2  Length3  Height_new\n", "0   Bream       242.0     23.2     25.4     30.0     11.5200\n", "1   Bream       290.0     24.0     26.3     31.2     12.4800\n", "2   Bream       340.0     23.9     26.5     31.1     12.3778\n", "3   Bream       363.0     26.3     29.0     33.5     12.7300\n", "4   Bream       430.0     26.5     29.0     34.0     12.4440"]}, "execution_count": 180, "metadata": {}, "output_type": "execute_result"}], "source": ["df.rename({'Weight':'Weight_new','Height':'Height_new'},axis = 1).head()\n", "# df.rename(columns={'Weight':'Weight_new','Height':'Height_new'}).head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 5) 修改某些数据"]}, {"cell_type": "code", "execution_count": 183, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Species</th>\n", "      <th>Weight_new</th>\n", "      <th>Length1</th>\n", "      <th>Length2</th>\n", "      <th>Length3</th>\n", "      <th>Height_new</th>\n", "      <th>Weight</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Bream</td>\n", "      <td>242.0</td>\n", "      <td>23.2</td>\n", "      <td>25.4</td>\n", "      <td>30.0</td>\n", "      <td>11.5200</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bream</td>\n", "      <td>290.0</td>\n", "      <td>24.0</td>\n", "      <td>26.3</td>\n", "      <td>31.2</td>\n", "      <td>12.4800</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Bream</td>\n", "      <td>340.0</td>\n", "      <td>23.9</td>\n", "      <td>26.5</td>\n", "      <td>31.1</td>\n", "      <td>12.3778</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Bream</td>\n", "      <td>363.0</td>\n", "      <td>26.3</td>\n", "      <td>29.0</td>\n", "      <td>33.5</td>\n", "      <td>12.7300</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Bream</td>\n", "      <td>430.0</td>\n", "      <td>26.5</td>\n", "      <td>29.0</td>\n", "      <td>34.0</td>\n", "      <td>12.4440</td>\n", "      <td>-1000.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Species  Weight_new  Length1  Length2  Length3  Height_new  Weight\n", "0   Bream       242.0     23.2     25.4     30.0     11.5200 -1000.0\n", "1   Bream       290.0     24.0     26.3     31.2     12.4800 -1000.0\n", "2   Bream       340.0     23.9     26.5     31.1     12.3778 -1000.0\n", "3   Bream       363.0     26.3     29.0     33.5     12.7300 -1000.0\n", "4   Bream       430.0     26.5     29.0     34.0     12.4440 -1000.0"]}, "execution_count": 183, "metadata": {}, "output_type": "execute_result"}], "source": ["# 使用前面讲过的查找数据（切片）的方法来定位要修改的数据，然后通过赋值操作进行修改。\n", "# 找到Sepecies为Bream的行，对这些行的Weight变量进行修改\n", "df.loc[df['Species']=='Bream', 'Weight'] = -1000\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 处理缺失值"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 1）发现缺失值"]}, {"cell_type": "code", "execution_count": 184, "metadata": {}, "outputs": [{"data": {"text/plain": ["float"]}, "execution_count": 184, "metadata": {}, "output_type": "execute_result"}], "source": ["type(np.nan)  # NaN和None的区别"]}, {"cell_type": "code", "execution_count": 185, "metadata": {}, "outputs": [{"data": {"text/plain": ["NoneType"]}, "execution_count": 185, "metadata": {}, "output_type": "execute_result"}], "source": ["type(None)"]}, {"cell_type": "code", "execution_count": 186, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    False\n", "1     True\n", "2    False\n", "3     True\n", "dtype: bool"]}, "execution_count": 186, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.Series([1,np.nan,'Hello',None])\n", "data.isnull()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 2）剔除缺失值"]}, {"cell_type": "code", "execution_count": 187, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>4.0</td>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     0    1  2\n", "0  1.0  NaN  2\n", "1  2.0  3.0  5\n", "2  NaN  4.0  6"]}, "execution_count": 187, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame([[1, np.nan, 2],\n", "                   [2, 3, 5],\n", "                   [np.nan, 4, 6]])\n", "df"]}, {"cell_type": "code", "execution_count": 188, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     0    1  2\n", "1  2.0  3.0  5"]}, "execution_count": 188, "metadata": {}, "output_type": "execute_result"}], "source": ["df.dropna()  # 默认删除所有包含缺失值的数据"]}, {"cell_type": "code", "execution_count": 191, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   2\n", "0  2\n", "1  5\n", "2  6"]}, "execution_count": 191, "metadata": {}, "output_type": "execute_result"}], "source": ["df.dropna(axis='columns')  # 删除包含缺失值的列"]}, {"cell_type": "code", "execution_count": 192, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>4.0</td>\n", "      <td>6</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     0    1  2   3\n", "0  1.0  NaN  2 NaN\n", "1  2.0  3.0  5 NaN\n", "2  NaN  4.0  6 NaN"]}, "execution_count": 192, "metadata": {}, "output_type": "execute_result"}], "source": ["# 删除绝大多数是缺失值的行或列，则可以设置how或者thresh参数。\n", "df[3] = np.nan\n", "df"]}, {"cell_type": "code", "execution_count": 195, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>4.0</td>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     0    1  2\n", "0  1.0  NaN  2\n", "1  2.0  3.0  5\n", "2  NaN  4.0  6"]}, "execution_count": 195, "metadata": {}, "output_type": "execute_result"}], "source": ["# 删除全部是缺失值的列\n", "df.dropna(axis='columns',how='all')"]}, {"cell_type": "code", "execution_count": 201, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "      <th>3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     0    1  2   3\n", "1  2.0  3.0  5 NaN"]}, "execution_count": 201, "metadata": {}, "output_type": "execute_result"}], "source": ["# thresh是阈值，表示至少有thresh个非空数值的才保留下来。\n", "df.dropna(axis='index',thresh=3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 3) 填充缺失值"]}, {"cell_type": "code", "execution_count": 202, "metadata": {}, "outputs": [{"data": {"text/plain": ["a    1.0\n", "b    <PERSON><PERSON>\n", "c    2.0\n", "d    <PERSON><PERSON>\n", "e    3.0\n", "dtype: float64"]}, "execution_count": 202, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.Series([1, np.nan, 2, None, 3], index = list('abcde'))\n", "data"]}, {"cell_type": "code", "execution_count": 203, "metadata": {}, "outputs": [{"data": {"text/plain": ["a    1.0\n", "b    0.0\n", "c    2.0\n", "d    0.0\n", "e    3.0\n", "dtype: float64"]}, "execution_count": 203, "metadata": {}, "output_type": "execute_result"}], "source": ["data.fillna(0)"]}, {"cell_type": "code", "execution_count": 204, "metadata": {}, "outputs": [{"data": {"text/plain": ["a    1.0\n", "b    1.0\n", "c    2.0\n", "d    2.0\n", "e    3.0\n", "dtype: float64"]}, "execution_count": 204, "metadata": {}, "output_type": "execute_result"}], "source": ["data.fillna(method='ffill') #用缺失值前面的有效值来从前往后填充（forward-fill）"]}, {"cell_type": "code", "execution_count": 205, "metadata": {}, "outputs": [{"data": {"text/plain": ["a    1.0\n", "b    2.0\n", "c    2.0\n", "d    3.0\n", "e    3.0\n", "dtype: float64"]}, "execution_count": 205, "metadata": {}, "output_type": "execute_result"}], "source": ["data.fillna(method='bfill') # 用缺失值后面的有效值来从前往后填充（backward-fill）"]}, {"cell_type": "code", "execution_count": 209, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>0</th>\n", "      <th>1</th>\n", "      <th>2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>4.0</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     0    1    2\n", "0  1.0  1.0  2.0\n", "1  2.0  3.0  5.0\n", "2  NaN  4.0  6.0"]}, "execution_count": 209, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame([[1, np.nan, 2],\n", "                   [2, 3, 5],\n", "                   [np.nan, 4, 6]])\n", "df.fillna(method='ffill',axis=1)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 4}