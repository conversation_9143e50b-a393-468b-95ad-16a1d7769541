#!/usr/bin/env python
# coding: utf-8

# # 10 Exercise

# In[1]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Sunburst, Tree, Pie
import random
import plotly.express as px
import plotly.graph_objects as go

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号


# ## 随堂练习（1）
# 利用plotly绘制：
# data = [
#     {"name": "A", "children": [
#         {"name": "A1", "value": 10},
#         {"name": "A2", "value": 5}
#     ]},
#     {"name": "B", "children": [
#         {"name": "B1", "value": 20},
#         {"name": "B2", "value": 15}
#     ]}
# ]

# In[6]:


# 原始树形数据
data = [
    {"name": "A", "children": [
        {"name": "A1", "value": 10},
        {"name": "A2", "value": 5}
    ]},
    {"name": "B", "children": [
        {"name": "B1", "value": 20},
        {"name": "B2", "value": 15}
    ]}
]

plotly_data = []

for item in data:
    parent_name = item['name']
    for child in item['children']:
        plotly_data.append({
            'name': parent_name,
            'children': child['name'],
            'value': child['value']
        })

print(plotly_data)

# 创建 DataFrame
df = pd.DataFrame(plotly_data)
print(df)

# 使用Plotly绘制旭日图
fig = px.sunburst(df, path=['name', 'children'], values='value', title="Plotly Sunburst Chart")
fig.show()


# ## 随堂练习（2）
# 根据数据绘制基本饼图

# In[4]:


languages = ['Python', 'Java', 'C', 'PHP', 'R']
counts = [40,20,30,10,30]

# 使用plotly绘制
df = pd.DataFrame({'Languages': languages, 'Value': counts})

fig = px.pie(df, names='Languages', values='Value', title='Plotly Pie Chart')
fig.show()


# ## 随堂练习（3）
# 根据练习（2）绘制画布

# In[5]:


import matplotlib.pyplot as plt

# 数据
languages = ['Python', 'Java', 'C', 'PHP', 'R']
counts = [40, 20, 30, 10, 30]

# 创建一行两列的子图
fig, axs = plt.subplots(1, 2, figsize=(12, 6))

# 第一个饼图（基本饼图）
axs[0].pie(counts, labels=languages, autopct='%1.1f%%', startangle=90)
axs[0].set_title("Basic Pie Chart")

# 第二个饼图（甜甜圈饼图）
axs[1].pie(counts, labels=languages, autopct='%1.1f%%', startangle=90, wedgeprops={'width': 0.3})
axs[1].set_title("Donut Pie Chart")

plt.tight_layout()  # 自动调整布局
plt.show()


# In[23]:


from pyecharts.charts import Pie, Grid
from pyecharts import options as opts

# 数据
languages = ['Python', 'Java', 'C', 'PHP', 'R']
counts = [40, 20, 30, 10, 30]

# 基本饼图
pie1 = (
    Pie()
    .add("", [list(z) for z in zip(languages, counts)])
    .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {d}%"))
)

# 甜甜圈饼图
pie2 = (
    Pie()
    .add("", [list(z) for z in zip(languages, counts)], radius=["40%", "70%"])
    .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {d}%"))
    .set_global_opts(title_opts=opts.TitleOpts(title="Donut Pie Chart"))
)

# 使用Grid布局
grid = (
    Grid()
    .add(pie2, grid_opts=opts.GridOpts(pos_left='5%',pos_right="55%"))  # 左边的饼图
    .add(pie2, grid_opts=opts.GridOpts(pos_right="5%",pos_left='55%'))   # 右边的甜甜圈饼图
)

grid.render_notebook()  # Jupyter 里展示
# grid.render("grid.html")  # 生成html的时候只能显示第一个，如果保存成html的时候，可以采用下一个cell的代码


# In[21]:


from pyecharts.charts import Pie
from pyecharts import options as opts

# 数据
languages = ['Python', 'Java', 'C', 'PHP', 'R']
counts = [40, 20, 30, 10, 30]

# 同一个 Pie 里，画两个饼图
pie = Pie()

# 第一个饼图
pie.add(
    series_name="普通饼图",
    data_pair=[list(z) for z in zip(languages, counts)],
    center=["25%", "50%"],  # 位置：画布25%的宽度，50%的高度
)

# 第二个甜甜圈
pie.add(
    series_name="甜甜圈图",
    data_pair=[list(z) for z in zip(languages, counts)],
    radius=["40%", "70%"],   # 甜甜圈样式
    center=["75%", "50%"],   # 位置：画布75%的宽度，50%的高度
)

# 全局配置
pie.set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {d}%"))
pie.set_global_opts(title_opts=opts.TitleOpts(title="左右双饼图展示"))

pie.render("dual_pie.html")


# In[8]:


import plotly.graph_objects as go
from plotly.subplots import make_subplots

# 数据
languages = ['Python', 'Java', 'C', 'PHP', 'R']
counts = [40, 20, 30, 10, 30]

# 创建1行2列的子图，并指定子图类型为 'pie'
fig = make_subplots(rows=1, cols=2, 
                    subplot_titles=["Basic Pie Chart", "Donut Pie Chart"],
                    specs=[[{'type': 'pie'}, {'type': 'pie'}]])

# 第一个子图：基本饼图
fig.add_trace(go.Pie(labels=languages, values=counts, hole=0, name='Basic Pie'), row=1, col=1)

# 第二个子图：甜甜圈饼图
fig.add_trace(go.Pie(labels=languages, values=counts, hole=0.3, name='Donut Pie'), row=1, col=2)

fig.update_layout(height=400, width=800, title_text="Pie and Donut Pie Charts")
fig.show()


# ## 随堂练习（4）
# 读取文件后绘制嵌套饼图

# In[36]:


# 读取数据
df = pd.read_csv("pokemon.csv")

# 处理缺失值——用None
df['Type 2'] = df['Type 2'].fillna('None')

# 筛选最多的两个主类型 Type 1
top2_type1 = df["Type 1"].value_counts().nlargest(2).index.tolist()
df_filtered = df[df["Type 1"].isin(top2_type1)]

# 内环数据（Type 1）
inner_labels = df_filtered["Type 1"].value_counts().index.tolist()
inner_sizes = df_filtered["Type 1"].value_counts().values.tolist()
print(inner_labels, inner_sizes)

# 外环数据（Type 2 在每个 Type 1 下的分布）
outer = df_filtered.groupby(["Type 1", "Type 2"]).size().reset_index(name="count")

# 思考：如果屏蔽下面的代码，嵌套饼图有什么问题？
'''
# 将type1设置为一个有顺序的分类变量（Categorical Variable），顺序是inner_labels给定的顺序
outer["Type 1"] = pd.Categorical(outer["Type 1"], categories=inner_labels, ordered=True)
# 按照定义的顺序（不是字母顺序）来排
outer = outer.sort_values(["Type 1", "Type 2"])
# print(outer)
'''

# 拼接标签时，强制转换成str
outer_labels = list(outer["Type 1"].astype(str) + " - " + outer["Type 2"].astype(str))
outer_sizes = list(outer["count"])
print(outer_labels, outer_sizes)


# In[30]:


# 绘图
fig, ax = plt.subplots(figsize=(8, 8))
# 绘制外层饼图
ax.pie(outer_sizes, labels=outer_labels, radius=1)
# 绘制内层饼图，调整半径，使其成为嵌套图
ax.pie(inner_sizes, labels=inner_labels, radius=0.5)

ax.set(aspect="equal", title="嵌套饼图 - Matplotlib（Type1 和 Type2）")
plt.show()


# In[31]:


import plotly.express as px

sunburst_df = df_filtered.copy()
sunburst_df["Type 2"] = sunburst_df["Type 2"].fillna("None")

fig = px.sunburst(sunburst_df, path=["Type 1", "Type 2"], values=[1]*len(sunburst_df),
                  title="嵌套饼图（Plotly Sunburst）")
fig.show()


# In[32]:


# 内环
inner_data = [list(z) for z in zip(inner_labels, inner_sizes)]
# 外环
outer_data = [list(z) for z in zip(outer_labels, outer_sizes)]
# print(outer_data)

pie = (
    Pie()
    .add("", outer_data, radius=["50%", "70%"])  # 再添加外环
    .add("", inner_data, radius=["20%", "50%"])  # 先添加内环
    .set_global_opts(title_opts=opts.TitleOpts(title="嵌套饼图 - Pyecharts"))
    .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {d}%"))
)
pie.render("nested_pie_pyecharts.html")


# In[ ]:




