#!/usr/bin/env python
# coding: utf-8

# # Lecture 7：类别比较数据

# In[1]:


# 导入必备的包
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
import plotly.express as px
from pyecharts.charts import Bar, Line, Radar
from pyecharts import options as opts


# ## 柱状图

# ### 单一柱状图

# In[2]:


# 自定义数据集
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value': [10, 15, 7, 12, 8]})  # 创建一个包含类别和值的DataFrame
# 查看数据结构
data


# In[3]:


# 利用matplotlib创建单一柱状图

plt.figure(figsize=(6, 4))  # 创建图形对象，并设置图形大小
# bar函数绘制柱状图
plt.bar(data['category'], data['value'], color='steelblue')
# 绘制柱状图，指定x轴为类别，y轴为值，柱状颜色为钢蓝色
plt.xlabel('Category')  # 设置x轴标签
plt.ylabel('Value')  # 设置y轴标签
plt.title('Single Bar Chart')  # 设置图表标题

# 添加网格线，采用虚线，设置为灰色，透明度为0.5
plt.grid(linestyle='-', color='gray', alpha=0.5)
plt.show()


# In[8]:


# 使用 Seaborn 画柱状图

# 创建图形对象，并设置大小
plt.figure(figsize=(6, 4))

# 设置 Seaborn 样式
# sns.set_style("whitegrid")

# 利用barplot绘制
sns.barplot(x='category', y='value', data=data, color='steelblue')

# 设置标题和坐标轴标签
plt.xlabel('Category')
plt.ylabel('Value')
plt.title('Single Bar Chart')

# 显示图表
plt.show()


# In[5]:


# 利用pyecharts绘制

# 创建柱状图对象
bar = (
    Bar()
    .add_xaxis(data["category"].tolist())  # 设置X轴数据
    .add_yaxis("Value", data["value"].tolist(), color="steelblue")  # 设置Y轴数据，并指定颜色
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Single Bar Chart"),  # 设置标题
        xaxis_opts=opts.AxisOpts(name="Category"),  # 设置X轴标签
        yaxis_opts=opts.AxisOpts(name="Value"),  # 设置Y轴标签
        toolbox_opts=opts.ToolboxOpts(),  # 添加工具栏
        legend_opts=opts.LegendOpts(is_show=False),  # 隐藏图例
    )
)

# 渲染图表
# bar.render_notebook()  # 在Jupyter Notebook中渲染，如果由于版本兼容问题出现问题，可以采用下述代码保存到html中
bar.render("basic_bar_chart.html") # 生成HTML文件


# In[6]:


# 利用plotly创建柱状图

fig = px.bar(
    data, 
    x="category", 
    y="value", 
    title="Single Bar Chart", 
    labels={"category": "Category", "value": "Value"}, 
    color_discrete_sequence=["steelblue"]  # 设置柱状颜色
)

# 显示图表
fig.show()


# ### 分组柱状图
# 创建包含5个类别和4个对应的数值列的分组柱状图

# In[13]:


# 自定义一个包含多列数据的数据框DataFrame，包含类别和多列值
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value1': [10, 15, 7, 12, 8], 'value2': [6, 9, 5, 8, 4],
                     'value3': [3, 5, 2, 4, 6], 'value4': [9, 6, 8, 3, 5]})
# 查看数据框
data


# In[14]:


# 使用DataFrame的plot方法绘制分组柱状图
'''
方法说明:
dataframe.plot(x, kind, figsize)
    x: 标签
    kind: 绘制的图表类型 (e.g., bar)
'''
data.plot(x='category', kind='bar', figsize=(6, 4))

# 指定x轴为'category'列，图表类型为'bar'，图形大小为(6,4)
plt.xlabel('Category')  # 设置x轴标签
plt.xticks(rotation=0)  # 旋转x轴文本，使其水平显示
plt.ylabel('Value')  # 设置y轴标签
plt.title('Grouped Bar Chart')  # 设置图表标题
plt.legend(title='Values')  # 添加图例，并设置标题为'Values'
plt.show()



# In[9]:


# 数据转换（将宽数据变成长数据，以便 Seaborn 处理）
'''
方法说明：
pandas.melt() 函数可以实现将 “宽数据” → “长数据”的一种列转行变换
这一种格式其中一个或多个列是标识符变量（id_vars），而所有其他列，被视为测量变量（value_vars），被“解开”到行轴，只留下两个非标识符列，'variable'和'value'。
    # id_vars -- 不需要被转换的列名
    # var_name、value_name -- 自定义设置对应的列名
'''
data_melted = data.melt(id_vars="category", var_name="Group", value_name="Value")

data_melted


# In[10]:


# 使用seaborn的barplot方法绘制分组柱状图

# 设置 Seaborn 样式
sns.set_style("whitegrid")

# 创建图形对象
plt.figure(figsize=(6, 4))

# 使用 Seaborn 绘制分组柱状图
'''

'''
sns.barplot(x="category", y="Value", hue="Group", data=data_melted)

# 设置标题和坐标轴标签
plt.xlabel("Category")
plt.ylabel("Value")
plt.title("Grouped Bar Chart")

# 旋转 x 轴标签
plt.xticks(rotation=0)

# 显示图例
plt.legend(title="Group")

# 显示图表
plt.show()


# In[11]:


# 利用pyecharts的Bar绘制分组柱状图

# 提取类别和数据
categories = data["category"].tolist()
values = [data[col].tolist() for col in data.columns[1:]]  # 依次提取 value1 - value4

# 创建 Pyecharts 柱状图
bar = (
    Bar()
    .add_xaxis(categories)
    .add_yaxis("value1", values[0])
    .add_yaxis("value2", values[1])
    .add_yaxis("value3", values[2])
    .add_yaxis("value4", values[3])
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Grouped Bar Chart with Pyecharts"),
        xaxis_opts=opts.AxisOpts(name="Category"),
        yaxis_opts=opts.AxisOpts(name="Value"),
    )
)

# 渲染图表
# bar.render_notebook()  # 在Jupyter Notebook中渲染，如果由于版本兼容问题出现问题，可以采用下述代码保存到html中
bar.render("grouped_bar_chart.html") # 生成HTML文件


# In[12]:


# 利用plotly.express的bar绘制分组柱状图

fig = px.bar(
    data,
    x="category",
    y=["value1", "value2", "value3","value4"],
    barmode="group",  # 关键参数：分组模式
    title="Grouped Bar Chart with plotly",
)

# 显示图表
fig.show()


# ### 堆积柱状图
# 创建包含5个类别和4个对应的数值列的堆积柱状图

# In[15]:


# 自定义一个包含多列数据的数据框DataFrame，包含类别和多列值
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value1': [10, 15, 7, 12, 8], 'value2': [6, 9, 5, 8, 4],
                     'value3': [3, 5, 2, 4, 6], 'value4': [9, 6, 8, 3, 5]})
# 查看数据框
data


# In[16]:


# 将'category'列设置为索引，并创建堆积柱状图

# 使用DataFrame的plot方法绘制堆积柱状图
data.set_index('category').plot(kind='bar', stacked=True, figsize=(6, 4))
# 设置索引为'category'列，图表类型为'bar'，堆积模式为True，图形大小为(6,4)

plt.xlabel('Category')  # 设置x轴标签
plt.ylabel('Value')  # 设置y轴标签
plt.title('Stacked Bar Chart')  # 设置图表标题
plt.xticks(rotation=0)  # 旋转x轴文本，使其水平显示

# 添加图例，并设置标题为'Values'，并放置在图的右侧
plt.legend(title='Values', loc='center left', bbox_to_anchor=(1, 0.5))
plt.show()


# In[18]:


# 利用seaborn绘制（错误代码）——柱子会重叠而非堆叠
# 将数据转换为长格式
df_melted = data.melt(id_vars='category', var_name='Values', value_name='Value')
# print(df_melted)
# 使用seaborn设置样式
sns.set(style="whitegrid")

# 创建堆积柱状图
plt.figure(figsize=(6, 4))
sns.barplot(data=df_melted, x='category', y='Value', hue='Values', dodge=False)

plt.xlabel('Category')
plt.ylabel('Value')
plt.title('Stacked Bar Chart with Seaborn')
plt.legend(title='Values', bbox_to_anchor=(1.05, 1), loc='upper left')

plt.tight_layout()
plt.show()


# In[21]:


# 使用pyecharts进行绘制
# 创建柱状图
bar = Bar()

# 添加x轴数据
bar.add_xaxis(categories)

# 添加系列数据，并设置stack参数为相同的值以实现堆叠
bar.add_yaxis("Value1", data['value1'].tolist(), stack="stack1")
bar.add_yaxis("Value2", data['value2'].tolist(), stack="stack1")
bar.add_yaxis("Value3", data['value3'].tolist(), stack="stack1")
bar.add_yaxis("Value4", data['value4'].tolist(), stack="stack1")

# 设置全局配置
bar.set_global_opts(
    title_opts=opts.TitleOpts(title="Stacked Bar Chart with Pyecharts"),
    xaxis_opts=opts.AxisOpts(name="Category"),
    yaxis_opts=opts.AxisOpts(name="Value"),
    legend_opts=opts.LegendOpts(pos_right="right")
)

# 渲染图表
# bar.render_notebook()  # 在Jupyter Notebook中渲染，如果由于版本兼容问题出现问题，可以采用下述代码保存到html中
bar.render("stacked_bar_chart.html") # 生成HTML文件


# In[22]:


# 利用plotly.express的bar绘制堆叠柱状图

fig = px.bar(
    data,
    x="category",
    y=["value1", "value2", "value3","value4"],
    barmode="stack",  # 关键参数：堆叠模式
    title="Stacked Bar Chart with plotly",
)

# 显示图表
fig.show()


# ### 百分比柱状图
# 创建包含5个类别和4个对应的数值列的百分比柱状图

# In[23]:


# 自定义一个包含多列数据的数据框DataFrame，包含类别和多列值
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value1': [10.0, 15.0, 7.0, 12.0, 8.0], 'value2': [6.0, 9.0, 5.0, 8.0, 4.0],
                     'value3': [3.0, 5.0, 2.0, 4.0, 6.0], 'value4': [9.0, 6.0, 8.0, 3.0, 5.0]})
# 查看数据框
data


# In[24]:


# 续上例，创建百分比柱状状图（对数据进行预处理）
# 复制数据集到新的DataFrame以便进行百分比计算
data_percentage = data.copy()

data_percentage.iloc[:, 1:] = data_percentage.iloc[:, 1:].astype(float)

# 计算每个数值列的百分比，除以每行的总和并乘以100
data_percentage.iloc[:, 1:] = data_percentage.iloc[:, 1:].div(
    data_percentage.iloc[:, 1:].sum(axis=1), axis=0) * 100

# print(data_percentage)
data_percentage


# In[ ]:


# 随堂练习


# ### 均值柱状图
# 创建包含5个类别和4个对应的数值列的均值柱状图。

# In[25]:


# 创建创建一个包含类别、值和标准差的DataFrame数据集
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value': [10, 15, 7, 12, 8], 'std': [1, 2, 1.5, 1.2, 2.5]})

# 计算每个类别的均值和标准差
mean_values = data['value']
std_values = data['std']


# In[26]:


# import matplotlib.cm as cm
# 生成颜色
# colors = [cm.tab10(i / len(data['category'])) for i in range(len(data['category']))]

# 创建均值柱状图
plt.figure(figsize=(6, 4))  # 设置图形大小
bars = plt.bar(data['category'], mean_values)

# 添加误差线
for bar, std in zip(bars, std_values): # 让我们可以遍历每个柱子及其对应的标准差
    plt.errorbar(bar.get_x() + bar.get_width() / 2, # 计算误差线的X轴位置
                 bar.get_height(), # 误差线的Y轴位置，设置在柱子顶部（即柱子的高度）
                 yerr=std, fmt='none', color='black', ecolor='gray', # 误差棒的长度（误差值）
                 capsize=5, capthick=2)  # 误差线样式

# 添加标题和标签
plt.xlabel('Category')  # x轴标签
plt.ylabel('Mean Value')  # y轴标签
plt.title('Mean Bar Chart with Error Bars')  # 图表标题

# 设置网格线
plt.grid(axis='both', linestyle='-', color='gray', alpha=0.5)

# 显示图表
plt.show()


# In[13]:


# 使用 seaborn 绘制带误差棒的柱状图

plt.figure(figsize=(6, 4))
ax = sns.barplot(data=data, x="category", y="value")  # 先画柱状图，不让 seaborn 自动计算误差棒

# 获取每个柱子的 x 轴位置
x_positions = range(len(data["category"]))

# 使用 matplotlib 添加误差棒
plt.errorbar(x_positions, data["value"], yerr=data["std"], fmt='none', ecolor='gray', capsize=5)

# 添加标题和标签
plt.xlabel('Category')
plt.ylabel('Mean Value')
plt.title('Mean Bar Chart with Error Bars')

# 显示图表
plt.show()


# In[27]:


# pyecharts 本身不直接支持误差棒（error bars），但我们可以通过叠加 line 图表来模拟误差棒。
# 效果并不理想

# 计算误差棒的上下界
upper = [v + s for v, s in zip(mean_values, std_values)]
lower = [v - s for v, s in zip(mean_values, std_values)]

# 创建柱状图
bar = (
    Bar()
    .add_xaxis(list(data['category']))
    .add_yaxis("Mean Value", list(mean_values), color="blue")
    .set_global_opts(title_opts=opts.TitleOpts(title="Bar Chart with Error Bars"))
)

# 创建误差棒（用 Line 模拟）
error_bar = (
    Line()
    .add_xaxis(data['category'])
    .add_yaxis("Upper Bound", upper, symbol="circle", linestyle_opts=opts.LineStyleOpts(width=1, type_="dashed"))
    .add_yaxis("Lower Bound", lower, symbol="circle", linestyle_opts=opts.LineStyleOpts(width=1, type_="dashed"))
)

# 叠加误差棒到柱状图
bar.overlap(error_bar)
# bar.render_notebook()  # 在 Jupyter Notebook 里渲染 (如果由于版本问题无法渲染，可以保存到html中）
bar.render('pyecharts_errorbar.html')  # 在 Jupyter Notebook 里渲染


# In[28]:


# plotly 原生支持误差棒，我们只需指定 error_y 参数。
# 绘制带误差棒的柱状图
fig = px.bar(data, x="category", y="value", error_y="std",
             title="Bar Chart with Error Bars",
             labels={"value": "Mean Value", "category": "Category"},
             color_discrete_sequence=["blue"])  # 颜色设置

# 显示图表
fig.show()


# ### 不等宽柱状图
# 创建包含5个类别和5个对应的值及宽度值的不等宽柱状图

# In[77]:


# 创建数据集
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value': [10, 15, 7, 12, 8],
                     'width': [0.8, 0.4, 1.0, 0.5, 0.9]})
print("数据结构：")
print(data) 


# In[78]:


# 自定义颜色列表，每个柱子使用不同的配色
colors = ['red', 'green', 'blue', 'orange', 'purple']
# 创建不等宽柱状图
plt.figure(figsize=(6, 4))
for i in range(len(data)): # 遍历五个类别
    plt.bar(data['category'][i], data['value'][i],
            width=data['width'][i], color=colors[i])

# 添加标题和标签
plt.xlabel('Category')
plt.ylabel('Value')
plt.title('Unequal Width Bar Chart')

# 设置网格线
plt.grid(axis='both', linestyle='-', color='gray', alpha=0.5)
plt.show()


# #### seaborn 需要用 matplotlib 叠加
# #### pyecharts没有直接修改宽度的方法
# #### plotly需要用基本的plotly.graph_objects方法

# ### 有序柱状图
# 创建有序柱状图

# In[2]:


import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns


# In[5]:


# 自定义一个包含多列数据的数据框DataFrame，包含类别和多列值
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value': [10.0, 15.0, 7.0, 12.0, 8.0]})
# 查看数据框
data


# In[3]:


# 按 value 排序
data_sorted = data.sort_values(by='value', ascending=True)

# 添加颜色列（红色表示负数，蓝色正数）
data_sorted['color'] = data_sorted['value'].apply(lambda x: 'red' if x < 0 else 'blue')

# 绘图
plt.figure(figsize=(6, 4))
sns.barplot(y='category', x='value', data=data_sorted, palette=data_sorted['color'].tolist())
plt.axvline(x=0, color='black', linewidth=1)  # 中心线
plt.xlabel('Value')
plt.ylabel('Category')
plt.title('Bar Chart Sorted by Value')
plt.tight_layout()
plt.show()


# ### 条形图
# 使用水平或垂直的矩形条（条形）来表示数据

# In[29]:


# 自定义数据集
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value': [10, 15, 7, 12, 8]})  # 创建一个包含类别和值的DataFrame
# 查看数据结构
data


# In[30]:


# 利用matplotlib创建条形图

plt.figure(figsize=(6, 4))  # 创建图形对象，并设置图形大小
# bar函数绘制柱状图
plt.barh(data['category'], data['value'], color='steelblue')
# 绘制柱状图，指定x轴为类别，y轴为值，柱状颜色为钢蓝色
plt.xlabel('Category')  # 设置x轴标签
plt.ylabel('Value')  # 设置y轴标签
plt.title('Single Bar Chart')  # 设置图表标题

# 添加网格线，采用虚线，设置为灰色，透明度为0.5
plt.grid(linestyle='-', color='gray', alpha=0.5)
plt.show()


# In[31]:


# 使用 Seaborn 画柱状图

# 创建图形对象，并设置大小
plt.figure(figsize=(6, 4))

# 设置 Seaborn 样式
# sns.set_style("whitegrid")

# 利用barplot绘制
sns.barplot(y='category', x='value', data=data, color='steelblue')

# 设置标题和坐标轴标签
plt.xlabel('Value')
plt.ylabel('Category')
plt.title('Single Bar Chart')

# 显示图表
plt.show()


# In[32]:


# 利用pyecharts绘制

bar = (
    Bar()
    .add_xaxis(data["category"].tolist())  # 设置X轴数据
    .add_yaxis("Value", data["value"].tolist(), color="steelblue")  # 设置Y轴数据，并指定颜色
    .reversal_axis() # 关键：转换为横向条形图
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Single Bar Chart"),  # 设置标题
        xaxis_opts=opts.AxisOpts(name="Category"),  # 设置X轴标签
        yaxis_opts=opts.AxisOpts(name="Value"),  # 设置Y轴标签
        toolbox_opts=opts.ToolboxOpts(),  # 添加工具栏
        legend_opts=opts.LegendOpts(is_show=False),  # 隐藏图例
    )
)

# 渲染图表
# bar.render_notebook()  # 在Jupyter Notebook中渲染
bar.render("bar_chart.html") # 生成HTML文件


# In[33]:


# 利用plotly创建柱状图

fig = px.bar(
    data, 
    y="category", 
    x="value", 
    title="Single Bar Chart", 
    labels={"category": "Category", "value": "Value"}, 
    color_discrete_sequence=["steelblue"]  # 设置柱状颜色
)

# 显示图表
fig.show()


# #### 发散条形图
# 创建发散条形图数据
# 其中条形的颜色根据数据的标准化值而变化，正值使用绿色，负值使用红色。

# In[37]:


# 创建数据
data = pd.DataFrame({
    'category': ["A", "B", "C", "D", "E"],
    'value': [10, -15, 7, -12, 8]  # 既有正值也有负值
})
data


# In[38]:


# 设置颜色：正值用蓝色，负值用红色
colors = ['steelblue' if v >= 0 else 'red' for v in data['value']]

# 创建横向发散条形图
plt.figure(figsize=(6, 4))
plt.barh(data['category'], data['value'], color=colors)

# 添加网格线
plt.axvline(x=0, color='black', linewidth=1)  # 竖直中心线
plt.grid(axis='x', linestyle='--', alpha=0.5)

# 添加标签和标题
plt.xlabel('Value')
plt.ylabel('Category')
plt.title('Diverging Bar Chart')

# 显示图表
plt.show()


# In[44]:


# 使用seaborn绘制
# 添加颜色列
data['color'] = data['value'].apply(lambda x: 'blue' if x >= 0 else 'red')

# 创建图形对象
plt.figure(figsize=(6, 4))

# 使用 seaborn.barplot 绘制发散条形图
sns.barplot(y='category', x='value', data=data, palette=data['color'].tolist())


# 添加竖直中心线
plt.axvline(x=0, color='black', linewidth=1)

# 添加标签和标题
plt.xlabel('Value')
plt.ylabel('Category')
plt.title('Diverging Bar Chart')

# 显示图表
plt.show()


# In[40]:


# 使用pyecharts绘制

# 创建横向发散条形图
bar = (
    Bar()
    .add_xaxis(data["category"].tolist())  
    .add_yaxis("Value", data["value"].tolist(), color="auto")  # 自动匹配颜色
    .reversal_axis()  # 横向条形图
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Diverging Bar Chart"),
        xaxis_opts=opts.AxisOpts(name="Value"),
        yaxis_opts=opts.AxisOpts(name="Category"),
    )
)

# 渲染图表
# bar.render_notebook()  # 或者 bar.render("diverging_bar_chart.html")
bar.render("diverging_bar_chart.html")


# In[41]:


# 使用plotly.express绘制

# 创建发散条形图
fig = px.bar(
    data, 
    x='value', 
    y='category', 
    title='Diverging Bar Chart',
    labels={'category': 'Category', 'value': 'Value'},
    color='value',  # 按值变化颜色
    color_continuous_scale=['red', 'steelblue']  # 颜色渐变
)

# 显示图表
fig.show()


# ### 雷达图

# In[34]:


# 设置数据
df = pd.DataFrame({'group': ['A', 'B', 'C', 'D', 'E'],  # 五组数据
                   'var1': [38, 1.5, 30, 4, 29], 'var2': [29, 10, 9, 34, 18],
                   'var3': [8, 39, 23, 24, 19], 'var4': [7, 31, 33, 14, 33],
                   'var5': [28, 15, 32, 14, 22]})  # 每组数据的变量
print(df)


# In[37]:


# 利用matplotlib绘制

# 获取变量列表（除了group以外的列名）
categories = list(df.columns[1:])
N = len(categories)

# 通过复制第1个值来闭合雷达图
# 绘制A的雷达图

# df.loc[0]获取第一行数据，然后去掉第一列名（group）
# .values.flatten().tolist()：将 Series 转换为一个一维数组，然后转化为列表 values
values = df.loc[0].drop('group').values.flatten().tolist()
# 为了使雷达图闭合，需要将第一个值再次添加到列表末尾，这样绘制的图形才会形成闭环。
values += values[:1] # 38 29 8 7 28 38

# 计算每个变量的角度
# angles 列表存储每个变量对应的角度。由于雷达图是圆形的，角度从 0 到 2π 之间分布
# 根据变量数量 N 计算每个变量在圆上的角度。n 是当前变量的索引，N 是总的变量数量，2 * np.pi 是完整圆的角度。
# 为了闭合雷达图，添加第一个角度值到列表末尾，这样图形在绘制时能够回到起点。
angles = [n / float(N) * 2 * np.pi for n in range(N)]
angles += angles[:1]

# 初始化雷达图
# 创建一个 matplotlib 图形对象 fig 和坐标轴对象 ax，并设置图形大小为 4x4 英寸。
# 指定 subplot 为极坐标系统，这使得坐标轴可以表示为极坐标形式（即圆形坐标系），适合绘制雷达图
fig, ax = plt.subplots(figsize=(4, 4), subplot_kw=dict(polar=True))
# 绘制每个变量的轴，并添加标签
# 将每个角度值对应到类别（变量名）上，angles[:-1] 去掉了最后一个角度（因为雷达图已经闭合），categories 是变量名列表。
plt.xticks(angles[:-1], categories, color='grey', size=8)

# 添加y轴标签
# 设置 y 轴标签的位置，0 表示标签从图的顶部开始
ax.set_rlabel_position(0)
# 设置 y 轴的刻度值（10, 20, 30）以及对应的标签。
plt.yticks([10, 20, 30], ["10", "20", "30"], color="grey", size=7)
# 设置 y 轴的范围，即雷达图的值范围在 0 到 40 之间
plt.ylim(0, 40)

# 关键代码
# 绘制数据：绘制雷达图的边框，即每个变量的值在极坐标系统中的连线。
ax.plot(angles, values, linewidth=1, linestyle='solid')  
# 填充区域：在雷达图的区域内填充颜色，'b' 表示蓝色，alpha=0.1 设置透明度为 0.1（即图形的填充颜色比较浅）
ax.fill(angles, values, 'b', alpha=0.1)  
plt.show()


# In[41]:


# 利用pyecharts绘制
# 提取A组数据
a_data = df[df['group'] == 'A'].iloc[:, 1:].values.tolist()[0]
print(a_data)

# 绘制雷达图
radar = (
    Radar()
    .add_schema(schema=[
        {"name": "var1", "max": 40},
        {"name": "var2", "max": 40},
        {"name": "var3", "max": 40},
        {"name": "var4", "max": 40},
        {"name": "var5", "max": 40},
    ]) # 雷达图的维度（变量名）和最大值（用于标准化）
    .add("A组数据", [a_data], color="#FF4500", areastyle_opts=opts.AreaStyleOpts(opacity=0.3))
    .set_series_opts(label_opts=opts.LabelOpts(is_show=True)) # # 显示数值标签
    .set_global_opts(title_opts=opts.TitleOpts(title="A组数据雷达图"))
)

radar.render("radar_chart_A.html")


# In[113]:


# 使用plotly实现
# 提取类别和数据
categories = list(df.columns[1:])
values = df.loc[0, categories].tolist() 

# 构建长格式数据以适配 plotly.express
# 'category': 将 categories 列表作为一列数据，表示雷达图的每个维度。
# 'value': 将之前提取的 values 列表作为数据列，表示每个维度的数值。
# 'group': 为所有数据指定一个组，通常在这里是 'A'，表明所有数据来自同一组。这个字段用于区分不同的系列数据，尽管在这里只有一个系列（group 始终为 'A'）。
long_df = pd.DataFrame({
    'category': categories,
    'value': values,
    'group': ['A'] * len(categories)
})
print(long_df)
# 绘制雷达图
fig = px.line_polar(long_df, # 传入之前构建的长格式数据框
                    r='value', # 指定每个维度的数值（即数据）在 r 轴上
                    theta='category', # 指定每个维度（即类别）在 theta（角度）轴上的位置
                    line_close=True, # 过将 line_close 设置为 True，确保雷达图的边缘闭合，即从最后一个点连接回第一个点，形成封闭的图形
                    title="Radar Chart with Plotly Express", 
                    color='group', # 使用 group 列来为不同的系列着色。尽管这里只有一个系列（A），这个参数还是用于确定分组颜色
                    markers=True # 在每个数据点上添加标记，便于查看数据点。
                   )

# 显示图表
fig.show()


# In[ ]:


# 随堂练习
# 绘制六边形战士！


# ### 径向柱状图

# In[47]:


# 创建数据集，第一列为数据集各项的名称。第二列各项的数值
df = pd.DataFrame(
    {'Name': ['Ding ' + str(i) for i in list(range(1, 51))],
     'Value': np.random.randint(low=10, high=100, size=50)}
)
df.head(3)  # 显示前 3 行数据，输出略


# In[118]:


# 使用matplotlib绘制
plt.figure(figsize=(20, 10))  # 设置图形大小
ax = plt.subplot(111, polar=True)  # 绘制极坐标轴
plt.axis('off')  # 移除网格线

upperLimit = 100  # 设置坐标轴的上限
lowerLimit = 30  # 设置坐标轴的下限
max_value = df['Value'].max()  # 计算数据集中的最大值

# 计算每个条形图的高度，它们是在新坐标系中将每个条目值转换的结果
# 数据集中的0转换为lowerLimit(30)，最大值被转换为upperLimit(100)
slope = (max_value - lowerLimit) / max_value
heights = slope * df.Value + lowerLimit

width = 2 * np.pi / len(df.index)  # 计算每个条形图的宽度，共有2*Pi=360°

# 计算每个条形图中心的角度：
indexes = list(range(1, len(df.index) + 1))
angles = [element * width for element in indexes]

# 绘制条形图
bars = ax.bar(x=angles, height=heights, width=width,
              bottom=lowerLimit, linewidth=2, edgecolor="white")


plt.show()


# In[49]:


# 利用plotly绘制
# 参数设置
upperLimit = 100
lowerLimit = 30
max_value = df['Value'].max()
slope = (upperLimit - lowerLimit) / max_value
df["ScaledValue"] = slope * df['Value'] + lowerLimit
df["Angle"] = np.linspace(0, 360, len(df), endpoint=False)  # 均匀分布角度

# 创建径向柱状图
fig = px.bar_polar(
    df,
    r="ScaledValue",
    theta="Angle",
    color="Name",
    template="plotly_dark",
    color_discrete_sequence=px.colors.qualitative.Pastel,
    range_r=[lowerLimit, upperLimit],
    direction="clockwise",
    start_angle=0
)

# 调整布局
# fig.update_layout(
#     title="径向柱状图 (Plotly)",
#     polar=dict(
#         radialaxis=dict(visible=True, range=[lowerLimit, upperLimit]),
#         angularaxis=dict(showticklabels=True, ticks="outside")
#     ),
#     showlegend=True
# )

fig.show()  # 显示图表（Jupyter/浏览器）
# fig.write_html("radial_bar_plotly.html")  # 保存为HTML


# In[ ]:




