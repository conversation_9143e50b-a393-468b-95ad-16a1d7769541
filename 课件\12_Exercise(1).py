#!/usr/bin/env python
# coding: utf-8

# # 12 随堂练习参考答案

# In[11]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Bar
import random
import plotly.express as px
import plotly.graph_objects as go

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号


# # 随堂练习（1）

# In[3]:


import pandas as pd
import matplotlib.pyplot as plt

# 读取Excel文件
file_path = "exercise_1.xlsx"

# 读取Sheet1绘制直方图（连续数据分布）
df_sheet1 = pd.read_excel(file_path, sheet_name="Sheet1")
plt.figure(figsize=(8, 4))
plt.hist(df_sheet1["scores"], bins=5, edgecolor="black", alpha=0.7)
plt.title("Sheet1: 成绩分布直方图")
plt.xlabel("分数")
plt.ylabel("频数")
plt.grid(True)
plt.show()
df_sheet1


# In[4]:


# 读取Sheet2绘制条形图（离散分类统计）
df_sheet2 = pd.read_excel(file_path, sheet_name="Sheet2")
plt.figure(figsize=(8, 4))
# 传递x（score），传递y（频数）
plt.bar(df_sheet2["score"], df_sheet2["count"], color="skyblue", edgecolor="black")
plt.title("Sheet2: 成绩分段统计条形图")
plt.xlabel("分数段")
plt.ylabel("人数")
plt.grid(axis="y")
plt.show()



# In[5]:


# 读取Sheet3并观察数据
df_sheet3 = pd.read_excel(file_path, sheet_name="Sheet3")
df_sheet3


# In[8]:


# 假如用直方图，直接传递数据进去
plt.figure(figsize=(8, 4))
plt.hist(df_sheet3["grade"], bins=5, edgecolor="black", alpha=0.7)
plt.title("Sheet3: 成绩等级直方图")
plt.xlabel("等级")
plt.ylabel("频数")
plt.grid(True)
plt.show()


# In[9]:


# 条形图——需要先处理数据

grade_counts = df_sheet3["grade"].value_counts().sort_index()
grade_counts


# In[10]:


plt.figure(figsize=(8, 4))
plt.bar(grade_counts.index, grade_counts.values, color="lightgreen", edgecolor="black")
plt.title("Sheet3: 成绩等级条形图")
plt.xlabel("等级")
plt.ylabel("人数")
plt.grid(axis="y")
plt.show()


# # 随堂练习（2）

# In[2]:


# 生成数据
data = np.random.normal(loc=0, scale=1, size=1000)

# 不同带宽的核密度图
sns.kdeplot(x=data, fill=True, color='green', bw_adjust=0.5)
sns.kdeplot(x=data, fill=True, color='black', bw_adjust=1)
sns.kdeplot(x=data, fill=True, color='red', bw_adjust=4)

plt.title("不同带宽的核密度图")
plt.xlabel("值")
plt.ylabel("密度")
plt.show()


# In[3]:


# 生成数据
data = np.random.normal(loc=0, scale=1, size=1000)

# 绘制直方图 + KDE 曲线
sns.histplot(data, stat='density', bins=30, color='lightgray', edgecolor='black')
sns.kdeplot(x=data, fill=True, color='green', bw_adjust=1)

plt.title("直方图 + 核密度图 (Seaborn)")
plt.xlabel("值")
plt.ylabel("密度")
plt.show()


# # 随堂练习（3）

# In[12]:


# 读取数据
import pandas as pd

df = pd.read_csv("starbucks.csv")
df = df[['Beverage_category', 'Calories']].dropna()
df


# In[13]:


# 使用matplotlib绘制
# plt.boxplot(data)
# plt.title("Boxplot using Matplotlib")
# plt.ylabel("Value")
# plt.grid(True)
# plt.show()

plt.figure(figsize=(10, 6))
categories = df['Beverage_category'].unique()
data = [df[df['Beverage_category'] == cat]['Calories'] for cat in categories]

plt.boxplot(data, labels=categories)

plt.xticks(rotation=45)
plt.title('Calories by Beverage Category (Matplotlib)')
plt.ylabel('Calories')
plt.tight_layout()
plt.show()


# In[14]:


# 使用seaborn绘制
# sns.boxplot(y=data, color='skyblue')
# plt.title("Boxplot using Seaborn")
# plt.ylabel("Value")
# plt.grid(True)
# plt.show()

plt.figure(figsize=(10, 6))
sns.boxplot(x='Beverage_category', y='Calories', data=df)
plt.xticks(rotation=45)
plt.title('Calories by Beverage Category (Seaborn)')
plt.tight_layout()
plt.show()


# In[9]:


# 使用pyecharts绘制
# 需要对传入数据进行预处理,要求嵌套列表
# data_group = [data.tolist()]
# print(data_group)


# boxplot = Boxplot()

# # 输出： min, Q1, median, Q3, max
# box_data = boxplot.prepare_data(data_group)  

# print(box_data)

# boxplot.add_xaxis(["Group A"])
# boxplot.add_yaxis("Boxplot", box_data)
# boxplot.set_global_opts(title_opts=opts.TitleOpts(title="Boxplot using Pyecharts"))
# boxplot.render_notebook()
# boxplot.render("boxplot_pyecharts.html")

from pyecharts.charts import Boxplot
# 准备数据
categories = df['Beverage_category'].unique().tolist()
box_data = [df[df['Beverage_category'] == cat]['Calories'].tolist() for cat in categories]

boxplot = Boxplot()
boxplot.add_xaxis(categories)
boxplot.add_yaxis("Calories", boxplot.prepare_data(box_data))
boxplot.set_global_opts(title_opts=opts.TitleOpts(title="Calories by Beverage Category (Pyecharts)"),
                        xaxis_opts=opts.AxisOpts(axislabel_opts={"rotate": 45}),
                        toolbox_opts=opts.ToolboxOpts())
boxplot.render("boxplot_pyecharts_exercise.html") 


# In[26]:


# 使用plotly.express绘制
# fig = px.box(y=data, title="Boxplot using Plotly")
# fig.show()

fig = px.box(df, x='Beverage_category', y='Calories',
             title='Calories by Beverage Category (Plotly)')
fig.update_layout(xaxis_tickangle=-45)
fig.show()


# # 随堂练习（4）

# In[31]:


import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 生成随机数据
data = np.random.randn(1000)

# 创建绘图区域
plt.figure(figsize=(8, 6))

# 绘制累积分布曲线 (CDF)
plt.hist(data, bins=30, cumulative=True, color='blue', edgecolor='black', alpha=0.5, label='CDF', density=True)

# 绘制核密度估计曲线 (KDE)
sns.kdeplot(data, color='red', label='KDE', linewidth=2, fill=True)

# 添加标题和标签
plt.title('CDF and KDE in One Plot')
plt.xlabel('Value')
plt.ylabel('Density / Cumulative Probability')

# 显示图例
plt.legend()

# 显示图表
plt.show()


# In[ ]:




