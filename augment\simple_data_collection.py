"""
简化版B站数据收集程序
用于测试和收集基础数据
"""

import requests
import json
import pandas as pd
import time
import random


def get_bilibili_popular_data():
    """
    获取B站热门视频数据
    """
    print("开始获取B站热门视频数据...")
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.bilibili.com/',
    }
    
    # B站热门视频API
    url = 'https://api.bilibili.com/x/web-interface/popular'
    params = {
        'ps': 50,  # 获取50个视频
        'pn': 1
    }
    
    try:
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        if data['code'] == 0:
            videos = data['data']['list']
            print(f"成功获取 {len(videos)} 条热门视频数据")
            return videos
        else:
            print(f"API返回错误: {data['message']}")
            return []
            
    except Exception as e:
        print(f"获取数据失败: {e}")
        return []


def extract_video_info(videos):
    """
    提取视频信息
    """
    print("正在提取视频信息...")
    
    extracted_data = []
    
    for video in videos:
        try:
            video_info = {
                'bvid': video.get('bvid', ''),
                'aid': video.get('aid', 0),
                'title': video.get('title', ''),
                'desc': video.get('desc', ''),
                'duration': video.get('duration', 0),
                'pubdate': video.get('pubdate', 0),
                'view': video.get('stat', {}).get('view', 0),
                'danmaku': video.get('stat', {}).get('danmaku', 0),
                'reply': video.get('stat', {}).get('reply', 0),
                'favorite': video.get('stat', {}).get('favorite', 0),
                'coin': video.get('stat', {}).get('coin', 0),
                'share': video.get('stat', {}).get('share', 0),
                'like': video.get('stat', {}).get('like', 0),
                'owner_name': video.get('owner', {}).get('name', ''),
                'owner_mid': video.get('owner', {}).get('mid', 0),
                'tname': video.get('tname', ''),
                'tid': video.get('tid', 0),
            }
            extracted_data.append(video_info)
            
        except Exception as e:
            print(f"提取视频信息失败: {e}")
            continue
    
    return pd.DataFrame(extracted_data)


def create_sample_data():
    """
    创建示例数据用于演示
    """
    print("创建示例数据...")
    
    # 模拟真实的B站视频数据
    sample_data = []
    
    # 不同分区的示例数据
    categories = [
        ("动画", 1), ("音乐", 3), ("游戏", 4), ("科技", 36), ("生活", 160),
        ("美食", 211), ("娱乐", 5), ("影视", 181), ("舞蹈", 129), ("搞笑", 138),
        ("体育", 75), ("电影", 23), ("电视剧", 11), ("纪录片", 177), ("国创", 168)
    ]
    
    for i in range(200):  # 创建200条示例数据
        category = random.choice(categories)
        
        # 模拟不同热度的视频
        if i < 20:  # 热门视频
            view_base = random.randint(500000, 5000000)
        elif i < 50:  # 中等热度
            view_base = random.randint(50000, 500000)
        else:  # 普通视频
            view_base = random.randint(1000, 50000)
        
        # 计算其他互动数据（基于播放量的比例）
        like = int(view_base * random.uniform(0.02, 0.15))
        coin = int(view_base * random.uniform(0.005, 0.05))
        favorite = int(view_base * random.uniform(0.01, 0.08))
        share = int(view_base * random.uniform(0.001, 0.02))
        reply = int(view_base * random.uniform(0.001, 0.05))
        danmaku = int(view_base * random.uniform(0.001, 0.1))
        
        video_data = {
            'bvid': f'BV{random.randint(100000000, 999999999)}',
            'aid': random.randint(100000000, 999999999),
            'title': f'示例视频标题_{i+1}',
            'desc': f'这是第{i+1}个示例视频的描述',
            'duration': random.randint(30, 3600),  # 30秒到1小时
            'pubdate': random.randint(1640995200, 1750000000),  # 2022-2025年的时间戳
            'view': view_base,
            'danmaku': danmaku,
            'reply': reply,
            'favorite': favorite,
            'coin': coin,
            'share': share,
            'like': like,
            'owner_name': f'UP主_{random.randint(1, 1000)}',
            'owner_mid': random.randint(1000000, 9999999),
            'tname': category[0],
            'tid': category[1],
        }
        
        sample_data.append(video_data)
    
    return pd.DataFrame(sample_data)


def main():
    """
    主函数
    """
    print("=== B站数据收集程序 ===")
    
    # 尝试获取真实数据
    videos = get_bilibili_popular_data()
    
    if videos:
        # 提取真实数据
        df_real = extract_video_info(videos)
        print(f"获取到 {len(df_real)} 条真实数据")
        
        # 创建示例数据补充
        df_sample = create_sample_data()
        print(f"创建了 {len(df_sample)} 条示例数据")
        
        # 合并数据
        df_combined = pd.concat([df_real, df_sample], ignore_index=True)
        
    else:
        # 如果无法获取真实数据，使用示例数据
        print("无法获取真实数据，使用示例数据进行演示")
        df_combined = create_sample_data()
    
    # 保存数据
    df_combined.to_csv('bilibili_data.csv', index=False, encoding='utf-8-sig')
    print(f"数据已保存到 bilibili_data.csv，共 {len(df_combined)} 条记录")
    
    # 显示基本统计信息
    print("\n=== 数据概览 ===")
    print(f"总记录数: {len(df_combined)}")
    print(f"平均播放量: {df_combined['view'].mean():.0f}")
    print(f"平均点赞数: {df_combined['like'].mean():.0f}")
    print(f"平均投币数: {df_combined['coin'].mean():.0f}")
    
    print("\n=== 分区分布 ===")
    category_counts = df_combined['tname'].value_counts().head(10)
    for category, count in category_counts.items():
        print(f"{category}: {count} 条")
    
    return df_combined


if __name__ == "__main__":
    try:
        data = main()
        print("\n数据收集完成！可以开始数据可视化分析。")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
