"""
数据预处理模块
用于清洗、转换和处理B站视频数据
"""

import pandas as pd
import numpy as np
from datetime import datetime
import re
from typing import Dict, List, Optional


class DataProcessor:
    """
    数据预处理类
    
    主要功能：
    - 数据清洗
    - 缺失值处理
    - 数据类型转换
    - 特征工程
    """
    
    def __init__(self):
        """初始化数据处理器"""
        self.processed_data = None
    
    def load_data(self, file_path: str) -> pd.DataFrame:
        """
        加载数据文件
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            加载的DataFrame
        """
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            elif file_path.endswith('.json'):
                df = pd.read_json(file_path)
            elif file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
            else:
                raise ValueError("不支持的文件格式")
            
            print(f"成功加载数据，共 {len(df)} 行，{len(df.columns)} 列")
            return df
            
        except Exception as e:
            print(f"加载数据失败: {e}")
            return pd.DataFrame()
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        数据清洗
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        print("开始数据清洗...")
        
        # 创建副本避免修改原数据
        cleaned_df = df.copy()
        
        # 1. 去除重复数据
        initial_count = len(cleaned_df)
        cleaned_df = cleaned_df.drop_duplicates(subset=['bvid'], keep='first')
        duplicate_count = initial_count - len(cleaned_df)
        print(f"去除重复数据: {duplicate_count} 条")
        
        # 2. 处理标题中的HTML标签和特殊字符
        if 'title' in cleaned_df.columns:
            cleaned_df['title'] = cleaned_df['title'].apply(self._clean_title)
        
        # 3. 处理描述字段
        if 'desc' in cleaned_df.columns:
            cleaned_df['desc'] = cleaned_df['desc'].apply(self._clean_description)
        
        # 4. 处理数值字段的异常值
        numeric_columns = ['view', 'danmaku', 'reply', 'favorite', 'coin', 'share', 'like', 'duration']
        for col in numeric_columns:
            if col in cleaned_df.columns:
                cleaned_df[col] = pd.to_numeric(cleaned_df[col], errors='coerce')
                # 将负值设为0
                cleaned_df[col] = cleaned_df[col].clip(lower=0)
        
        # 5. 处理时间字段
        if 'pubdate' in cleaned_df.columns:
            cleaned_df['pubdate'] = pd.to_numeric(cleaned_df['pubdate'], errors='coerce')
            cleaned_df['publish_time'] = pd.to_datetime(cleaned_df['pubdate'], unit='s', errors='coerce')
        
        print(f"数据清洗完成，剩余 {len(cleaned_df)} 条数据")
        return cleaned_df
    
    def handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        处理缺失值
        
        Args:
            df: 数据DataFrame
            
        Returns:
            处理缺失值后的DataFrame
        """
        print("开始处理缺失值...")
        
        processed_df = df.copy()
        
        # 检查缺失值情况
        missing_info = processed_df.isnull().sum()
        print("缺失值统计:")
        for col, count in missing_info.items():
            if count > 0:
                print(f"  {col}: {count} ({count/len(processed_df)*100:.2f}%)")
        
        # 1. 字符串字段用空字符串填充
        string_columns = ['title', 'desc', 'owner_name', 'tname']
        for col in string_columns:
            if col in processed_df.columns:
                processed_df[col] = processed_df[col].fillna('')
        
        # 2. 数值字段用0填充
        numeric_columns = ['view', 'danmaku', 'reply', 'favorite', 'coin', 'share', 'like', 'duration', 'aid', 'owner_mid', 'tid']
        for col in numeric_columns:
            if col in processed_df.columns:
                processed_df[col] = processed_df[col].fillna(0)
        
        # 3. 删除关键字段缺失的行
        key_columns = ['bvid']
        for col in key_columns:
            if col in processed_df.columns:
                processed_df = processed_df.dropna(subset=[col])
        
        print(f"缺失值处理完成，剩余 {len(processed_df)} 条数据")
        return processed_df
    
    def feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        特征工程
        
        Args:
            df: 数据DataFrame
            
        Returns:
            添加新特征后的DataFrame
        """
        print("开始特征工程...")
        
        featured_df = df.copy()
        
        # 1. 计算互动率相关指标
        if all(col in featured_df.columns for col in ['view', 'like', 'coin', 'favorite', 'share', 'reply']):
            # 点赞率
            featured_df['like_rate'] = featured_df['like'] / (featured_df['view'] + 1)
            # 投币率
            featured_df['coin_rate'] = featured_df['coin'] / (featured_df['view'] + 1)
            # 收藏率
            featured_df['favorite_rate'] = featured_df['favorite'] / (featured_df['view'] + 1)
            # 分享率
            featured_df['share_rate'] = featured_df['share'] / (featured_df['view'] + 1)
            # 评论率
            featured_df['reply_rate'] = featured_df['reply'] / (featured_df['view'] + 1)
            # 总互动数
            featured_df['total_interaction'] = (featured_df['like'] + featured_df['coin'] + 
                                              featured_df['favorite'] + featured_df['share'] + featured_df['reply'])
            # 互动率
            featured_df['interaction_rate'] = featured_df['total_interaction'] / (featured_df['view'] + 1)
        
        # 2. 时长分类
        if 'duration' in featured_df.columns:
            featured_df['duration_category'] = pd.cut(
                featured_df['duration'],
                bins=[0, 60, 300, 600, 1800, float('inf')],
                labels=['短视频(<1分钟)', '短视频(1-5分钟)', '中等(5-10分钟)', '长视频(10-30分钟)', '超长视频(>30分钟)']
            )
        
        # 3. 播放量分级
        if 'view' in featured_df.columns:
            featured_df['view_level'] = pd.cut(
                featured_df['view'],
                bins=[0, 1000, 10000, 100000, 1000000, float('inf')],
                labels=['低播放量', '一般播放量', '热门', '爆款', '现象级']
            )
        
        # 4. 发布时间特征
        if 'publish_time' in featured_df.columns:
            featured_df['publish_hour'] = featured_df['publish_time'].dt.hour
            featured_df['publish_day'] = featured_df['publish_time'].dt.day_name()
            featured_df['publish_month'] = featured_df['publish_time'].dt.month
            featured_df['publish_year'] = featured_df['publish_time'].dt.year
        
        # 5. 标题长度
        if 'title' in featured_df.columns:
            featured_df['title_length'] = featured_df['title'].str.len()
        
        print(f"特征工程完成，新增 {len(featured_df.columns) - len(df.columns)} 个特征")
        return featured_df
    
    def _clean_title(self, title: str) -> str:
        """清洗标题文本"""
        if pd.isna(title):
            return ""
        
        # 移除HTML标签
        title = re.sub(r'<[^>]+>', '', str(title))
        # 移除多余空格
        title = re.sub(r'\s+', ' ', title).strip()
        return title
    
    def _clean_description(self, desc: str) -> str:
        """清洗描述文本"""
        if pd.isna(desc):
            return ""
        
        # 移除HTML标签
        desc = re.sub(r'<[^>]+>', '', str(desc))
        # 移除多余空格和换行
        desc = re.sub(r'\s+', ' ', desc).strip()
        # 限制长度
        if len(desc) > 200:
            desc = desc[:200] + "..."
        return desc
    
    def get_data_summary(self, df: pd.DataFrame) -> Dict:
        """
        获取数据摘要信息
        
        Args:
            df: 数据DataFrame
            
        Returns:
            数据摘要字典
        """
        summary = {
            'total_records': len(df),
            'total_columns': len(df.columns),
            'missing_values': df.isnull().sum().sum(),
            'duplicate_records': df.duplicated().sum(),
            'data_types': df.dtypes.to_dict(),
            'numeric_summary': df.describe().to_dict() if len(df.select_dtypes(include=[np.number]).columns) > 0 else {},
        }
        
        return summary
    
    def process_pipeline(self, file_path: str, save_path: Optional[str] = None) -> pd.DataFrame:
        """
        完整的数据处理流水线
        
        Args:
            file_path: 输入文件路径
            save_path: 输出文件路径（可选）
            
        Returns:
            处理后的DataFrame
        """
        print("开始数据处理流水线...")
        
        # 1. 加载数据
        df = self.load_data(file_path)
        if df.empty:
            return df
        
        # 2. 数据清洗
        df = self.clean_data(df)
        
        # 3. 处理缺失值
        df = self.handle_missing_values(df)
        
        # 4. 特征工程
        df = self.feature_engineering(df)
        
        # 5. 保存处理后的数据
        if save_path:
            df.to_csv(save_path, index=False, encoding='utf-8-sig')
            print(f"处理后的数据已保存到: {save_path}")
        
        self.processed_data = df
        print("数据处理流水线完成!")
        
        return df


if __name__ == "__main__":
    # 测试代码
    processor = DataProcessor()
    
    # 处理数据
    processed_df = processor.process_pipeline(
        'bilibili_popular_videos.csv',
        'bilibili_processed_data.csv'
    )
    
    if not processed_df.empty:
        print("\n数据摘要:")
        summary = processor.get_data_summary(processed_df)
        for key, value in summary.items():
            if key != 'data_types' and key != 'numeric_summary':
                print(f"{key}: {value}")
