#!/usr/bin/env python
# coding: utf-8

# # 可视化举例
# ## 《数据可视化》
# ## 2024-2025-2 春季学期

# ### 1. matplotlib

# In[3]:


import matplotlib.pyplot as plt

# 数据
x = [1, 2, 3, 4, 5]
y = [1, 4, 9, 16, 25]

# 创建图形
plt.plot(x, y)

# 添加标题和标签
plt.title("Matplotlib Example")
plt.xlabel("X Axis")
plt.ylabel("Y Axis")

# 显示图形
plt.show()


# ### 2. Seaborn

# In[4]:


import seaborn as sns
import matplotlib.pyplot as plt

# 数据
tips = sns.load_dataset("tips")

# 创建散点图
sns.scatterplot(data=tips, x="total_bill", y="tip", hue="time")

# 添加标题
plt.title("Seaborn Example")

# 显示图形
plt.show()


# ### 3. plotly

# In[1]:


import plotly.graph_objects as go

# 数据
categories = ['A', 'B', 'C', 'D', 'E']
sales = [150, 200, 120, 180, 90]

# 创建柱状图
fig = go.Figure(data=[go.Bar(x=categories, y=sales)])

# 添加标题和标签
fig.update_layout(
    title="Sales by Category",
    xaxis_title="Category",
    yaxis_title="Sales",
    template="plotly_dark"  # 更换主题
)

# 显示图形
fig.show()


# ### 4. pyecharts

# In[3]:


from pyecharts import options as opts
from pyecharts.charts import Bar

# 数据
bar = Bar()
bar.add_xaxis(["A", "B", "C", "D", "E"])
bar.add_yaxis("Series 1", [5, 20, 36, 10, 75])

# 设置全局配置
bar.set_global_opts(title_opts=opts.TitleOpts(title="Pyecharts Example"))

# 在Jupyter中直接显示图表
bar.render_notebook()


# ### 5. dash

# In[2]:


import dash
from dash import dcc, html
import plotly.express as px

# 创建Dash应用
app = dash.Dash()

# 数据
df = px.data.gapminder()

# 创建图表
fig = px.scatter(df, x="gdpPercap", y="lifeExp", color="continent", size="pop", hover_name="country", log_x=True, size_max=60)

# 布局
app.layout = html.Div([
    html.H1("Dash Example"),
    dcc.Graph(figure=fig)
])

# 运行应用
if __name__ == "__main__":
    app.run_server(debug=True)


# In[ ]:




