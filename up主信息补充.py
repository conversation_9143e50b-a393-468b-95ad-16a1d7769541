import requests
import pandas as pd
import time
from tqdm import tqdm
import os  # 引入os库来检查文件是否存在

# 1. 配置请求头
# ===================== 重要：请在这里填入与之前相同的COOKIE =====================
COOKIE = "_uuid=C7EAEC4C-1C410-B105A-1109C-1B243B1A4A6206748infoc; buvid_fp=40028e29b81b14ac49efa06d07c665dd; buvid3=FFEC89FF-63B7-1FCD-91CC-FD834CF0438206572infoc; b_nut=1750321907; buvid4=DFA3049C-E5E1-098E-9F59-37F1CDB06F1A06572-025061916-5MLPJieg5mRnK3tL9En1Mg%3D%3D; header_theme_version=CLOSE; enable_web_push=DISABLE; enable_feed_channel=ENABLE; rpdid=|(um~luR)ukR0J'u~l|JkR~lk; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTA1ODM5NTcsImlhdCI6MTc1MDMyNDY5NywicGx0IjotMX0.iLCjY4mWThfCtuCulZSGhpXV9J0hVz2X4npRnKxAiag; bili_ticket_expires=1750583897; CURRENT_FNVAL=2000; b_lsid=D2710A10E3_1978BCE1FF8; bsource=search_google; home_feed_column=5; browser_resolution=1536-695; bp_t_offset_2056235164=1080436965049892864; SESSDATA=dc37b24a%2C1765949268%2Cb5537%2A61CjCNgoah4yD02yPjU-bs1jcMfRC5NuzL8FKJCoajEAEJyRPzdcIeHQJIUx_Umj-KfBwSVklfSDk3Q0RCeHplcWpSckRqazBoeVRILWRmWVljbEM3UFNUSWktcVNYZjBEcGJKMjEzdHNkRUt1akU2UWU5cnIxOF8tdHpuc2lHSEpVNDBTRlFqRDJ3IIEC; bili_jct=0d052be836e710bfd75d6ff732d5e4bf; DedeUserID=2056235164; DedeUserID__ckMd5=10a7dac6e5d3cd30; sid=dxkneumm"

if '在这里粘贴你复制的COOKIE字符串' in COOKIE:
    print(" 错误：请在代码中第7行的COOKIE变量处，填入你自己的B站Cookie！")
    exit()

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'Cookie': COOKIE,
    'Referer': 'https://www.bilibili.com/'
}


# 2. 复用之前的UP主粉丝获取函数
def get_uploader_fans(mid_list):
    """
    根据UP主的mid列表，批量获取UP主的粉丝数。
    """
    fans_dict = {}
    if not mid_list:
        return fans_dict

    print(f"准备为 {len(mid_list)} 位UP主补充粉丝数据...")
    url = "https://api.bilibili.com/x/web-interface/card"

    for mid in tqdm(mid_list, desc="补充获取UP主粉丝量"):
        params = {'mid': mid}
        try:
            response = requests.get(url, headers=HEADERS, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data['code'] == 0:
                fans_count = data['data']['card'].get('fans', 0)
                # 只有当获取到的粉丝数大于0时才记录，否则保留原样（或仍为0）
                if fans_count > 0:
                    fans_dict[mid] = fans_count
            else:
                # API返回错误，保留为0
                tqdm.write(f"获取 mid:{mid} 粉丝数失败, API消息: {data.get('message')}")
                break
        except requests.exceptions.RequestException as e:
            # 网络请求失败，保留为0
            tqdm.write(f"获取 mid:{mid} 粉丝数时网络错误: {e}")

        time.sleep(1)  # 友好请求，降低请求频率

    print("粉丝数据补充获取完成。")
    return fans_dict


# 3. 主程序执行流程
if __name__ == '__main__':
    # --- 参数配置 ---
    # 请确保这个文件名与您之前生成的文件名完全一致
    CSV_FILE_PATH = '补充后.csv'

    # --- 健壮性检查：检查文件是否存在 ---
    if not os.path.exists(CSV_FILE_PATH):
        print(f" 错误：找不到文件 '{CSV_FILE_PATH}'。")
        print("请确保此脚本与您的CSV数据文件在同一个文件夹下。")
        exit()

    # --- 读取现有数据 ---
    print(f"正在读取文件: {CSV_FILE_PATH}...")
    df = pd.read_csv(CSV_FILE_PATH)
    print("文件读取成功。")

    # --- 筛选需要补充数据的UP主 ---
    # 找到 'up主粉丝量' 等于 0 的所有行
    missing_fans_df = df[df['up主粉丝量'] == 0]

    if missing_fans_df.empty:
        print("\n 检查完成，没有发现粉丝数为0的数据。您的数据是完整的！")
        exit()

    # 获取这些行的不重复的 'up主_id' 列表
    mids_to_update = missing_fans_df['up主_id'].unique().tolist()
    print(f"\n发现 {len(mids_to_update)} 位UP主的粉丝数据需要补充。")

    # --- 重新获取粉丝数据 ---
    new_fans_data = get_uploader_fans(mids_to_update)

    if not new_fans_data:
        print("\n本次未能成功获取任何补充数据，文件未作修改。请稍后重试或检查Cookie。")
        exit()

    print(f"\n成功补充了 {len(new_fans_data)} 位UP主的数据。现在开始更新表格...")

    # --- 更新DataFrame中的数据 ---
    # 将字典形式的粉丝数据转换为Series，方便进行映射更新
    fans_series = pd.Series(new_fans_data, name='new_fans')

    # 遍历需要更新的mid，在原始DataFrame中更新粉丝数
    update_count = 0
    for mid, fans in fans_series.items():
        # 定位到所有该up主的行，并更新他们的粉丝量
        original_rows = df['up主_id'] == mid
        if fans > 0:  # 确保是用有效数据更新
            df.loc[original_rows, 'up主粉丝量'] = fans
            # bincount会计算True的数量
            update_count += original_rows.value_counts().get(True, 0)

    # --- 保存更新后的数据 ---
    df.to_csv("补充后.csv", index=False, encoding='utf-8-sig')

    print("\n 数据更新完成！")
    print(f"总共更新了 {update_count} 行记录。")
    print(f"数据已重新保存至: 补充后.csv")