#!/usr/bin/env python
# coding: utf-8

# ## matplotlib包

# #### 利用axes()函数可以在一幅图中生成多个坐标图形（axes）

# In[1]:


import matplotlib.pyplot as plt

plt.figure()
plt.axes([0.0,0.0,1,1])
plt.axes([0.1,0.1,.5,.5],facecolor='blue')
plt.axes([0.2,0.2,.5,.5],facecolor='pink')
plt.axes([0.3,0.3,.5,.5],facecolor='green')
plt.axes([0.4,0.4,.5,.5],facecolor='skyblue')
plt.show()


# ### 创建图形的简单实例

# In[4]:


import matplotlib.pyplot as plt
from sklearn.datasets import load_iris

# 加载 iris 数据集
iris=load_iris()
data=iris.data
target=iris.target

# 提取数据
sepal_length=data[:,0]
petal_length=data[:,2]

# 创建图形和子图
fig,axs=plt.subplots(1,2,figsize=(10,5))	# 创建包含两个子图的图形
fig.suptitle('Sepal Length vs Petal Length',fontsize=16)		# 设置图形标题

# 第1个子图：线图
axs[0].plot(sepal_length,label='Sepal Length',color='blue',
             linestyle='-')				# 绘制线图
axs[0].plot(petal_length,label='Petal Length',color='green',
             linestyle='--')				# 绘制另一个线图
axs[0].set_xlabel('Sample')				# 设置x轴标签
axs[0].set_ylabel('Length')				# 设置y轴标签
axs[0].legend()							# 添加图例
axs[0].grid(True)						# 添加网格线

# 第2个子图：散点图
scatter=axs[1].scatter(sepal_length,petal_length,c=target,
						       cmap='viridis',label='Data Points')	# 绘制散点图
axs[1].set_xlabel('Sepal Length')		# 设置x轴标签
axs[1].set_ylabel('Petal Length')		# 设置y轴标签
axs[1].legend()							# 添加图例
axs[1].grid(True)						# 添加网格线
fig.colorbar(scatter,ax=axs[1],label='Species')				# 添加颜色条

plt.tight_layout()						# 自动调整子图布局
plt.show()		# 显示图形


# ### 使用plt.subplot()函数创建子图

# In[7]:


import matplotlib.pyplot as plt
import seaborn as sns				# seaborn 库内置了iris数据集

import ssl
ssl._create_default_https_context = ssl._create_unverified_context

# 加载iris数据集并查看其结构
iris=sns.load_dataset('iris')
iris.head()							# 输出略
plt.figure(figsize=(10,6))			# 设置画布大小

# 第1个子图
plt.subplot(2,2,1)					# 2行2列的第1个
plt.hist(iris['sepal_length'],color='blue')
plt.title('Sepal Length')
# 第2个子图
plt.subplot(2,2,2)				# 2行2列的第2个
plt.hist(iris['sepal_width'],color='orange')
plt.title('Sepal Width')
# 第3个子图
plt.subplot(2,2,3)				# 2行2列的第3个
plt.hist(iris['petal_length'],color='green')
plt.title('Petal Length')
# 第4个子图
plt.subplot(2,2,4)				# 2行2列的第4个
plt.hist(iris['petal_width'],color='red')
plt.title('Petal Width')

plt.tight_layout()				# 自动调整子图间距
plt.show()


# ### 使用plt.subplots()函数创建子图

# In[8]:


import matplotlib.pyplot as plt
import seaborn as sns

import ssl
ssl._create_default_https_context = ssl._create_unverified_context

data=sns.load_dataset("iris")				# 加载内置的iris数据集

# 使用plt.subplots()创建一个2行3列的子图布局
fig,axs=plt.subplots(2,3,figsize=(15,8))

# 第1个子图：绘制sepal_length和sepal_width的散点图
axs[0,0].scatter(data['sepal_length'],data['sepal_width'])
axs[0,0].set_title('Sepal Length vs Sepal Width')

# 第2个子图：绘制petal_length和petal_width的散点图
axs[0,1].scatter(data['petal_length'],data['petal_width'])
axs[0,1].set_title('Petal Length vs Petal Width')

# 第3个子图：绘制sepal_length的直方图
axs[0,2].hist(data['sepal_length'],bins=20)
axs[0,2].set_title('Sepal Length Distribution')

# 4个子图：绘制petal_length的直方图
axs[1,0].hist(data['petal_length'],bins=20)
axs[1,0].set_title('Petal Length Distribution')

# 第5和第6位置合并为一个大图，展示species的计数条形图
# 为了合并第二行的中间和最右侧位置，使用subplot2grid功能
plt.subplot2grid((2,3),(1,1),colspan=2)
sns.countplot(x='species',data=data)
plt.title('Species Count')

plt.tight_layout()			# 调整子图之间的间距
plt.show()


# ### 使用figure.add_subplot()函数创建子图

# In[9]:


import matplotlib.pyplot as plt

fig = plt.figure(figsize=(8,4))		# 创建一个图形实例

# 添加第1个子图：1行2列的第1个位置
ax1=fig.add_subplot(1,2,1)
ax1.plot([1,2,3,4],[1,4,2,3])		# 绘制一条简单的折线图
ax1.set_title('First Subplot')

# 添加第2个子图：1行2列的第2个位置
ax2=fig.add_subplot(122)
ax2.bar([1,2,3,4],[10,20,15,25])	# 绘制一个条形图
ax2.set_title('Second Subplot')

# 显示图形
plt.tight_layout()					# 自动调整子图参数，使之填充整个图形区域
plt.show()


# ### 使用subplot2grid()函数创建子图

# In[9]:


import matplotlib.pyplot as plt
from sklearn.datasets import load_iris

# 载入鸢尾花数据集
iris=load_iris()
data=iris.data
target=iris.target
feature_names=iris.feature_names
target_names=iris.target_names

grid_size=(3,3)			# 定义网格大小为3x3

# 第1个子图占据位置 (0,0)
ax1=plt.subplot2grid(grid_size,(0,0),facecolor='orange')
ax1.scatter(data[:,0],data[:,1],c=target,cmap='viridis')
ax1.set_xlabel(feature_names[0])
ax1.set_ylabel(feature_names[1])

# 第2个子图占据位置(0,1)，并跨越2列
ax2=plt.subplot2grid(grid_size,(0,1),colspan=2,facecolor='pink')
ax2.scatter(data[:,1],data[:,2],c=target,cmap='viridis')
ax2.set_xlabel(feature_names[1])
ax2.set_ylabel(feature_names[2])

# 第3个子图占据位置(1,0)，并跨越2行
ax3=plt.subplot2grid(grid_size,(1,0),rowspan=2,facecolor='grey')
ax3.scatter(data[:,0],data[:,2],c=target,cmap='viridis')
ax3.set_xlabel(feature_names[0])
ax3.set_ylabel(feature_names[2])

# 第4个子图占据位置 (1,1)，并跨越到最后
ax4=plt.subplot2grid(grid_size,(1,1),colspan=2,
						     rowspan=2,facecolor='skyblue')
ax4.scatter(data[:,2],data[:,3],c=target,cmap='viridis')
ax4.set_xlabel(feature_names[2])
ax4.set_ylabel(feature_names[3])

plt.tight_layout()
plt.show()


# ### 使用gridspec.GridSpec()函数创建子图

# In[10]:


import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from sklearn.datasets import load_iris

# 载入Iris数据集
iris=load_iris()
data=iris.data
target=iris.target
feature_names=iris.feature_names
target_names=iris.target_names

# 创建一个2x2的子图网格
fig=plt.figure(figsize=(10,6))
gs=gridspec.GridSpec(2,2,height_ratios=[1,1],width_ratios=[1,1])

# 在网格中创建子图
ax1=plt.subplot(gs[0,0])
ax1.scatter(data[:,0],data[:,1],c=target,cmap='viridis')
ax1.set_xlabel(feature_names[0])
ax1.set_ylabel(feature_names[1])
ax1.set_title('Sepal Length vs Sepal Width')

ax2=plt.subplot(gs[0,1])
ax2.scatter(data[:,1],data[:,2],c=target,cmap='viridis')
ax2.set_xlabel(feature_names[1])
ax2.set_ylabel(feature_names[2])
ax2.set_title('Sepal Width vs Petal Length')

ax3=plt.subplot(gs[1,:])
ax3.scatter(data[:,2],data[:,3],c=target,cmap='viridis')
ax3.set_xlabel(feature_names[2])
ax3.set_ylabel(feature_names[3])
ax3.set_title('Petal Length vs Petal Width')

plt.tight_layout()				# 调整布局
plt.show()


# ### 图表元素的添加

# In[11]:


import matplotlib.pyplot as plt
import numpy as np
from sklearn.datasets import load_iris

# 载入Iris数据集
iris=load_iris()
data=iris.data
target=iris.target
feature_names=iris.feature_names
target_names=iris.target_names

fig,ax=plt.subplots(figsize=(6,4))		# 创建图形和子图
# 绘制散点图
for i in range(len(target_names)):
    ax.scatter(data[target==i,0],data[target==i,1],label=target_names[i])
ax.set_title('Sepal Length vs Sepal Width',fontsize=16)	# 添加标题
ax.legend(fontsize=12)						# 添加图例
ax.grid(True,linestyle='--',alpha=0.5)	# 添加网格线

# 自定义坐标轴标签
ax.set_xlabel(feature_names[0],fontsize=14)
ax.set_ylabel(feature_names[1],fontsize=14)
# 设置坐标轴刻度标签大小
ax.tick_params(axis='both',which='major',labelsize=12)

plt.tight_layout()							# 调整图形边界
plt.show()


# ### 极坐标示例

# In[12]:


import matplotlib.pyplot as plt
import numpy as np

# 创建一些示例数据
theta=np.linspace(0,2*np.pi,100)
r=np.abs(np.sin(theta))

plt.figure(figsize=(6,6))
ax=plt.subplot(111,projection='polar')			# 创建极坐标系图形
ax.plot(theta,r,color='blue',linewidth=2)		# 绘制极坐标系图形
ax.set_title('Polar Plot',fontsize=16)			# 添加标题
plt.show()										# 显示图形


# In[10]:


import numpy as np
import matplotlib.pyplot as plt

# 生成角度数据（0 到 2π）
theta = np.linspace(0, 2 * np.pi, 100)

# 生成对应的半径数据
r = np.abs(np.sin(2 * theta))

# 使用 plt.polar() 绘制极坐标图
plt.polar(theta, r, color='b', linewidth=2, label="r = |sin(2θ)|")

# 添加标题和图例
plt.title("Polar Plot Example")
plt.legend()

# 显示图像
plt.show()


# ## seaborn包

# ### 与matplotlib配合使用

# In[12]:


import seaborn as sns
import matplotlib.pyplot as plt

# 加载数据集
iris=sns.load_dataset("iris",data_home='seaborn-data',cache=True)
tips=sns.load_dataset("tips",data_home='seaborn-data',cache=True)
car_crashes=sns.load_dataset("car_crashes",data_home='seaborn-data',cache=True)
penguins=sns.load_dataset("penguins",data_home='seaborn-data',cache=True)
diamonds=sns.load_dataset("diamonds",data_home='seaborn-data',cache=True)

plt.figure(figsize=(15,8))				# 设置画布
# 第1幅图：iris数据集的散点图
plt.subplot(2,3,1)
sns.scatterplot(x="sepal_length",y="sepal_width",hue="species",
                   data=iris)
plt.title("Iris scatterplot")

# 第2幅图：tips 数据集的箱线图
plt.subplot(2,3,2)
tips=sns.load_dataset("tips",data_home='seaborn-data',cache=True)
sns.boxplot(x="day",y="total_bill",hue="smoker",data=tips)
plt.title("Tips boxplot")

# 第3幅图：tips 数据集的小提琴图
plt.subplot(2,3,3)
sns.violinplot(x="day",y="total_bill",hue="smoker",data=tips)
plt.title("Tips violinplot")

# 第4幅图：car_crashes 数据集的直方图
plt.subplot(2,3,4)
sns.histplot(car_crashes['total'],bins=20)
plt.title("Car Crashes histplot")

# 第5幅图：penguins 数据集的点图
plt.subplot(2,3,5)
sns.pointplot(x="island",y="bill_length_mm",hue="species",data=penguins)
plt.title("Penguins pointplot")

# 第6幅图：diamonds 数据集的计数图
plt.subplot(2,3,6)
sns.countplot(x="cut",data=diamonds)
plt.title("Diamonds countplot")

plt.tight_layout()
plt.show()


# ### 使用图表分面绘制多个散点图

# In[13]:


import seaborn as sns
import matplotlib.pyplot as plt

# import ssl
# ssl._create_default_https_context = ssl._create_unverified_context

iris=sns.load_dataset("iris")			# 加载iris数据集

# 创建 FacetGrid 对象，按照种类（'species'）进行分面
g=sns.FacetGrid(iris,col="species",margin_titles=True)
# 在每个子图中绘制花萼长度与花萼宽度的散点图
g.map(sns.scatterplot,"sepal_length","sepal_width")
g.set_axis_labels("Sepal Length","Sepal Width")		# 设置子图标题

plt.show()


# ## pyecharts包

# ### 简单柱状图

# In[9]:


from pyecharts.charts import Bar

bar = Bar()
bar.add_xaxis(["衬衫", "羊毛衫", "雪纺衫", "裤子", "高跟鞋", "袜子"])
bar.add_yaxis("商家A", [5, 20, 36, 10, 75, 90])
# render 会生成本地 HTML 文件，默认会在当前目录生成 render.html 文件
# 也可以传入路径参数，如 bar.render("mycharts.html")
bar.render()
# bar.render_notebook()


# ### 绘图流程展示

# In[10]:


from pyecharts import options as opts
from pyecharts.charts import Line

# 1. 选择图表类型 - 在这里选择的是折线图 (Line)
line = Line()

# 2. 声明图形类并添加数据
x_data = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
y_data = [820, 932, 901, 934, 1290, 1330, 1320]
line.add_xaxis(x_data)
line.add_yaxis("销量", y_data)

# 3. 选择全局变量 - 设置图表的一些全局配置项
line.set_global_opts(
    title_opts=opts.TitleOpts(title="一周销量数据", subtitle="2025年销售统计"),
    tooltip_opts=opts.TooltipOpts(trigger="axis"),
    xaxis_opts=opts.AxisOpts(name="星期"),
    yaxis_opts=opts.AxisOpts(name="销量"),
)

# 4. 系列配置项 - 设置具体数据系列的样式
line.set_series_opts(
    label_opts=opts.LabelOpts(is_show=True, position="top"),  # 显示每个数据点的标签
    linestyle_opts=opts.LineStyleOpts(width=2, color="blue", type_="solid"),  # 设置线条样式
)

# 5. 显示及保存图表 - 渲染图表并保存为HTML文件
line.render("sales_data_line_chart.html")  # 保存为HTML文件
# line.render_notebook() # 展示到该界面


# ### 普通方式配置

# In[3]:


from pyecharts import options as opts
from pyecharts.charts import Line

# 创建一个折线图实例
line = Line()

# 普通方式配置
line.add_xaxis(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'])
line.add_yaxis("销量", [820, 932, 901, 934, 1290, 1330, 1320])
line.set_global_opts(
    title_opts=opts.TitleOpts(title="一周销量数据", subtitle="2025年销售统计"),
    tooltip_opts=opts.TooltipOpts(trigger="axis"),
    xaxis_opts=opts.AxisOpts(name="星期"),
    yaxis_opts=opts.AxisOpts(name="销量"),
)
line.set_series_opts(
    label_opts=opts.LabelOpts(is_show=True, position="top"),
    linestyle_opts=opts.LineStyleOpts(width=2, color="blue", type_="solid"),
)

# 渲染图表
line.render("sales_data_line_chart_normal.html")


# ### 链式使用

# In[4]:


from pyecharts import options as opts
from pyecharts.charts import Line

# 创建一个折线图实例并使用链式调用
line = Line().add_xaxis(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']) \
             .add_yaxis("销量", [820, 932, 901, 934, 1290, 1330, 1320]) \
             .set_global_opts(
                 title_opts=opts.TitleOpts(title="一周销量数据", subtitle="2025年销售统计"),
                 tooltip_opts=opts.TooltipOpts(trigger="axis"),
                 xaxis_opts=opts.AxisOpts(name="星期"),
                 yaxis_opts=opts.AxisOpts(name="销量"),
             ) \
             .set_series_opts(
                 label_opts=opts.LabelOpts(is_show=True, position="top"),
                 linestyle_opts=opts.LineStyleOpts(width=2, color="blue", type_="solid"),
             )

# 渲染图表
line.render("sales_data_line_chart_chain.html")


# ## plotly包

# In[5]:


import plotly.graph_objects as go
# 创建轨迹
trace = go.Scatter(x=[1, 2, 3], y=[4, 5, 6])

# 创建布局
layout = go.Layout(title='我的散点图', xaxis={'title': 'x轴'}, yaxis={'title': 'y轴'})

# 创建图表对象
fig = go.Figure(data=[trace], layout=layout)

# 显示图表
fig.show()


# In[6]:


import plotly.graph_objects as go

# 创建带有工具提示的散点图轨迹
trace1 = go.Scatter(x=[1, 2, 3, 4], y=[10, 11, 12, 13], text=['点1', '点2', '点3', '点4'], mode='markers')

# 创建图表对象并显示
fig = go.Figure(data=[trace1])
# fig.update_traces(hoverinfo='text+x+y')
fig.show()


# In[7]:


import plotly.graph_objects as go

# 创建带有工具提示的散点图轨迹
trace7 = go.Scatter(x=[1, 2, 3, 4], y=[10, 11, 12, 13], text=['点1', '点2', '点3', '点4'], mode='markers')

# 创建图表对象并显示
fig = go.Figure(data=[trace7])
fig.update_traces(hoverinfo='text+x+y')
fig.show()


# In[8]:


import plotly.express as px

# 创建散点图
fig = px.scatter(x=[1, 2, 3], y=[10, 11, 12], title='散点图')

# 显示图表
fig.show()


# ## dash包

# In[9]:


from dash.dependencies import Input, Output
import pandas as pd

# 创建一个示例数据集
df = pd.DataFrame({
    "Fruit": ["Apples", "Oranges", "Bananas", "Apples", "Oranges", "Bananas"],
    "Amount": [4, 1, 2, 2, 4, 5],
    "City": ["SF", "SF", "SF", "NYC", "NYC", "NYC"]
})

# 更新应用的布局，添加一个下拉菜单
app.layout = html.Div(children=[
    html.H1(children='Hello Dash'),

    html.Div(children='''
        Dash: A web application framework for Python.
    '''),

    # 添加了一个 dcc.Dropdown 组件，用户可以选择不同的城市
    dcc.Dropdown(
        id='city-dropdown',
        options=[
            {'label': 'San Francisco', 'value': 'SF'},
            {'label': 'New York City', 'value': 'NYC'}
        ],
        value='SF'
    ),

    dcc.Graph(
        id='example-graph',
    )
])

# 定义回调函数：使用 @app.callback 装饰器定义了一个回调函数 update_graph，当用户选择不同的城市时，图表会动态更新
@app.callback(
    Output('example-graph', 'figure'),
    [Input('city-dropdown', 'value')]
)
# 回调函数根据用户选择的城市过滤数据集，并更新图表
def update_graph(selected_city):
    filtered_df = df[df['City'] == selected_city]
    fig = px.bar(filtered_df, x="Fruit", y="Amount", color="City", barmode="group")
    return fig

# 运行应用
if __name__ == '__main__':
    app.run_server(debug=True)


# In[ ]:




