#!/usr/bin/env python
# coding: utf-8

# # 随堂练习（1）

# In[3]:


import jieba
from collections import Counter
from pyecharts.charts import WordCloud
from pyecharts import options as opts


# 统计词频并返回前20个词
def get_top_words(words, top_k=20):
    # counter返回一个类似字典的结构：每个单词出现了多少次
    counter = Counter(words)
    # most_common方法：返回出现频率最高的 n 个元素
    return counter.most_common(top_k)

# 读取当前目录下的文件
with open("学院简介", 'r', encoding='utf-8') as f:
    text = f.read()

# 使用jieba进行分词
words = jieba.lcut(text)
# 去除单个字和标点符号
words = [w for w in words if len(w) > 1 and w.strip()]

# 获取前20个词和词频
top_words = get_top_words(words, top_k=20)
wordcloud = (
    WordCloud()
    .add("", top_words, word_size_range=[20, 100], shape='circle')
    .set_global_opts(title_opts=opts.TitleOpts(title="词云图 - Top20 高频词"))
)
wordcloud.render("wordcloud.html")  # 生成 HTML 文件


# # 随堂练习（2）

# In[2]:


import dash
from dash import html, dcc, Input, Output
import pandas as pd
import plotly.express as px

# 创建数据
data = {
    'City': ['Beijing'] * 4 + ['Shanghai'] * 4 + ['Shenzhen'] * 4,
    'Product': ['A', 'B', 'C', 'D'] * 3,
    'Sales': [100, 150, 80, 70, 120, 90, 60, 130, 110, 100, 85, 95]
}
df = pd.DataFrame(data)

# 初始化 Dash 应用
app = dash.Dash(__name__)

# 按产品汇总总销售额
df_sum = df.groupby('Product', as_index=False)['Sales'].sum()
print(df_sum)
pie_fig = px.pie(df_sum, names='Product', values='Sales', title="产品销量占比饼图")

# 页面布局
app.layout = html.Div([
    # 顶部标题，设置格式
    html.H1("产品销售数据大屏", style={'textAlign': 'center', 'marginBottom': '30px'}),

    

    # 统计卡片
    html.Div(id='stats-cards', style={
        'display': 'flex',
        'justifyContent': 'space-around',
        'marginBottom': '40px'
    }),

    # 控件区域
    html.Div([
        html.Label("请选择城市："),
        dcc.Dropdown(
            id='city-dropdown',
            options=[{'label': c, 'value': c} for c in df['City'].unique()],
            value='Beijing',
            style={'width': '300px'}
        )
    ], style={'marginBottom': '30px'}),
    
    # 图表区域
    html.Div([
        
        html.Div([
            dcc.Graph(id='bar-chart')
        ], style={'width': '48%', 'display': 'inline-block'}),

        html.Div([
            dcc.Graph(figure=pie_fig)
        ], style={'width': '48%', 'display': 'inline-block'})
    ])
], style={'padding': '20px'})

# 回调：更新卡片 & 图表
@app.callback(
    [Output('stats-cards', 'children'), # id是 'stats-cards' 组件的 children 属性（就是这个区域里面显示的内容，通常是子组件）
     Output('bar-chart', 'figure'),],
    [Input('city-dropdown', 'value')]
)

def update_dashboard(city):
    filtered = df[df['City'] == city]

    total_sales = filtered['Sales'].sum()
    product_count = filtered['Product'].nunique()
    avg_sales = round(filtered['Sales'].mean(), 2)

    # 卡片组件——注意这里存储的是CSS样式
    # padding：内边距，卡片内容离边框距离，20像素。
    # borderRadius：圆角，10像素圆滑边角。
    # backgroundColor：背景色，浅灰色 #f0f0f0。
    # width：卡片宽度，这里设置为父容器的 30%。
    # textAlign：文字居中。
    # boxShadow：阴影效果，使卡片有立体感。
    card_style = {
        'padding': '20px',
        'borderRadius': '10px',
        'backgroundColor': '#f0f0f0',
        'width': '30%',
        'textAlign': 'center',
        'boxShadow': '2px 2px 8px rgba(0,0,0,0.1)'
    }

    # 这是一个列表，包含了 3 个 html.Div，代表 3 个统计卡片。
    # 每个卡片结构一样：
    # - html.Div 作为卡片容器
    # - 里面有标题（html.H4）和具体数值（html.H2）
    # - 通过 style=card_style，这 3 个卡片都共享刚才定义的样式。
    # # 由于外层容器用的是 flex 布局且 justifyContent: space-around，3 个卡片会横排且间距均匀。
    cards = [
        html.Div([
            html.H4("总销量"),
            html.H2(f"{total_sales}")
        ], style=card_style),

        html.Div([
            html.H4("产品种类"),
            html.H2(f"{product_count}")
        ], style=card_style),

        html.Div([
            html.H4("平均销量"),
            html.H2(f"{avg_sales}")
        ], style=card_style)
    ]

    # 图表
    bar_fig = px.bar(filtered, x='Product', y='Sales', title=f"{city} 产品销量柱状图")

    return cards, bar_fig

# 启动应用
if __name__ == '__main__':
    app.run_server(debug=True)


# In[ ]:




