#!/usr/bin/env python
# coding: utf-8

# # 13 随堂练习参考答案

# In[62]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Line, Grid
import random
import plotly.express as px
import plotly.graph_objects as go

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号


# # 随堂练习（1）
# - 预处理：按年份聚合，计算平均评分和平均票房两个指标
# - 绘图：用4个包分别绘制
#     - 两个数据系列共享Y轴（示例中均值分数和均值票房）
#     - 两个数据系列不同Y轴（分数和票房尺度差距大，适合双Y轴）

# In[63]:


# 数据预处理过程
df = pd.read_csv("movies.csv")

# 去掉year score gross有缺失值的观测值
df_clean = df[['year', 'score', 'gross']].dropna()

# 以year为祖，对score和gross做平均值操作，并且分别命名为avg_score和avg_gross
df_yearly = df_clean.groupby('year').agg(
    avg_score=('score', 'mean'),
    avg_gross=('gross', 'mean')
).reset_index()

# 对year重命名为Year
df_yearly.rename(columns={'year': 'Year'}, inplace=True)

df_yearly.head()


# In[66]:


# 使用matplotlib绘制
# 多个数据系列（但共享Y轴）
plt.figure(figsize=(10,5))
plt.plot(df_yearly['Year'], df_yearly['avg_score'], label='Avg Score')

# plt.plot(df_yearly['Year'], df_yearly['avg_gross'], label='Avg Gross (x10M)')
# 观察除1e7的意义在于？（思考）
plt.plot(df_yearly['Year'], df_yearly['avg_gross'] / 1e7, label='Avg Gross (x10M)')

plt.xlabel('Year')
plt.ylabel('Value')
plt.title('Movies Avg Score and Gross Over Years (Shared Y axis)')
plt.legend()
plt.show()


# In[ ]:


# 观察除1e7的意义在于？（思考）
# plt.plot(df_yearly['Year'], df_yearly['avg_gross'] / 1e7, label='Avg Gross (x10M)')


# In[51]:


# 多个数据系列（不共享Y轴）
# 创建一个绘图窗口和主坐标轴 ax1，设置图形大小为10x5
fig, ax1 = plt.subplots(figsize=(10,5))

# 在第一个坐标轴（左侧Y轴）上绘制蓝色折线，X轴是年份，Y轴是电影的平均评分。
ax1.plot(df_yearly['Year'], df_yearly['avg_score'], 'b-', label='Avg Score')
ax1.set_xlabel('Year')
ax1.set_ylabel('Avg Score', color='b')
ax1.tick_params(axis='y', labelcolor='b')

# 实例化一个共享x轴的第2个坐标轴
# 新建第二个坐标轴 ax2，它共享ax1的X轴，但拥有独立的Y轴（位于右侧）
ax2 = ax1.twinx()
ax2.plot(df_yearly['Year'], df_yearly['avg_gross'], 'r-', label='Avg Gross')
ax2.set_ylabel('Avg Gross', color='r')
ax2.tick_params(axis='y', labelcolor='r')

plt.title('Movies Score and Gross Over Years (Dual Y axes)')
plt.show()


# In[5]:


# 使用seaborn绘图（共享Y轴）
# 需要将数据转为长格式（参考第7讲melt函数）
df_melt = df_yearly.melt(id_vars='Year', value_vars=['avg_score', 'avg_gross'], var_name='Metric', value_name='Value')
# print(df_melt)
plt.figure(figsize=(10,5))
sns.lineplot(data=df_melt, x='Year', y='Value', hue='Metric')
plt.title('Movies Avg Score and Gross Over Years (Seaborn)')
plt.show()


# In[52]:


# 使用pyecharts绘制
# 多个数据系列（但共享Y轴）
line = (
    Line()
    .add_xaxis(df_yearly['Year'].astype(str).tolist())
    .add_yaxis("Avg Score", df_yearly['avg_score'].round(2).tolist())
    .add_yaxis("Avg Gross (x10M)", (df_yearly['avg_gross'] / 1e7).round(2).tolist())
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Movies Avg Score & Gross (Shared Y) - Pyecharts"),
        yaxis_opts=opts.AxisOpts(name="Value")
    )
)
line.render_notebook()


# In[53]:


# 多个数据系列（不共享Y轴）
line2 = (
    Line()
    .add_xaxis(df_yearly['Year'].astype(str).tolist())
    .add_yaxis("Avg Score", df_yearly['avg_score'].round(2).tolist(), yaxis_index=0)
    .extend_axis(yaxis=opts.AxisOpts(name="Avg Gross", position="right"))
    .add_yaxis("Avg Gross", df_yearly['avg_gross'].round(0).tolist(), yaxis_index=1)
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Movies Avg Score & Gross (Dual Y) - Pyecharts"),
        yaxis_opts=opts.AxisOpts(name="Avg Score"),
        tooltip_opts=opts.TooltipOpts(trigger="axis"),
    )
)
line2.render_notebook()


# In[3]:


# 共享Y轴——方式1
df_long = df_yearly.melt(id_vars='Year', value_vars=['avg_score', 'avg_gross'],
                        var_name='Metric', value_name='Value')
print(df_long)
fig = px.line(df_long, x='Year', y='Value', color='Metric',
              labels={'Year':'Year', 'Value':'Value', 'Metric':'Metric'},
              title='Plotly Express - Shared Y-axis Line Plot')
fig.show()


# In[4]:


# 共享Y轴——方式2
# 用 px.line 画第一个系列
fig = px.line(df_yearly, x='Year', y='avg_score', labels={'avg_score': 'avg_score'}, title='共享Y轴的双线图')

# 用 add_scatter 添加第二个系列
fig.add_scatter(x=df_yearly['Year'], y=df_yearly['avg_gross'], mode='lines', name='avg_gross')

fig.show()


# In[67]:


# 不共享Y轴（双Y轴）
fig = go.Figure()

fig.add_trace(go.Scatter(
    x=df_yearly['Year'], y=df_yearly['avg_score'],
    name='Avg Score', mode='lines', yaxis='y1'
))

fig.add_trace(go.Scatter(
    x=df_yearly['Year'], y=df_yearly['avg_gross'],
    name='Avg Gross', mode='lines', yaxis='y2'
))

fig.update_layout(
    title='Plotly - Dual Y-axis Line Plot',
    xaxis=dict(title='Year'),
    yaxis=dict(title='Avg Score', side='left'),
    yaxis2=dict(title='Avg Gross', overlaying='y', side='right')
)
fig.show()


# In[ ]:





# # 随堂练习（2）
# - 预处理：读取数据（ratings.csv）
# - 绘图：用不同的包分别绘制1*2的格式（参考第10讲随堂练习）

# In[6]:


# 数据预处理过程
df = pd.read_csv("ratings.csv")

df.head()


# In[7]:


# 创建一行两列的子图
fig, axes = plt.subplots(1, 2, figsize=(12, 6))

# 第一个子图：折线图
axes[0].plot(df['Date'].tolist(), df['Python'].tolist(), marker='o')
axes[0].set_title("Line Chart - Matplotlib")
axes[0].set_xlabel("X Axis")
axes[0].set_ylabel("Y Axis")
axes[0].grid(True)
axes[0].tick_params(axis='x', rotation=45)


# 第二个子图：面积图
plt.fill_between(df['Date'], df['C'], color='skyblue', alpha=0.5)
axes[1].plot(df['Date'].tolist(), df['C'].tolist(), marker='o')
axes[1].set_title("Line Chart (Time Series) - Matplotlib")
axes[1].set_xlabel("Date")
axes[1].set_ylabel("Value")
axes[1].grid(True)

axes[1].tick_params(axis='x', rotation=45)

plt.tight_layout()  # 自动调整布局
plt.show()


# 观察下面两个图，x轴标签有什么问题？


# In[59]:


from pyecharts.charts import Line, Grid
from pyecharts import options as opts

# 创建折线图（左边）
line_chart = (
    Line()
    .add_xaxis(df['Date'].tolist())
    .add_yaxis("Line Chart", df['Python'].tolist(), 
               symbol="circle",
               label_opts=opts.LabelOpts(is_show=False),
               linestyle_opts=opts.LineStyleOpts(width=3))
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Line Chart (Left)"),
        xaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(rotate=45)),
        yaxis_opts=opts.AxisOpts(name="Value"),
    )
)

# 创建面积图（右边）
area_chart = (
    Line()
    .add_xaxis(df['Date'].tolist())
    .add_yaxis("Area Chart", df['C'].tolist(),
               areastyle_opts=opts.AreaStyleOpts(opacity=0.5),
               symbol="circle",
               label_opts=opts.LabelOpts(is_show=False),
               linestyle_opts=opts.LineStyleOpts(width=3))
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Area Chart (Right)"),
        xaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(rotate=45)),
        yaxis_opts=opts.AxisOpts(name="Value"),
    )
)

# 使用 Grid 布局（1行2列）
grid = (
    Grid()
    .add(line_chart, grid_opts=opts.GridOpts(pos_left="5%", pos_right="55%"))  # 左边折线图
    .add(area_chart, grid_opts=opts.GridOpts(pos_left="55%", pos_right="5%"))   # 右边面积图
)

# 显示图表
grid.render_notebook()


# In[8]:


import plotly.express as px
import pandas as pd
from plotly.subplots import make_subplots

# 创建折线图（左边）
line_chart = px.line(df, x="Date", y="Python", title="Line Chart (Left)")

# 创建面积图（右边）
area_chart = px.area(df, x="Date", y="C", title="Area Chart (Right)")

# 组合成 1×2 布局

fig = make_subplots(rows=1, cols=2)
fig.add_trace(line_chart.data[0], row=1, col=1)
fig.add_trace(area_chart.data[0], row=1, col=2)

# 调整布局
fig.update_layout(
    title_text="1×2 Layout with Plotly Express",
    showlegend=True,
    height=500,
    width=900,
)

fig.show()


# # 随堂练习（3）

# In[9]:


# 下载后，假设有edges.csv，格式：Source, Target
df_edges = pd.read_csv('edges.csv')  # 节点关系数据

# 创建无向图
G = nx.from_pandas_edgelist(df_edges, 'source', 'target')

plt.figure(figsize=(12,12))
pos = nx.spring_layout(G, k=0.15)  # 通过弹簧布局确定节点位置
nx.draw_networkx_nodes(G, pos, node_size=20, node_color='blue', alpha=0.7)
nx.draw_networkx_edges(G, pos, alpha=0.3)
plt.title('Marvel Universe Network')
plt.axis('off')
plt.show()


# In[12]:


import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt

# 读取dges.csv，格式：Source, Target
df_edges = pd.read_csv('edges.csv')  # 节点关系数据

G = nx.Graph()

G = nx.from_pandas_edgelist(df_edges, 'source', 'target')

plt.figure(figsize=(12,12))
pos = nx.spring_layout(G, k=0.15)  # 通过弹簧布局确定节点位置

nx.draw(G, 
        pos, 
        # with_labels=True, 
        node_color='skyblue', 
        edge_color='gray', 
        node_size=100, 
        font_size=16)
plt.title('Marvel Universe Network')
plt.axis('off')
plt.show()


# In[61]:


from pyecharts import options as opts
from pyecharts.charts import Graph

# 读取边数据
df_edges = pd.read_csv('edges.csv')  # 假设格式：source,target

# 提取所有唯一节点
all_nodes = set(df_edges['source']).union(set(df_edges['target']))
# print(all_nodes)

# 构建节点列表（默认大小，可根据需要调整）
nodes = [{"name": node, "symbolSize": 20} for node in all_nodes]

# 构建边列表
links = [
    {"source": row['source'], "target": row['target']}
    for _, row in df_edges.iterrows()
]

graph = (
    Graph()
    .add(
        "",
        nodes,
        links,
        repulsion=400,  # 排斥力，节点间距离
        layout="force",  # 力导向布局
        edge_symbol=["none", "arrow"],  # 边的箭头
        edge_symbol_size=10,
        linestyle_opts=opts.LineStyleOpts(opacity=0.6, width=1, curve=0.3),
        label_opts=opts.LabelOpts(is_show=True),
    )
    .set_global_opts(title_opts=opts.TitleOpts(title="Marvel Heroes Relationship Graph"))
)

graph.render_notebook()
# graph.render("graph.html")


# In[ ]:




