#!/usr/bin/env python
# coding: utf-8

# # Lecture 8: 数值关系数据可视化

# In[2]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Scatter
import random
import plotly.express as px


# ## 散点图

# ### 基本散点图

# In[5]:


# 利用matplotlib绘制

# 生成随机样本
x = np.random.rand(50)
y = np.random.rand(50)

plt.figure(figsize=(8, 6))

# 传递x值和y值
plt.scatter(x, y, c='blue', alpha=0.6, s=100)

# 增加x轴和y轴标签，添加标题
plt.xlabel('X-axis')
plt.ylabel('Y-axis')
plt.title('Matplotlib Scatter Plot')

# 显示网格线
plt.grid(True)
plt.show()


# In[15]:


# 生成随机样本
data = pd.DataFrame({
    'x': np.random.rand(50),
    'y': np.random.rand(50)
})

plt.figure(figsize=(8, 6))

# 传递x值和y值
# x='x', y='y' - 设置x轴和y轴所对应的数据列名
sns.scatterplot(data=data, x='x', y='y')

# 增加x轴和y轴标签，添加标题
plt.xlabel('X-axis')
plt.ylabel('Y-axis')
plt.title('Seaborn Scatter Plot')

plt.show()


# In[36]:


# 创建两个包含50个随机数的列表
x_data = [random.random() for _ in range(50)]
y_data = [random.random() for _ in range(50)]

# 创建散点图
scatter = (
    Scatter()                               # 初始化散点图对象
    .add_xaxis(xaxis_data=x_data)           # 添加x轴数据
    .add_yaxis(                             # 添加y轴数据和相关配置
        series_name="Scatter Data",         # 数据系列名称，会显示在图例中
        y_axis=y_data,                      # y轴数据
        symbol_size=10,                     # 散点大小设置为10像素
        label_opts=opts.LabelOpts(is_show=False),  # 不显示数据标签
    )
    .set_global_opts( # 用于设置全局图表选项
        title_opts=opts.TitleOpts(title="Pyecharts Scatter Plot"),  # 设置图表标题
        xaxis_opts=opts.AxisOpts(                                   # 配置x轴
            type_="value",                                          # 数值轴
            name="X-axis",                                          # 轴名称
            splitline_opts=opts.SplitLineOpts(is_show=True)         # 显示网格线
        ),
        yaxis_opts=opts.AxisOpts(                                   # 配置y轴
            type_="value",                                          # 数值轴
            name="Y-axis",                                          # 轴名称
            splitline_opts=opts.SplitLineOpts(is_show=True)         # 显示网格线
        ),
    )
)

# Render to HTML file or display
# scatter.render_notebook()  # If in Jupyter notebook
scatter.render("scatter_plot.html")


# In[16]:


# 生成随机生成样本
data = pd.DataFrame({
    'x': np.random.rand(50),
    'y': np.random.rand(50),
    'category': np.random.choice(['A', 'B', 'C', 'D'], 50)
})

# x='x', y='y'：指定X轴和Y轴使用的数据列
# title='Plotly Scatter Plot'：设置图表标题
# labels={'x': 'X-axis', 'y': 'Y-axis'}：自定义轴标签
# hover_data=['category']：当鼠标悬停在点上时显示类别信息
fig = px.scatter(data, x='x', y='y', 
                 title='Plotly Scatter Plot',
                 labels={'x': 'X-axis', 'y': 'Y-axis'},
                 hover_data=['category'])

# Update layout
fig.update_layout(
    plot_bgcolor='white',
    legend_title_text='Category'
)

# Show the plot
fig.show()


# ### 回归散点图
# 在散点图上添加回归线或曲线，直观显示变量间的相关关系和趋势

# In[3]:


df = sns.load_dataset('iris',data_home='seaborn',cache=True)  # 加载 iris 数据集
# 绘制散点图，默认线性拟合
sns.regplot(x=df["sepal_length"], y=df["sepal_width"])
plt.show()


# In[4]:


# 绘制散点图（默认进行线性拟合，设置不进行线性拟合，fit_reg=False）
sns.regplot(x=df["sepal_length"], y=df["sepal_width"], fit_reg=False)
plt.show()


# #### 其他包没有那么便捷实现

# ### 抖动散点图（Jittered Scatter Plots）

# In[5]:


# 创建样本数据集
categories = ['A', 'B', 'C']
x = np.random.choice(categories, size=100)  # 分类变量
y = np.random.normal(0, 1, size=100)        # 连续变量

# 创建DataFrame
df = pd.DataFrame({
    'category': x,
    'value': y
})

# 绘制对比图
plt.figure(figsize=(12, 6))

# 无抖动的点图
plt.subplot(1, 2, 1)
sns.stripplot(x='category', y='value', data=df, jitter=False, alpha=0.6)
plt.title('Seaborn Strip Plot (No Jitter)')
plt.xlabel('Category')
plt.ylabel('Value')

# 有抖动的点图
plt.subplot(1, 2, 2)
sns.stripplot(x='category', y='value', data=df, jitter=True, alpha=0.6)
plt.title('Seaborn Strip Plot (With Jitter)')
plt.xlabel('Category')
plt.ylabel('Value')

plt.tight_layout()
plt.show()


# In[8]:


# 使用plotly绘制
fig = px.strip(df, 
               x='category', 
               y='value', 
               title='Jitter Plot with Plotly Express')
fig.show()


# ## 六边形图

# In[39]:


# 使用matplotlib绘制
# 生成随机数据
x = np.random.randn(10000)
y = np.random.randn(10000)

plt.figure(figsize=(8, 6))

# 绘制六边形图，gridsize控制六边形的大小
plt.hexbin(x, y, gridsize=30, cmap='Blues')

# 添加颜色条
plt.colorbar(label='Density')

plt.title("Hexbin Plot")
plt.xlabel("X")
plt.ylabel("Y")
plt.show()


# In[41]:


# 使用seaborn绘制
data = pd.DataFrame({
    'x': np.random.rand(50),
    'y': np.random.rand(50)
})

# 绘制六边形图
sns.jointplot(data=data, x="x", y="y", kind="hex", cmap="Blues")

plt.show()


# pyecharts 没有内建的六边形图功能

# In[37]:


# 使用plotly绘制（矩形网格，而不是六边形网格）
# 生成随机数据
x = np.random.randn(10000)
y = np.random.randn(10000)

# 使用plotly.express绘制六边形图
# density_heatmap() 用于绘制二维密度热图（实际上是通过对数据进行分箱并绘制矩形网格来展示数据的密度分布）。
# x=x, y=y 指定了 x 和 y 的数据，用于绘制热图。
# nbinsx=30 和 nbinsy=30 表示将数据分别分为 30 个箱子（或网格）沿 X 和 Y 轴分布。这决定了矩形网格的数量，实际上每个箱子是矩形而不是六边形。
# color_continuous_scale='Blues' 设置了颜色映射的范围，这里使用了蓝色渐变（Blues）。数据密度越高的区域会被赋予更深的颜色，密度较低的区域会呈现较浅的颜色。
fig = px.density_heatmap(x=x, y=y, nbinsx=30, nbinsy=30, color_continuous_scale='Blues')

# 设置标题和标签
fig.update_layout(
    title="六边形图 (Hexbin Plot)",
    xaxis_title="X轴",
    yaxis_title="Y轴"
)

# 显示图形
fig.show()


# ## 气泡图
# 可视化三个变量之间关系的图表类型

# In[22]:


# 使用matplotlib绘制
# 随机生成样本
x = [10, 20, 30, 40, 50]  # 横坐标
y = [15, 25, 35, 20, 10]  # 纵坐标
sizes = [100, 300, 500, 200, 400]  # 气泡大小（可以理解为“权重”或“值”）

# 创建气泡图
plt.figure(figsize=(8, 6))
# x、y：气泡的位置。
# s：每个点的大小（可以是列表或数组）。
# alpha：透明度（0~1，适合让气泡“重叠”时不太遮挡）。
# c：颜色，可以是单个颜色，也可以是颜色列表。
# edgecolors：气泡边缘的颜色。
plt.scatter(x, y, s=sizes, alpha=0.5, c='skyblue', edgecolors='black')

# 添加标签
plt.xlabel("X-axis")
plt.ylabel("Y-axis")
plt.title("Matplotlib Bubble Chart")
plt.grid(True)
plt.show()


# In[24]:


# 增加颜色映射

colors = [1, 2, 3, 4, 5]  # 颜色映射依据变量（比如代表某个数值属性）

plt.figure(figsize=(8, 6))

# 创建气泡图，使用 colormap（c与cmap）
# c=colors：使用 colors 列表指定每个气泡的“颜色权重”。
# cmap='viridis'：指定颜色映射表（常用的还有 'plasma'、'coolwarm'、'inferno' 等）。
# plt.colorbar()：添加右侧颜色图例（legend）
scatter = plt.scatter(x, y, s=sizes, c=colors, cmap='viridis', alpha=0.6, edgecolors='black')

# 添加 colorbar
cbar = plt.colorbar(scatter)
cbar.set_label('Color Map')

# 图形美化
plt.xlabel("X-axis")
plt.ylabel("Y-axis")
plt.title("Matplotlib Bubble Chart")
plt.grid(True)
plt.show()


# In[17]:


# 使用seaborn绘制

# 生成随机样本
data = pd.DataFrame({
    'x': np.random.rand(50),
    'y': np.random.rand(50),
    'size': np.random.randint(10, 100, 50),
    'category': np.random.choice(['A', 'B', 'C', 'D'], 50)
})

plt.figure(figsize=(8, 6))

# 传递x值和y值
# x='x', y='y' - 设置x轴和y轴所对应的数据列名
# size='size' - 指定用于控制点大小的数据列
# hue='category' - 指定用于控制点颜色的数据列
# palette='viridis' - 设置颜色主题
sns.scatterplot(data=data, x='x', y='y', size='size', hue='category', palette='viridis')

# 增加x轴和y轴标签，添加标题
plt.xlabel('X-axis')
plt.ylabel('Y-axis')
plt.title('Seaborn Scatter Plot')

plt.show()


# In[26]:


# 使用pyechart绘制
np.random.seed(42)
data = pd.DataFrame({
    'x': np.random.rand(50),
    'y': np.random.rand(50),
    'size': np.random.randint(10, 100, 50),
    'category': np.random.choice(['A', 'B', 'C', 'D'], 50)
})

# 绘制气泡图
scatter = (
    Scatter()
    .add_xaxis(data['x'].tolist())
    .add_yaxis(
        series_name="气泡图",
        y_axis=data.apply(lambda row: [row['y'], row['size'], row['category']], axis=1).tolist(),  # 格式: [y, size, category]
        symbol_size=1,  # 基础大小（会被visualmap覆盖）
    )
    .set_global_opts(
        title_opts=opts.TitleOpts(title="PyEcharts 2.x 气泡图"),
        xaxis_opts=opts.AxisOpts(name="X轴"),
        yaxis_opts=opts.AxisOpts(name="Y轴"),
        # 视觉映射：控制大小
        visualmap_opts=[
            opts.VisualMapOpts(
                type_="size",  # 映射类型为大小
                min_=10,       # 最小值
                max_=100,      # 最大值
                dimension=2,   # 使用数据第三列（size）控制大小
                range_size=[10, 50],  # 实际显示的像素范围
                pos_right="10%"
            ),
        ]
    )
)

scatter.render("bubble_v2_fixed.html")


# In[18]:


# 使用plotly绘制

# 生成随机生成样本
data = pd.DataFrame({
    'x': np.random.rand(50),
    'y': np.random.rand(50),
    'size': np.random.randint(10, 100, 50),
    'category': np.random.choice(['A', 'B', 'C', 'D'], 50)
})

# x='x', y='y'：指定X轴和Y轴使用的数据列
# size='size'：使用'size'列的值来调整点的大小
# color='category'：根据'category'列的值为点着色
# title='Plotly Scatter Plot'：设置图表标题
# labels={'x': 'X-axis', 'y': 'Y-axis'}：自定义轴标签
# hover_data=['category']：当鼠标悬停在点上时显示类别信息
fig = px.scatter(data, x='x', y='y', 
                 size='size', 
                 color='category',
                 title='Plotly Scatter Plot',
                 labels={'x': 'X-axis', 'y': 'Y-axis'},
                 hover_data=['category'])

# Update layout
fig.update_layout(
    plot_bgcolor='white',
    legend_title_text='Category'
)

# Show the plot
fig.show()


# In[ ]:


# 随堂练习：读取文件，绘制气泡图


# ## 等高线图

# In[26]:


# 使用matplotlib绘制

# 生成数据
# 在 x 和 y 方向上生成均匀分布的 100 个点，范围从 -2 到 2。
x = np.linspace(-2, 2, 100)
y = np.linspace(-2, 2, 100)
# np.meshgrid 将 x 和 y 的一维数组转换为二维网格坐标矩阵，X 和 Y 分别表示网格中每个点的横纵坐标。
X, Y = np.meshgrid(x, y)
# 根据网格坐标计算每个点的函数值 Z = sin(x) * cos(y)，这个就是等高线图的“高度”。
Z = np.sin(X) * np.cos(Y)

# 将函数值从 -1 到 1 均分为 20 个等级（即 20 条等高线）；定义等高线水平
levels = np.linspace(-1, 1, 20)  

# 使用 plt.contour() 画出等高线图，其中 X 和 Y 是坐标网格，Z 是函数值，levels 控制绘制哪些高度的等高线（指定等级）。
plt.contour(X, Y, Z, levels=levels) 

# 添加标签和标题
plt.xlabel('X-axis')
plt.ylabel('Y-axis')
plt.title('Contour Plot with Specific Levels')

plt.show()


# In[30]:


import plotly.graph_objects as go
import numpy as np

# 生成数据
x = np.linspace(-2, 2, 100)
y = np.linspace(-2, 2, 100)
X, Y = np.meshgrid(x, y)
Z = np.sin(X) * np.cos(Y)

# 创建 Plotly contour plot
fig = go.Figure(
    data=go.Contour(
        z=Z, # 等高线依据的函数值（高度）
        x=x, # 坐标轴的网格范围
        y=y, # 坐标轴的网格范围
        colorscale='Viridis', # 设置色带为 Viridis（从深蓝到亮黄）
        contours=dict(
            start=-1, # 最小等高值
            end=1, # 最大等高值
            size=0.1, # 每两个等高线之间的间隔
            coloring='lines'  # 只画线；可改成 'fill'（填充区域）或 'heatmap'（类似热力图）。
        )
    )
)
fig.update_layout(
    title="Plotly 等高线图",
    xaxis_title="X 轴",
    yaxis_title="Y 轴"
)
fig.show()


# Pyecharts 没有直接的 contour plot 接口
# Seaborn 并不直接支持任意函数绘制等高线，更适合用在密度估计场景。

# ## 边际图

# In[28]:


df = sns.load_dataset('iris')  # 从Seaborn中加载iris数据集

# 创建带有散点图的边际图
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='scatter')
# 创建带有六边形图的边际图
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='hex')
# 创建带有核密度估计图的边际图
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='kde')
plt.show()


# In[34]:


# 自定义联合图中的散点图
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"],
              kind='scatter', s=200, color='m',
              edgecolor="skyblue", linewidth=2)

# 自定义颜色
sns.set_theme(style="white", color_codes=True)
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='kde',
              color="skyblue")
plt.show()


# In[35]:


# 自定义直方图
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='hex',
              marginal_kws=dict(bins=30, fill=True))
plt.show()

# 无间隔
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='kde',
              color="blue", space=0)
# 大间隔
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='kde',
              color="blue", space=3)
# 调整边际图比例
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"],
              kind='kde', ratio=2)
plt.show()

