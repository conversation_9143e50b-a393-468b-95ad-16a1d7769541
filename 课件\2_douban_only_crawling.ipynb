{"cells": [{"cell_type": "code", "execution_count": 2, "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-03-17T01:47:56.709409100Z", "start_time": "2025-03-17T01:47:17.710512Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["抓取第1页的内容...\n", "1 肖申克的救赎 https://movie.douban.com/subject/1292052/ 9.7 3145315\n", "2 霸王别姬 https://movie.douban.com/subject/1291546/ 9.6 2320636\n", "3 泰坦尼克号 https://movie.douban.com/subject/1292722/ 9.5 2384747\n", "4 阿甘正传 https://movie.douban.com/subject/1292720/ 9.5 2338068\n", "5 千与千寻 https://movie.douban.com/subject/1291561/ 9.4 2429837\n", "6 美丽人生 https://movie.douban.com/subject/1292063/ 9.5 1426179\n", "7 这个杀手不太冷 https://movie.douban.com/subject/1295644/ 9.4 2470167\n", "8 星际穿越 https://movie.douban.com/subject/1889243/ 9.4 2059904\n", "9 盗梦空间 https://movie.douban.com/subject/3541415/ 9.4 2234080\n", "10 楚门的世界 https://movie.douban.com/subject/1292064/ 9.4 1914090\n", "11 辛德勒的名单 https://movie.douban.com/subject/1295124/ 9.5 1204419\n", "12 忠犬八公的故事 https://movie.douban.com/subject/3011091/ 9.4 1491051\n", "13 海上钢琴师 https://movie.douban.com/subject/1292001/ 9.3 1811959\n", "14 三傻大闹宝莱坞 https://movie.douban.com/subject/3793023/ 9.2 1999597\n", "15 放牛班的春天 https://movie.douban.com/subject/1291549/ 9.3 1418695\n", "16 疯狂动物城 https://movie.douban.com/subject/25662329/ 9.2 2145381\n", "17 机器人总动员 https://movie.douban.com/subject/2131459/ 9.3 1425609\n", "18 无间道 https://movie.douban.com/subject/1307914/ 9.3 1495572\n", "19 控方证人 https://movie.douban.com/subject/1296141/ 9.6 660515\n", "20 大话西游之大圣娶亲 https://movie.douban.com/subject/1292213/ 9.2 1648168\n", "21 熔炉 https://movie.douban.com/subject/5912992/ 9.3 1001745\n", "22 触不可及 https://movie.douban.com/subject/6786002/ 9.3 1234252\n", "23 教父 https://movie.douban.com/subject/1291841/ 9.3 1056405\n", "24 寻梦环游记 https://movie.douban.com/subject/20495023/ 9.1 1869482\n", "25 当幸福来敲门 https://movie.douban.com/subject/1849031/ 9.2 1630381\n", "抓取第2页的内容...\n", "26 末代皇帝 https://movie.douban.com/subject/1293172/ 9.3 979497\n", "27 龙猫 https://movie.douban.com/subject/1291560/ 9.2 1361487\n", "28 哈利·波特与魔法石 https://movie.douban.com/subject/1295038/ 9.2 1348628\n", "29 怦然心动 https://movie.douban.com/subject/3319755/ 9.1 1975640\n", "30 活着 https://movie.douban.com/subject/1292365/ 9.3 928753\n", "31 蝙蝠侠：黑暗骑士 https://movie.douban.com/subject/1851857/ 9.2 1148662\n", "32 指环王3：王者无敌 https://movie.douban.com/subject/1291552/ 9.3 873387\n", "33 我不是药神 https://movie.douban.com/subject/26752088/ 9.0 2270217\n", "34 乱世佳人 https://movie.douban.com/subject/1300267/ 9.3 754982\n", "35 飞屋环游记 https://movie.douban.com/subject/2129039/ 9.1 1437626\n", "36 让子弹飞 https://movie.douban.com/subject/3742360/ 9.0 1846439\n", "37 哈尔的移动城堡 https://movie.douban.com/subject/1308807/ 9.1 1218685\n", "38 素媛 https://movie.douban.com/subject/21937452/ 9.3 746714\n", "39 十二怒汉 https://movie.douban.com/subject/1293182/ 9.4 548512\n", "40 海蒂和爷爷 https://movie.douban.com/subject/25958717/ 9.3 723014\n", "41 猫鼠游戏 https://movie.douban.com/subject/1305487/ 9.1 1146271\n", "42 天空之城 https://movie.douban.com/subject/1291583/ 9.2 966098\n", "43 摔跤吧！爸爸 https://movie.douban.com/subject/26387939/ 9.0 1684278\n", "44 鬼子来了 https://movie.douban.com/subject/1291858/ 9.3 683930\n", "45 少年派的奇幻漂流 https://movie.douban.com/subject/1929463/ 9.1 1441890\n", "46 钢琴家 https://movie.douban.com/subject/1296736/ 9.3 713056\n", "47 指环王2：双塔奇兵 https://movie.douban.com/subject/1291572/ 9.2 821473\n", "48 大话西游之月光宝盒 https://movie.douban.com/subject/1299398/ 9.0 1313507\n", "49 死亡诗社 https://movie.douban.com/subject/1291548/ 9.2 825229\n", "50 何以为家 https://movie.douban.com/subject/30170448/ 9.1 1126506\n", "抓取第3页的内容...\n", "51 闻香识女人 https://movie.douban.com/subject/1298624/ 9.1 978594\n", "52 绿皮书 https://movie.douban.com/subject/27060077/ 8.9 1808231\n", "53 大闹天宫 https://movie.douban.com/subject/1418019/ 9.4 485600\n", "54 黑客帝国 https://movie.douban.com/subject/1291843/ 9.1 908675\n", "55 罗马假日 https://movie.douban.com/subject/1293839/ 9.1 1013384\n", "56 指环王1：护戒使者 https://movie.douban.com/subject/1291571/ 9.1 920880\n", "57 教父2 https://movie.douban.com/subject/1299131/ 9.3 607928\n", "58 狮子王 https://movie.douban.com/subject/1301753/ 9.1 921846\n", "59 天堂电影院 https://movie.douban.com/subject/1291828/ 9.2 719411\n", "60 饮食男女 https://movie.douban.com/subject/1291818/ 9.2 684694\n", "61 辩护人 https://movie.douban.com/subject/21937445/ 9.2 637907\n", "62 搏击俱乐部 https://movie.douban.com/subject/1292000/ 9.0 924612\n", "63 本杰明·巴顿奇事 https://movie.douban.com/subject/1485260/ 9.0 1063015\n", "64 美丽心灵 https://movie.douban.com/subject/1306029/ 9.1 827100\n", "65 穿条纹睡衣的男孩 https://movie.douban.com/subject/3008247/ 9.2 617542\n", "66 情书 https://movie.douban.com/subject/1292220/ 8.9 1243480\n", "67 窃听风暴 https://movie.douban.com/subject/1900841/ 9.2 611097\n", "68 两杆大烟枪 https://movie.douban.com/subject/1293350/ 9.1 648919\n", "69 哈利·波特与死亡圣器(下) https://movie.douban.com/subject/3011235/ 9.0 928043\n", "70 音乐之声 https://movie.douban.com/subject/1294408/ 9.1 656616\n", "71 西西里的美丽传说 https://movie.douban.com/subject/1292402/ 8.9 1055884\n", "72 阿凡达 https://movie.douban.com/subject/1652587/ 8.8 1522105\n", "73 功夫 https://movie.douban.com/subject/1291543/ 8.9 1267991\n", "74 看不见的客人 https://movie.douban.com/subject/26580232/ 8.8 1385153\n", "75 哈利·波特与阿兹卡班的囚徒 https://movie.douban.com/subject/1291544/ 9.0 844615\n", "抓取第4页的内容...\n", "76 拯救大兵瑞恩 https://movie.douban.com/subject/1292849/ 9.1 698136\n", "77 小鞋子 https://movie.douban.com/subject/1303021/ 9.2 445799\n", "78 沉默的羔羊 https://movie.douban.com/subject/1293544/ 8.9 974493\n", "79 飞越疯人院 https://movie.douban.com/subject/1292224/ 9.1 584442\n", "80 布达佩斯大饭店 https://movie.douban.com/subject/11525673/ 8.9 1037237\n", "81 蝴蝶效应 https://movie.douban.com/subject/1292343/ 8.9 1022892\n", "82 禁闭岛 https://movie.douban.com/subject/2334904/ 8.9 1067484\n", "83 致命魔术 https://movie.douban.com/subject/1780330/ 8.9 930289\n", "84 心灵捕手 https://movie.douban.com/subject/1292656/ 9.0 784956\n", "85 低俗小说 https://movie.douban.com/subject/1291832/ 8.9 916746\n", "86 超脱 https://movie.douban.com/subject/5322596/ 9.0 685585\n", "87 摩登时代 https://movie.douban.com/subject/1294371/ 9.3 331094\n", "88 哈利·波特与密室 https://movie.douban.com/subject/1296996/ 8.9 870203\n", "89 喜剧之王 https://movie.douban.com/subject/1302425/ 8.8 1049259\n", "90 致命ID https://movie.douban.com/subject/1297192/ 8.9 906318\n", "91 杀人回忆 https://movie.douban.com/subject/1300299/ 8.9 796557\n", "92 春光乍泄 https://movie.douban.com/subject/1292679/ 9.0 684732\n", "93 海豚湾 https://movie.douban.com/subject/3442220/ 9.3 374343\n", "94 一一 https://movie.douban.com/subject/1292434/ 9.1 461208\n", "95 美国往事 https://movie.douban.com/subject/1292262/ 9.1 452175\n", "96 加勒比海盗 https://movie.douban.com/subject/1298070/ 8.8 930956\n", "97 红辣椒 https://movie.douban.com/subject/1865703/ 9.0 534716\n", "98 七宗罪 https://movie.douban.com/subject/1292223/ 8.8 1016096\n", "99 唐伯虎点秋香 https://movie.douban.com/subject/1306249/ 8.7 1176717\n", "100 狩猎 https://movie.douban.com/subject/6985810/ 9.1 443540\n", "抓取第5页的内容...\n", "101 7号房的礼物 https://movie.douban.com/subject/10777687/ 8.9 601926\n", "102 甜蜜蜜 https://movie.douban.com/subject/1305164/ 8.9 636012\n", "103 蝙蝠侠：黑暗骑士崛起 https://movie.douban.com/subject/3395373/ 8.9 783531\n", "104 寄生虫 https://movie.douban.com/subject/27010768/ 8.8 1488472\n", "105 被嫌弃的松子的一生 https://movie.douban.com/subject/1787291/ 8.8 751036\n", "106 天书奇谭 https://movie.douban.com/subject/1428581/ 9.2 310953\n", "107 超能陆战队 https://movie.douban.com/subject/11026735/ 8.8 1103113\n", "108 爱在黎明破晓前 https://movie.douban.com/subject/1296339/ 8.8 759324\n", "109 第六感 https://movie.douban.com/subject/1297630/ 8.9 610076\n", "110 重庆森林 https://movie.douban.com/subject/1291999/ 8.8 887105\n", "111 爱在日落黄昏时 https://movie.douban.com/subject/1291990/ 8.9 622157\n", "112 幽灵公主 https://movie.douban.com/subject/1297359/ 8.9 570059\n", "113 入殓师 https://movie.douban.com/subject/2149806/ 8.9 722878\n", "114 剪刀手爱德华 https://movie.douban.com/subject/1292370/ 8.7 1094150\n", "115 断背山 https://movie.douban.com/subject/1418834/ 8.8 755643\n", "116 菊次郎的夏天 https://movie.douban.com/subject/1293359/ 8.9 649813\n", "117 勇敢的心 https://movie.douban.com/subject/1294639/ 8.9 590210\n", "118 未麻的部屋 https://movie.douban.com/subject/1395091/ 9.1 398052\n", "119 借东西的小人阿莉埃蒂 https://movie.douban.com/subject/4202302/ 8.9 603128\n", "120 哈利·波特与火焰杯 https://movie.douban.com/subject/1309055/ 8.8 760586\n", "121 茶馆 https://movie.douban.com/subject/1461403/ 9.6 187445\n", "122 消失的爱人 https://movie.douban.com/subject/21318488/ 8.7 1036700\n", "123 时空恋旅人 https://movie.douban.com/subject/10577869/ 8.8 751555\n", "124 无人知晓 https://movie.douban.com/subject/1292337/ 9.1 357194\n", "125 头脑特工队 https://movie.douban.com/subject/10533913/ 8.8 752442\n", "抓取第6页的内容...\n"]}, {"ename": "ProxyError", "evalue": "HTTPSConnectionPool(host='movie.douban.com', port=443): Max retries exceeded with url: /top250?start=125&filter= (Caused by ProxyError('Cannot connect to proxy.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)))", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mConnectionResetError\u001b[0m                      <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\urllib3\\connectionpool.py:700\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    699\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_new_proxy_conn \u001b[38;5;129;01mand\u001b[39;00m http_tunnel_required:\n\u001b[1;32m--> 700\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_prepare_proxy\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    702\u001b[0m \u001b[38;5;66;03m# Make the request on the httplib connection object.\u001b[39;00m\n", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\urllib3\\connectionpool.py:996\u001b[0m, in \u001b[0;36mHTTPSConnectionPool._prepare_proxy\u001b[1;34m(self, conn)\u001b[0m\n\u001b[0;32m    994\u001b[0m     conn\u001b[38;5;241m.\u001b[39mtls_in_tls_required \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[1;32m--> 996\u001b[0m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\urllib3\\connection.py:419\u001b[0m, in \u001b[0;36mHTTPSConnection.connect\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    417\u001b[0m     context\u001b[38;5;241m.\u001b[39mload_default_certs()\n\u001b[1;32m--> 419\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock \u001b[38;5;241m=\u001b[39m \u001b[43mssl_wrap_socket\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    420\u001b[0m \u001b[43m    \u001b[49m\u001b[43msock\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    421\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkeyfile\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkey_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    422\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcertfile\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcert_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    423\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkey_password\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkey_password\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    424\u001b[0m \u001b[43m    \u001b[49m\u001b[43mca_certs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mca_certs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    425\u001b[0m \u001b[43m    \u001b[49m\u001b[43mca_cert_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mca_cert_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    426\u001b[0m \u001b[43m    \u001b[49m\u001b[43mca_cert_data\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mca_cert_data\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    427\u001b[0m \u001b[43m    \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mserver_hostname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    428\u001b[0m \u001b[43m    \u001b[49m\u001b[43mssl_context\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    429\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtls_in_tls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtls_in_tls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    430\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    432\u001b[0m \u001b[38;5;66;03m# If we're using all defaults and the connection\u001b[39;00m\n\u001b[0;32m    433\u001b[0m \u001b[38;5;66;03m# is TLSv1 or TLSv1.1 we throw a DeprecationWarning\u001b[39;00m\n\u001b[0;32m    434\u001b[0m \u001b[38;5;66;03m# for the host.\u001b[39;00m\n", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\urllib3\\util\\ssl_.py:449\u001b[0m, in \u001b[0;36mssl_wrap_socket\u001b[1;34m(sock, keyfile, certfile, cert_reqs, ca_certs, server_hostname, ssl_version, ciphers, ssl_context, ca_cert_dir, key_password, ca_cert_data, tls_in_tls)\u001b[0m\n\u001b[0;32m    448\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m send_sni:\n\u001b[1;32m--> 449\u001b[0m     ssl_sock \u001b[38;5;241m=\u001b[39m \u001b[43m_ssl_wrap_socket_impl\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    450\u001b[0m \u001b[43m        \u001b[49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcontext\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtls_in_tls\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mserver_hostname\u001b[49m\n\u001b[0;32m    451\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    452\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m:\n", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\urllib3\\util\\ssl_.py:493\u001b[0m, in \u001b[0;36m_ssl_wrap_socket_impl\u001b[1;34m(sock, ssl_context, tls_in_tls, server_hostname)\u001b[0m\n\u001b[0;32m    492\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m server_hostname:\n\u001b[1;32m--> 493\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mssl_context\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwrap_socket\u001b[49m\u001b[43m(\u001b[49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mserver_hostname\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    494\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[1;32mC:\\Python310\\lib\\ssl.py:513\u001b[0m, in \u001b[0;36mSSLContext.wrap_socket\u001b[1;34m(self, sock, server_side, do_handshake_on_connect, suppress_ragged_eofs, server_hostname, session)\u001b[0m\n\u001b[0;32m    507\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mwrap_socket\u001b[39m(\u001b[38;5;28mself\u001b[39m, sock, server_side\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[0;32m    508\u001b[0m                 do_handshake_on_connect\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[0;32m    509\u001b[0m                 suppress_ragged_eofs\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[0;32m    510\u001b[0m                 server_hostname\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, session\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[0;32m    511\u001b[0m     \u001b[38;5;66;03m# SSLSocket class handles server_hostname encoding before it calls\u001b[39;00m\n\u001b[0;32m    512\u001b[0m     \u001b[38;5;66;03m# ctx._wrap_socket()\u001b[39;00m\n\u001b[1;32m--> 513\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msslsocket_class\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_create\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    514\u001b[0m \u001b[43m        \u001b[49m\u001b[43msock\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    515\u001b[0m \u001b[43m        \u001b[49m\u001b[43mserver_side\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mserver_side\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    516\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdo_handshake_on_connect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdo_handshake_on_connect\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    517\u001b[0m \u001b[43m        \u001b[49m\u001b[43msuppress_ragged_eofs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msuppress_ragged_eofs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    518\u001b[0m \u001b[43m        \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mserver_hostname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    519\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcontext\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m    520\u001b[0m \u001b[43m        \u001b[49m\u001b[43msession\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msession\u001b[49m\n\u001b[0;32m    521\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mC:\\Python310\\lib\\ssl.py:1071\u001b[0m, in \u001b[0;36mSSLSocket._create\u001b[1;34m(cls, sock, server_side, do_handshake_on_connect, suppress_ragged_eofs, server_hostname, context, session)\u001b[0m\n\u001b[0;32m   1070\u001b[0m             \u001b[38;5;28;01<PERSON><PERSON>se\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdo_handshake_on_connect should not be specified for non-blocking sockets\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m-> 1071\u001b[0m         \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdo_handshake\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1072\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (\u001b[38;5;167;01mOS<PERSON>rror\u001b[39;00m, \u001b[38;5;167;01mValueError\u001b[39;00m):\n", "File \u001b[1;32mC:\\Python310\\lib\\ssl.py:1342\u001b[0m, in \u001b[0;36mSSLSocket.do_handshake\u001b[1;34m(self, block)\u001b[0m\n\u001b[0;32m   1341\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msettimeout(\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m)\n\u001b[1;32m-> 1342\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_sslobj\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdo_handshake\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1343\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "\u001b[1;31mConnectionResetError\u001b[0m: [WinError 10054] 远程主机强迫关闭了一个现有的连接。", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mMaxRetryError\u001b[0m                             Traceback (most recent call last)", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\requests\\adapters.py:667\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[1;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[0;32m    666\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 667\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    668\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    669\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    670\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    671\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    672\u001b[0m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    673\u001b[0m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    674\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    675\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    676\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    677\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    678\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    679\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    681\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\urllib3\\connectionpool.py:787\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    785\u001b[0m     e \u001b[38;5;241m=\u001b[39m ProtocolError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection aborted.\u001b[39m\u001b[38;5;124m\"\u001b[39m, e)\n\u001b[1;32m--> 787\u001b[0m retries \u001b[38;5;241m=\u001b[39m \u001b[43mretries\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mincrement\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    788\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43me\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_pool\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msys\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexc_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[0;32m    789\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    790\u001b[0m retries\u001b[38;5;241m.\u001b[39msleep()\n", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\urllib3\\util\\retry.py:592\u001b[0m, in \u001b[0;36mRetry.increment\u001b[1;34m(self, method, url, response, error, _pool, _stacktrace)\u001b[0m\n\u001b[0;32m    591\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m new_retry\u001b[38;5;241m.\u001b[39mis_exhausted():\n\u001b[1;32m--> 592\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m MaxRetryError(_pool, url, error \u001b[38;5;129;01mor\u001b[39;00m ResponseError(cause))\n\u001b[0;32m    594\u001b[0m log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mIncremented Retry for (url=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m): \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, url, new_retry)\n", "\u001b[1;31mMaxRetryError\u001b[0m: HTTPSConnectionPool(host='movie.douban.com', port=443): Max retries exceeded with url: /top250?start=125&filter= (Caused by ProxyError('Cannot connect to proxy.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)))", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mProxyError\u001b[0m                                Traceback (most recent call last)", "Cell \u001b[1;32mIn[2], line 15\u001b[0m\n\u001b[0;32m     13\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m抓取第\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m+\u001b[39m\u001b[38;5;28mstr\u001b[39m(page)\u001b[38;5;241m+\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m页的内容...\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     14\u001b[0m url_visit \u001b[38;5;241m=\u001b[39m top250_url\u001b[38;5;241m.\u001b[39mformat(start)\n\u001b[1;32m---> 15\u001b[0m req \u001b[38;5;241m=\u001b[39m \u001b[43mrequests\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl_visit\u001b[49m\u001b[43m,\u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# 注意加入headers\u001b[39;00m\n\u001b[0;32m     16\u001b[0m req\u001b[38;5;241m.\u001b[39mencoding \u001b[38;5;241m=\u001b[39m req\u001b[38;5;241m.\u001b[39mapparent_encoding\n\u001b[0;32m     17\u001b[0m content \u001b[38;5;241m=\u001b[39m req\u001b[38;5;241m.\u001b[39mtext\n", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\requests\\api.py:73\u001b[0m, in \u001b[0;36mget\u001b[1;34m(url, params, **kwargs)\u001b[0m\n\u001b[0;32m     62\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mget\u001b[39m(url, params\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m     63\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124mr\u001b[39m\u001b[38;5;124;03m\"\"\"Sends a GET request.\u001b[39;00m\n\u001b[0;32m     64\u001b[0m \n\u001b[0;32m     65\u001b[0m \u001b[38;5;124;03m    :param url: URL for the new :class:`Request` object.\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m     70\u001b[0m \u001b[38;5;124;03m    :rtype: requests.Response\u001b[39;00m\n\u001b[0;32m     71\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[1;32m---> 73\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m request(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mget\u001b[39m\u001b[38;5;124m\"\u001b[39m, url, params\u001b[38;5;241m=\u001b[39mparams, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\requests\\api.py:59\u001b[0m, in \u001b[0;36mrequest\u001b[1;34m(method, url, **kwargs)\u001b[0m\n\u001b[0;32m     55\u001b[0m \u001b[38;5;66;03m# By using the 'with' statement we are sure the session is closed, thus we\u001b[39;00m\n\u001b[0;32m     56\u001b[0m \u001b[38;5;66;03m# avoid leaving sockets open which can trigger a ResourceWarning in some\u001b[39;00m\n\u001b[0;32m     57\u001b[0m \u001b[38;5;66;03m# cases, and look like a memory leak in others.\u001b[39;00m\n\u001b[0;32m     58\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m sessions\u001b[38;5;241m.\u001b[39mSession() \u001b[38;5;28;01mas\u001b[39;00m session:\n\u001b[1;32m---> 59\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m session\u001b[38;5;241m.\u001b[39mrequest(method\u001b[38;5;241m=\u001b[39mmethod, url\u001b[38;5;241m=\u001b[39murl, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\requests\\sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[1;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[0;32m    584\u001b[0m send_kwargs \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m    585\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtimeout\u001b[39m\u001b[38;5;124m\"\u001b[39m: timeout,\n\u001b[0;32m    586\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mallow_redirects\u001b[39m\u001b[38;5;124m\"\u001b[39m: allow_redirects,\n\u001b[0;32m    587\u001b[0m }\n\u001b[0;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[1;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msend(prep, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39msend_kwargs)\n\u001b[0;32m    591\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\requests\\sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[1;34m(self, request, **kwargs)\u001b[0m\n\u001b[0;32m    700\u001b[0m start \u001b[38;5;241m=\u001b[39m preferred_clock()\n\u001b[0;32m    702\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[1;32m--> 703\u001b[0m r \u001b[38;5;241m=\u001b[39m adapter\u001b[38;5;241m.\u001b[39msend(request, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m    705\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n\u001b[0;32m    706\u001b[0m elapsed \u001b[38;5;241m=\u001b[39m preferred_clock() \u001b[38;5;241m-\u001b[39m start\n", "File \u001b[1;32mC:\\Python310\\lib\\site-packages\\requests\\adapters.py:694\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[1;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[0;32m    691\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m RetryError(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[0;32m    693\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e\u001b[38;5;241m.\u001b[39mreason, _ProxyError):\n\u001b[1;32m--> 694\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ProxyError(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[0;32m    696\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(e\u001b[38;5;241m.\u001b[39mreason, _SSLError):\n\u001b[0;32m    697\u001b[0m     \u001b[38;5;66;03m# This branch is for urllib3 v1.22 and later.\u001b[39;00m\n\u001b[0;32m    698\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m SSLError(e, request\u001b[38;5;241m=\u001b[39mrequest)\n", "\u001b[1;31mProxyError\u001b[0m: HTTPSConnectionPool(host='movie.douban.com', port=443): Max retries exceeded with url: /top250?start=125&filter= (Caused by ProxyError('Cannot connect to proxy.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None)))"]}], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "import time\n", "\n", "top250_url = \"https://movie.douban.com/top250?start={}&filter=\"\n", "headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.99 Safari/537.36'}\n", "\n", "page = 0\n", "\n", "for i in range(10):\n", "    start = i*25\n", "    page += 1\n", "    print('抓取第'+str(page)+'页的内容...')\n", "    url_visit = top250_url.format(start)\n", "    req = requests.get(url_visit,headers=headers)  # 注意加入headers\n", "    req.encoding = req.apparent_encoding\n", "    content = req.text\n", "    time.sleep(2)\n", "\n", "    # 解析\n", "    soup = BeautifulSoup(content, 'html.parser')\n", "    all_item_divs = soup.find_all('div',class_='item')\n", "    \n", "    # 之前给的代码\n", "    # for each_item in all_item_divs:\n", "    #     pic_div = each_item.find(class_='pic')\n", "    #     rank = pic_div.find('em').string  # 排名\n", "    #     title = pic_div.find('img')['alt']  #电影名称\n", "    #     url = pic_div.find('a')['href']      #电影链接\n", "    # \n", "    #     star_div = each_item.find(class_='star')\n", "    #     avg_rating = star_div.find('span',class_='rating_num').string  # 平均得分\n", "    #     rating_num = star_div.find_all('span')[3].get_text()[:-3]  # 评分人数\n", "    # \n", "    #     print(rank,title,url,avg_rating,rating_num)\n", "    \n", "    # 新代码\n", "    for each_item in all_item_divs:\n", "        pic_div = each_item.find(class_='pic')\n", "        rank = pic_div.find('em').string  # 排名\n", "        title = pic_div.find('img')['alt']  #电影名称\n", "        url = pic_div.find('a')['href']      #电影链接\n", "    \n", "        star_div = each_item.find(class_='bd')\n", "        attributes_div = star_div.find('div')\n", "        avg_rating = attributes_div.find('span',class_='rating_num').string  # 平均得分\n", "        rating_num = star_div.find_all('span')[3].get_text()[:-3]  # 评分人数\n", "    \n", "        print(rank,title,url,avg_rating,rating_num)"]}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": [], "metadata": {"collapsed": false}, "id": "9459cb2b13acf70a"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}