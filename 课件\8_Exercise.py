#!/usr/bin/env python
# coding: utf-8

# # Lecture 8: Exercise

# In[1]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Scatter
import random
import plotly.express as px


# ### 随堂练习(1)
# 生成一个数据框，包含行名为length、列名为width的100个数据点，其中每个数值为500～1000之间整数
# 
# 根据上述数据框绘制散点图
# 
# 扩充数据点为10000个，观察是否需要改为六边形图？
# 

# In[2]:


data = {
    'length': np.random.randint(500, 1001, size=100),
    'width': np.random.randint(500, 1001, size=100)
}
df = pd.DataFrame(data)
df.head()


# In[3]:


plt.figure(figsize=(8, 6))
sns.scatterplot(data=df, x='length', y='width', alpha=0.7)
plt.title('Scatter Plot of Length vs Width (100 Points)')
plt.show()


# In[4]:


# 生成10,000个数据点
large_data = {
    'length': np.random.randint(500, 1001, size=10000),
    'width': np.random.randint(500, 1001, size=10000)
}
large_df = pd.DataFrame(large_data)
large_df


# In[5]:


plt.figure(figsize=(8, 6))
sns.scatterplot(data=large_df, x='length', y='width', alpha=0.5)
plt.title('Scatter Plot of Length vs Width (10,000 Points)')
plt.show()


# In[5]:


plt.hexbin(large_df['length'],large_df['width'], gridsize=30, cmap='Blues')
plt.show()


# In[6]:


plt.figure(figsize=(10, 8))
sns.jointplot(data=large_df, x='length', y='width', kind='hex', color='blue')
plt.suptitle('Hexbin Plot of Length vs Width (10,000 Points)', y=1.02)
plt.show()


# ### 随堂练习(2)
# 
# 生成一个数据框，包含行名为length、列名为width的100个数据点，其中每个数值为500～1000之间整数，并为这100个数据点新增一个参考大小的值
# 
# 根据上述数据框绘制气泡图
# 
# 

# In[8]:


data = {
    'length': np.random.randint(500, 1001, size=100),
    'width': np.random.randint(500, 1001, size=100)
}
df = pd.DataFrame(data)
df['ratio']=df['length']/df['width']
df.head()


# In[9]:


# 创建气泡图
plt.figure(figsize=(8, 6))

plt.scatter(df['length'], df['width'], s=df['ratio'], alpha=0.5, c='skyblue', edgecolors='black')

# 添加标签
plt.xlabel("X-axis")
plt.ylabel("Y-axis")
plt.title("Example Bubble Chart")
plt.grid(True)
plt.show()


# In[10]:


# 气泡大小不明显

df['ratio'] = df['ratio']*100
plt.figure(figsize=(8, 6))

plt.scatter(df['length'], df['width'], s=df['ratio'], alpha=0.5, c='skyblue', edgecolors='black')

# 添加标签
plt.xlabel("X-axis")
plt.ylabel("Y-axis")
plt.title("Example Bubble Chart")
plt.grid(True)
plt.show()


# ### 随堂练习(3)
# 读取文件steam_game_data.csv
# 
# 
# 
利用气泡图可视化——x轴为价格，y轴为好评率，大小由ranking控制，排名越高气泡越大
# 

# In[11]:


# 读取数据
df = pd.read_csv('steam_game_data.csv')

# 数据预处理
def clean_price(price):
    if price == 'Free To Play':
        return 0
    elif isinstance(price, str) and '$' in price:
        return float(price.replace('$', ''))
    else:
        return float(price)  # 如果已经是数字则直接转换

# 定义转换函数：移除 % 并除以 100 → 转换为 0.9（浮点数）
def percent_to_float(user_review_percentage):
    return float(user_review_percentage.replace('%',''))

df['price'] = df['price'].apply(clean_price)
df_new = df[~df['user_review_percentage'].isna()]
df_new['user_review_percentage'] = df_new['user_review_percentage'].apply(percent_to_float)

# 检查
print(df_new)


# In[12]:


fig = px.scatter(df_new, x='price', y='user_review_percentage', 
                 size='rank', 
                 title='Plotly Scatter Plot',
                 labels={'price': 'price', 'user_review_percentage': 'user review'},
                 hover_data=['rank'])

# Update layout
fig.update_layout(
    yaxis_range=[0, 100],  # 强制y轴从0%到100%
    plot_bgcolor='white',
    xaxis_title='价格 (USD)',
    yaxis_title='好评率 (%)'
)

# Show the plot
fig.show()


# In[42]:


plt.figure(figsize=(8, 6))

# 传递x值和y值
# x='x', y='y' - 设置x轴和y轴所对应的数据列名
# size='size' - 指定用于控制点大小的数据列
sns.scatterplot(data=df_new, x='price', y='user_review_percentage', size='rank')

# 增加x轴和y轴标签，添加标题
plt.xlabel('X-axis')
plt.ylabel('Y-axis')
plt.title('Seaborn Scatter Plot')

plt.show()


# In[17]:


# 但是ranking和气泡大小是相反的，因此要做处理
df_new['new_rank'] = 20 - df_new['rank']
# df_new.loc[:, 'new_rank'] = 20 - df_new['rank'] # 该代码可以消除警告
df_new


# In[16]:


fig = px.scatter(df_new, x='price', y='user_review_percentage', 
                 size='new_rank', 
                 title='Plotly Scatter Plot',
                 labels={'price': 'price', 'user_review_percentage': 'user review'},
                 hover_data=['rank'])

# Update layout
fig.update_layout(
    yaxis_range=[0, 100],  # 强制y轴从0%到100%
    plot_bgcolor='white',
    xaxis_title='价格 (USD)',
    yaxis_title='好评率 (%)'
)

# Show the plot
fig.show()


# In[ ]:




