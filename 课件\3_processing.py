#!/usr/bin/env python
# coding: utf-8

# ## Lecture 3: 序列和数据框处理 `Pandas`

# ### 关于Pandas
# * 主要的数据结构有三种，Series, DataFrame, Panel

# ### 创建Series

# #### 1）Data是多维数组

# In[3]:


import pandas as pd
import numpy as np
s = pd.Series(np.random.randn(5),index = ['a','b','c','d','e'])  # 索引index长度必须与data长度一致
print(s)


# In[2]:


t = pd.Series(np.random.randn(5)) # 没有指定index参数时，默认创建数值型索引
print(t)


# In[3]:


n = pd.Series(['Nanjing','Hangzhou','Fuzhou'])
print(n)


# #### 2）Data是字典

# In[ ]:


# 未设置index参数时，如果Python版本>=3.6且Pandas版本>=0.23，Series按照字典的插入顺序排列索引；否则按字母顺序排序字典的key列表
d = {'b': 1, 'a': 0, 'c': 2}
d1 = pd.Series(d)
print(d1)


# In[6]:


# 如果设置了index参数，则按索引标签提取data里面的值。使用NaN (Not a Number)表示缺失数据
d = {'b': 1, 'a': 0, 'c': 2}
d2 = pd.Series(d, index=['b', 'c', 'd', 'a'])
print(d2)


# #### 3）Data是标量值

# In[7]:


# 必须提供索引。Series按索引长度重复该标量值
e = pd.Series(5., index=['a', 'b', 'c', 'd', 'e'])
print(e)


# ### 读取Series数据

# In[8]:


# 使用序列的属性 values 和 index 得到数据值和索引值
import pandas as pd
e = pd.Series(5., index=['a', 'b', 'c', 'd', 'e'])
print(e.values)
print(e.index)


# In[9]:


# 通过索引的方式选择 Series序列对象中的值
s = pd.Series(['Python','SQL','Java','English'], index=['a', 'b', 'c', 'd'])
print(s['d'])


# In[10]:


# 支持索引切片
print(s['c':'d'])


# ### 创建DataFrame

# #### 1) 从字典中创建DataFrame

# In[40]:


d = {'one': [1., 2., 3., 4.],
     'two': [6., 7., 8., 9.]}
df = pd.DataFrame(d)
df


# #### 2）从Series字典中创建DataFrame

# In[41]:


d = {'one': pd.Series([1., 2., 3.], index=['a', 'b', 'c']),
     'two': pd.Series([1., 2., 3., 4.], index=['a', 'b', 'c', 'd'])}
pd.DataFrame(d)


# In[50]:


s1 = pd.Series({'city': 'Nanjing',
                'province': 'Jiangsu',
                'population': 8.335})
s2 = pd.Series({'city': 'Hangzhou',
                'province': 'Zhejiang',
                'population': 9.468})
s3 = pd.Series({'city': 'Fuzhou',
                'province': 'Fujian',
                'population': 7.64})
df = pd.DataFrame([s1, s2, s3])
df


# #### 3) 从Numpy二维数据中创建数据框。

# In[120]:


import numpy as np
data = np.random.randint(0,150,size=(4,4))
index = ['张三','李四','王五','赵六']
columns = ['语文','数学','英语','python']
df = pd.DataFrame(data=data, index = index, columns = columns)
df


# #### 4）指定index和columns

# In[52]:


# 原本数据集不包含index时，指定index将为其赋予新的index
d = {'one': [1., 2., 3., 4.],
     'two': [6., 7., 8., 9.]}
pd.DataFrame(d, index = ['a','b','c','d'])


# In[55]:


# 原本数据集包含index时，指定index将仅展示所指定的index（行）
d = {'one': pd.Series([1., 2., 3.], index=['a', 'b', 'c']),
     'two': pd.Series([1., 2., 3., 4.], index=['a', 'b', 'c', 'd'])}
df = pd.DataFrame(d, index = ['a','b','c','e'], columns = ['two','three'])
df


# #### 5) 访问行和列标签

# In[56]:


df.index


# In[57]:


df.columns


# ### 读取数据：CSV， EXCEL

# In[21]:


# 从csv文件中读取数据
r = pd.read_csv('data/Fish.csv')
r


# In[24]:


# 只输出前几行
r.head()


# In[33]:


# 从Excel文件中读取数据
r = pd.read_excel('data/fish_new.xlsx',sheet_name = 'Fish')
r.head()


# ### 写入数据：CSV

# #### 写入到csv文件中

# In[37]:


import pandas as pd
df = pd.DataFrame(
    dict(A=range(1, 4), B=range(4, 7), C=range(7, 10)),
    columns=['A','B','C'],
    index=['x','y','z'],
)
df


# In[38]:


df.to_csv('data/test1.csv')


# ### 切片和查询

# #### 1）查看DataFrame的头部和尾部数据

# In[60]:


import pandas as pd
r = pd.read_csv('data/Fish.csv')
r.head(8)


# In[61]:


r.tail()


# #### 2) 描述与排序

# In[62]:


r.describe()


# In[64]:


# 转置数据
r.T


# In[69]:


# 按轴排序 （行标签 or 列标签）
r.sort_index(axis = 1, ascending = False)


# In[72]:


# 按某一列的值排序
r.sort_values(by  = 'Width')


# #### 3） 选择列：使用列索引

# In[79]:


r['Weight'].head()
r[['Weight','Length1']].head()


# #### 4) 选择行：使用行号

# In[78]:


r[0:10:2]


# #### 5) 同时选择行和列，使用属性 `loc`，按索引值选择

# In[80]:


r.loc[0:10:2,['Weight','Length1']]


# #### 6) 同时选择行和列，使用属性 `iloc`，按位置选择

# In[82]:


r.iloc[0:10:2,0:10:2]


# In[83]:


r.iloc[[10,15],]


# #### 7) 布尔索引，找出满足某个条件的值

# In[85]:


r[r.Weight>500].head()


# In[90]:


r[r.Species == 'Bream']
r[r['Species'] == 'Bream']
r[r['Species'].isin(['Bream','Pike'])]


# ### 数据框的运算

# #### 1）简单的运算：加减乘除

# In[91]:


# Series的运算，将在index上对齐后计算
s1 = pd.Series([7.3, -2.5, 3.4, 1.5], index = ['a', 'c', 'd', 'e'])
s2 = pd.Series([-2.1, 3.6, -1.5, 4, 3.1], index = ['a', 'c', 'e', 'f', 'g'])
s1 + s2


# In[92]:


# DataFrame的运算，将分别在行和列的索引上对齐后计算
df1 = pd.DataFrame(np.arange(9).reshape((3,3)), columns = list('bcd'), index = ['Ohio', 'Texas', 'Colorado'])
df2 = pd.DataFrame(np.arange(12).reshape((4,3)), columns = list('bde'), index = ['Utah', 'Ohio', 'Texas', 'Oregon'])
df1 + df2


# In[93]:


# 填充默认值
df1 = pd.DataFrame(np.arange(9).reshape((3,3)), columns = list('bcd'), index = ['Ohio', 'Texas', 'Colorado'])
df2 = pd.DataFrame(np.arange(12).reshape((4,3)), columns = list('bde'), index = ['Utah', 'Ohio', 'Texas', 'Oregon'])
df1.add(df2, fill_value = -100)


# #### 2) 一些常见的统计函数

# In[96]:


df = pd.read_csv('data/Fish.csv')
df.mean()


# In[97]:


df['Width'].mean()


# In[99]:


df.mean(1)   # 在轴1上执行均值计算（即，每行计算均值）


# #### 3）聚合：Groupby函数

# In[105]:


grouped = df.groupby(by='Species')  # 按照列 Species进行分组
grouped  # 返回一个groupby分组对象


# In[107]:


# 对groupby分组对象使用统计函数
grouped.size()
grouped.sum()
grouped.mean()


# In[115]:


# 按照多个列/变量进行分组
df1 = pd.DataFrame(
    dict(A=['Ana','Ana','Bob','Bob'], 
         B=['English','English','English','Math'],
         C=range(7, 11)),
    columns=['A','B','C'],
    index=['x','y','z','t'],
)
df1


# In[116]:


df1.groupby(['A','B']).count()


# ### 添加、删除、修改操作

# #### 1）添加新列

# In[123]:


df = pd.read_csv('data/Fish.csv')
df['new_column'] = -100    # 添加一列，具有相同的取值
df.head()


# In[131]:


# 可添加多个具有实际含义的列
df['total_length'] = df['Length1'] + df['Length2'] + df['Length3']
df['ratio'] = df['Height']/df['Width']
df.iloc[0:5,-4:]   # 只展示前5行，最后4列


# #### 2) 添加新行

# In[132]:


df1 = pd.DataFrame(
    {
    "A": ["A0", "A1", "A2", "A3"],
    "B": ["B0", "B1", "B2", "B3"],
    "C": ["C0", "C1", "C2", "C3"],
    "D": ["D0", "D1", "D2", "D3"],},
    index=[0, 1, 2, 3],
)
df1


# In[133]:


df2 = pd.DataFrame(
    {
    "A": ["A4", "A5", "A6", "A7"],
    "B": ["B4", "B5", "B6", "B7"],
    "C": ["C4", "C5", "C6", "C7"],
    "D": ["D4", "D5", "D6", "D7"],},
    index=[0, 1, 2, 3],
)
df2


# In[134]:


# 将两个数据集按照行连接：df1与df2的变量名完全相同
result = df1.append(df2)
result


# In[135]:


df4 = pd.DataFrame(
    {
        "B": ["B2", "B3", "B6", "B7"],
        "D": ["D2", "D3", "D6", "D7"],
        "F": ["F2", "F3", "F6", "F7"],},
    index=[2, 3, 6, 7],
)
df4


# In[139]:


# 将df1与df4按照行连接，对索引值index重新编号，且两个数据集的列不完全相同时，生成的新数据集为所有列的并集
result = df1.append(df4, ignore_index=True)
result 


# In[146]:


# 也可以使用concat进行行或者列的添加
result = pd.concat([df1,df4[0:3]], axis = 1)  # 沿着轴1（列）进行添加
result


# In[147]:


# 也可以使用concat进行行或者列的添加
result = pd.concat([df1,df4[0:3]], axis = 0)  # 沿着轴0（行）进行添加
result


# #### 3）删除数据

# In[161]:


df = pd.read_csv('data/Fish.csv')
df['new_column'] = -100    
del df['new_column']  # 使用del来删除列
# 使用pop来删除列，并返回删除的内容
popped_var = df.pop('Width')
popped_var


# In[168]:


# 使用drop进行删除行或者列
df.drop([0,3]).loc[0:6,]


# In[170]:


df.drop(['Height','Weight'],axis=1).head()


# #### 4) 修改行或者列标签（索引名）

# In[180]:


df.rename({'Weight':'Weight_new','Height':'Height_new'},axis = 1).head()
# df.rename(columns={'Weight':'Weight_new','Height':'Height_new'}).head()


# #### 5) 修改某些数据

# In[183]:


# 使用前面讲过的查找数据（切片）的方法来定位要修改的数据，然后通过赋值操作进行修改。
# 找到Sepecies为Bream的行，对这些行的Weight变量进行修改
df.loc[df['Species']=='Bream', 'Weight'] = -1000
df.head()


# ### 处理缺失值

# #### 1）发现缺失值

# In[184]:


type(np.nan)  # NaN和None的区别


# In[185]:


type(None)


# In[186]:


data = pd.Series([1,np.nan,'Hello',None])
data.isnull()


# #### 2）剔除缺失值

# In[187]:


df = pd.DataFrame([[1, np.nan, 2],
                   [2, 3, 5],
                   [np.nan, 4, 6]])
df


# In[188]:


df.dropna()  # 默认删除所有包含缺失值的数据


# In[191]:


df.dropna(axis='columns')  # 删除包含缺失值的列


# In[192]:


# 删除绝大多数是缺失值的行或列，则可以设置how或者thresh参数。
df[3] = np.nan
df


# In[195]:


# 删除全部是缺失值的列
df.dropna(axis='columns',how='all')


# In[201]:


# thresh是阈值，表示至少有thresh个非空数值的才保留下来。
df.dropna(axis='index',thresh=3)


# #### 3) 填充缺失值

# In[202]:


data = pd.Series([1, np.nan, 2, None, 3], index = list('abcde'))
data


# In[203]:


data.fillna(0)


# In[204]:


data.fillna(method='ffill') #用缺失值前面的有效值来从前往后填充（forward-fill）


# In[205]:


data.fillna(method='bfill') # 用缺失值后面的有效值来从前往后填充（backward-fill）


# In[209]:


df = pd.DataFrame([[1, np.nan, 2],
                   [2, 3, 5],
                   [np.nan, 4, 6]])
df.fillna(method='ffill',axis=1)

