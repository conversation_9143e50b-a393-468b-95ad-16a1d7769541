#!/usr/bin/env python
# coding: utf-8

# # 14 Dashboard with Dash

# ## 词云图

# In[6]:


# 静态图
from wordcloud import WordCloud
import matplotlib.pyplot as plt

# 词频数据：字典（键：单词，值：数值）
text = {"Python": 100, "数据": 80, "可视化": 60, "机器学习": 90}

# 创建词云对象
wordcloud = WordCloud(font_path="simhei.ttf",  # 中文字体
                      width=800, 
                      height=400, 
                      background_color='white'
                     ).generate_from_frequencies(text)

# 展示词云图
plt.figure(figsize=(10, 5))
plt.imshow(wordcloud)
plt.axis("off")
plt.show()


# In[2]:


# pyecharts绘制
from pyecharts.charts import WordCloud
from pyecharts import options as opts

# 词云数据：元组列表
# 每个元组第一个元素是“单词”，第二个元素是个数
words = [("Python", 100), 
         ("数据", 80), 
         ("可视化", 60), 
         ("机器学习", 90), 
         ("深度学习", 70), 
         ("AI", 95)]

# 创建词云图
wc = (
    WordCloud()
    .add(series_name="词云", data_pair=words, word_size_range=[20, 100])
    .set_global_opts(title_opts=opts.TitleOpts(title="词云图"))
)

# 在jupyter notebook显示
wc.render_notebook()


# ## 数据大屏

# ### 简单的dash页面

# In[7]:


import dash
from dash import html

app = dash.Dash(__name__)  # 初始化 app

app.layout = html.Div([    # 设置网页结构
    html.H1("欢迎来到数据大屏"),
    html.P("这个页面由 Python 构建")
])

app.run_server(debug=True) # 启动网页


# ### 添加图表

# In[8]:


from dash import dcc
import plotly.express as px

df = px.data.iris()
fig = px.scatter(df, x="sepal_width", y="sepal_length", color="species")

app.layout = html.Div([
    html.H1("Iris 数据可视化"),
    dcc.Graph(figure=fig)
])


# In[10]:


# 上述代码会覆盖掉之前的信息，因此采用下面的合并方式


# In[9]:


app.layout = html.Div([
    html.H1("欢迎来到数据大屏"),
    html.P("这个页面由 Python 构建"),
    
    html.Hr(),  # 添加分隔线

    html.H1("Iris 数据可视化"),
    dcc.Graph(figure=fig)
])


# ### 实现交互

# In[12]:


# 可以在该界面通过 “关闭”按钮先关闭之前的网页应用，释放端口


# In[14]:


import dash
from dash import html, dcc, Input, Output
import plotly.express as px
import pandas as pd

# 创建数据，为后续绘制饼图做准备
# 假设我们这里设置三个城市每个产品（A B C D）的销量
data = {
    'City': ['Beijing'] * 4 + ['Shanghai'] * 4 + ['Shenzhen'] * 4,
    'Product': ['A', 'B', 'C', 'D'] * 3,
    'Sales': [100, 150, 80, 70, 120, 90, 60, 130, 110, 100, 85, 95]
}
df = pd.DataFrame(data)
df


# In[15]:


# 初始化app
app = dash.Dash(__name__)

# 设置页面布局，注意这里我们开始使用图表
app.layout = html.Div([
    html.H1("产品销售分布饼图", style={'textAlign': 'center'}),
    
    html.P("请选择城市："),
    dcc.Dropdown(
        id='city-dropdown',
        options=[{'label': city, 'value': city} for city in df['City'].unique()],
        value='Beijing'
    ),
    
    dcc.Graph(id='pie-chart')
])

# 回调函数 callback
@app.callback(
    Output('pie-chart', 'figure'),
    Input('city-dropdown', 'value')
)

def update_pie_chart(selected_city):
    filtered_df = df[df['City'] == selected_city]
    fig = px.pie(filtered_df, names='Product', values='Sales',
                 title=f"{selected_city} 的产品销售占比")
    return fig

# 启动应用
if __name__ == '__main__':
    app.run_server(debug=True)


# ### 拓展布局，实现：
# - html.Div(style=...) 实现
# - 使用 dcc.Dropdown 控制数据变化
# - @app.callback 多个输出（卡片 + 图表联动更新）

# In[17]:


import dash
from dash import html, dcc, Input, Output
import pandas as pd
import plotly.express as px

# 创建数据
data = {
    'City': ['Beijing'] * 4 + ['Shanghai'] * 4 + ['Shenzhen'] * 4,
    'Product': ['A', 'B', 'C', 'D'] * 3,
    'Sales': [100, 150, 80, 70, 120, 90, 60, 130, 110, 100, 85, 95]
}
df = pd.DataFrame(data)

# 初始化 Dash 应用
app = dash.Dash(__name__)

# 页面布局
app.layout = html.Div([
    # 顶部标题，设置格式
    html.H1("产品销售数据大屏", style={'textAlign': 'center', 'marginBottom': '30px'}),

    # 控件区域
    html.Div([
        html.Label("请选择城市："),
        dcc.Dropdown(
            id='city-dropdown',
            options=[{'label': c, 'value': c} for c in df['City'].unique()],
            value='Beijing',
            style={'width': '300px'}
        )
    ], style={'marginBottom': '30px'}),

    # 统计卡片
    html.Div(id='stats-cards', style={
        'display': 'flex',
        'justifyContent': 'space-around',
        'marginBottom': '40px'
    }),

    # 图表区域
    html.Div([
        html.Div([
            dcc.Graph(id='bar-chart')
        ], style={'width': '48%', 'display': 'inline-block'}),

        html.Div([
            dcc.Graph(id='pie-chart')
        ], style={'width': '48%', 'display': 'inline-block'})
    ])
], style={'padding': '20px'})

# 回调：更新卡片 & 图表
@app.callback(
    [Output('stats-cards', 'children'), # id是 'stats-cards' 组件的 children 属性（就是这个区域里面显示的内容，通常是子组件）
     Output('bar-chart', 'figure'),
     Output('pie-chart', 'figure')],
    [Input('city-dropdown', 'value')]
)

def update_dashboard(city):
    filtered = df[df['City'] == city]

    total_sales = filtered['Sales'].sum()
    product_count = filtered['Product'].nunique()
    avg_sales = round(filtered['Sales'].mean(), 2)

    # 卡片组件——注意这里存储的是CSS样式
    # padding：内边距，卡片内容离边框距离，20像素。
    # borderRadius：圆角，10像素圆滑边角。
    # backgroundColor：背景色，浅灰色 #f0f0f0。
    # width：卡片宽度，这里设置为父容器的 30%。
    # textAlign：文字居中。
    # boxShadow：阴影效果，使卡片有立体感。
    card_style = {
        'padding': '20px',
        'borderRadius': '10px',
        'backgroundColor': '#f0f0f0',
        'width': '30%',
        'textAlign': 'center',
        'boxShadow': '2px 2px 8px rgba(0,0,0,0.1)'
    }

    # 这是一个列表，包含了 3 个 html.Div，代表 3 个统计卡片。
    # 每个卡片结构一样：
    # - html.Div 作为卡片容器
    # - 里面有标题（html.H4）和具体数值（html.H2）
    # - 通过 style=card_style，这 3 个卡片都共享刚才定义的样式。
    # # 由于外层容器用的是 flex 布局且 justifyContent: space-around，3 个卡片会横排且间距均匀。
    cards = [
        html.Div([
            html.H4("总销量"),
            html.H2(f"{total_sales}")
        ], style=card_style),

        html.Div([
            html.H4("产品种类"),
            html.H2(f"{product_count}")
        ], style=card_style),

        html.Div([
            html.H4("平均销量"),
            html.H2(f"{avg_sales}")
        ], style=card_style)
    ]

    # 图表
    bar_fig = px.bar(filtered, x='Product', y='Sales', title=f"{city} 产品销量柱状图")
    pie_fig = px.pie(filtered, names='Product', values='Sales', title=f"{city} 产品销量占比饼图")

    return cards, bar_fig, pie_fig

# 启动应用
if __name__ == '__main__':
    app.run_server(debug=True)


# ## 实时刷新数据大屏

# ### 模拟数据

# In[1]:


import dash
from dash import html, dcc, Output, Input
import plotly.express as px
import random
from collections import deque
import pandas as pd

# 初始化数据（maxlen：最多保存20个点）
# 用于存储滑动窗口的数据（避免内存爆炸）
X = deque(maxlen=20)
Y = deque(maxlen=20)
X.append(0)
Y.append(random.randint(0, 100))

app = dash.Dash(__name__)

app.layout = html.Div([
    html.H2("模拟实时数据展示", style={'textAlign': 'center'}),
    
    dcc.Graph(id='live-update-graph'),

    # 用于定时更新（如1秒刷新一次）
    dcc.Interval(
        id='interval-component',
        interval=1000,  # 每秒更新一次
        n_intervals=0
    )
])

# 每次触发时，n_intervals自增，可驱动图表刷新
@app.callback(
    Output('live-update-graph', 'figure'),
    Input('interval-component', 'n_intervals')
)
def update_graph(n):
    X.append(X[-1] + 1)
    Y.append(Y[-1] + random.randint(-10, 10))

    data = pd.DataFrame({'x': X, 'y': Y})

    fig = px.scatter(
        data, 
        x='x', 
        y='y', 
        title='模拟数据散点图',
        labels={'x': '时间', 'y': '数值'},
    )

    # uirevision='static'：保持缩放、拖动状态不会因为刷新而重置
    fig.update_layout(title='实时数据折线图', 
                      xaxis_title='时间步', 
                      yaxis_title='值', uirevision='static')

    return fig

if __name__ == '__main__':
    app.run_server(debug=True)


# ### 通过API获取数据

# In[3]:


import dash
from dash import dcc, html, Input, Output
import plotly.graph_objs as go
import plotly.express as px
import requests
from datetime import datetime

app = dash.Dash(__name__)

# 初始化数据存储
price_history = {'time': [], 'price': []}

app.layout = html.Div([
    html.H1("比特币实时价格", style={'textAlign': 'center'}),
    dcc.Graph(id='price-chart'),
    dcc.Interval(
        id='refresh', 
        interval=10*1000)  # 10秒刷新
])

@app.callback(
    Output('price-chart', 'figure'),
    Input('refresh', 'n_intervals')
)
def update_chart(n):
    # 获取实时数据
    try:
        response = requests.get(
            "https://api.coingecko.com/api/v3/simple/price",
            params={'ids': 'bitcoin', 'vs_currencies': 'usd'}
        )
        price = response.json()['bitcoin']['usd']
    except:
        price = price_history['price'][-1] if price_history['price'] else 0

    # 更新数据记录（保留最近30个点）
    price_history['time'].append(datetime.now().strftime("%H:%M:%S"))
    price_history['price'].append(price)
    if len(price_history['time']) > 30:
        price_history['time'] = price_history['time'][-30:]
        price_history['price'] = price_history['price'][-30:]

    # 构建图表
    fig = px.line(
        price_history,
        x='time',
        y='price',
        color_discrete_sequence=['#FF4B4B'],  # 设置线条颜色
        labels={
            'time': '时间',
            'price': '价格 (USD)'
        }
    )

    # 更新布局保持原样式
    fig.update_layout(
        plot_bgcolor='#1E1E1E',
        paper_bgcolor='#1E1E1E',
        xaxis=dict(color='white'),
        yaxis=dict(color='white'),
        margin=dict(t=40),
        showlegend=False  # 隐藏自动生成的图例
    )
    
    # 设置线条宽度（需单独更新）
    fig.update_traces(line=dict(width=2))
    
    return fig

if __name__ == '__main__':
    # 默认端口：8050
    app.run_server(debug=True, port=8051)


# In[ ]:




