#!/usr/bin/env python
# coding: utf-8

# # Lecture 12: 数据分布可视化

# In[29]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Bar, Boxplot
import random
import plotly.express as px
import plotly.graph_objects as go

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号


# ## 数据分布可视化

# ### 直方图

# In[4]:


# 生成数据
# 从均值为 0（通过loc参数设置），标准差为 1 （通过scale参数设置）的标准正态分布中随机抽取1000个元素
data = np.random.normal(loc=0, scale=1, size=1000)
data


# In[5]:


# 使用matplotlib绘制

# data传递一维数据,bins设置直方个数
plt.hist(data, bins=30, color='skyblue', edgecolor='black')
plt.title("Histogram using Matplotlib")
plt.xlabel("Value")
plt.ylabel("Frequency")
plt.grid(True)
plt.show()


# In[6]:


# 使用seaborn绘制
sns.histplot(data, bins=30, color='orange')
plt.title("Histogram using Seaborn")
plt.xlabel("Value")
plt.ylabel("Frequency")
plt.grid(True)
plt.show()


# In[8]:


# 使用pyecharts绘制
# 无法直接做分箱统计


# In[7]:


# 使用plotly绘制

# 预处理数据
df = pd.DataFrame({'value': data})

fig = px.histogram(df, x="value", nbins=30, title="Histogram using Plotly")
fig.show()


# ### 核密度图

# In[15]:


# sns.kdeplot(data, fill=True,color='green')
data = np.random.normal(loc=0, scale=1, size=1000)

sns.kdeplot(x=data, fill=True, color='green')

plt.title("KDE using Seaborn")
plt.xlabel("Value")
plt.ylabel("Density")
plt.grid(True)
plt.show()


# In[12]:


import plotly.figure_factory as ff

fig = ff.create_distplot([data], group_labels=['Data'], show_hist=False)
fig.update_layout(title="KDE using Plotly")
fig.show()


# In[17]:


fig = ff.create_distplot([data], group_labels=['Data'], show_hist=True)
fig.update_layout(title="KDE using Plotly")
fig.show()


# ### 箱线图

# In[32]:


# 重新生成数据
data = np.random.normal(loc=0, scale=1, size=100)


# In[19]:


# 使用matplotlib绘制
plt.boxplot(data)
plt.title("Boxplot using Matplotlib")
plt.ylabel("Value")
plt.grid(True)
plt.show()


# In[20]:


# 使用seaborn绘制
sns.boxplot(y=data, color='skyblue')
plt.title("Boxplot using Seaborn")
plt.ylabel("Value")
plt.grid(True)
plt.show()


# In[38]:


# 使用pyecharts绘制
# 需要对传入数据进行预处理,要求嵌套列表
data_group = [data.tolist()]
print(data_group)


boxplot = Boxplot()

# 输出： min, Q1, median, Q3, max
box_data = boxplot.prepare_data(data_group)  

print(box_data)

boxplot.add_xaxis(["Group A"])
boxplot.add_yaxis("Boxplot", box_data)
boxplot.set_global_opts(title_opts=opts.TitleOpts(title="Boxplot using Pyecharts"))
boxplot.render_notebook()
boxplot.render("boxplot_pyecharts.html")


# In[40]:


# 使用plotly.express绘制
fig = px.box(y=data, title="Boxplot using Plotly")
fig.show()


# ### 小提琴图

# In[45]:


# 重新生成数据
data = np.random.normal(loc=0, scale=1, size=100)


# In[46]:


# 使用matplotlib绘制
plt.violinplot(data, showmeans=True, showmedians=True)
plt.title("Violin Plot using Matplotlib")
plt.ylabel("Value")
plt.grid(True)
plt.show()


# In[47]:


# 使用seaborn绘制
sns.violinplot(y=data, inner="box", color='skyblue')
plt.title("Violin Plot using Seaborn")
plt.ylabel("Value")
plt.grid(True)
plt.show()


# In[48]:


# 使用seaborn绘制
sns.violinplot(y=data, inner="quartile", color='skyblue')
plt.title("Violin Plot using Seaborn")
plt.ylabel("Value")
plt.grid(True)
plt.show()


# In[51]:


# 使用plotly.express绘制
fig = px.violin(
    y=data,
    box=True,          # 内嵌箱线图
    title="Plotly Express 小提琴图",
    labels={"y": "Value"}
)
fig.show()


# ### 累积分布曲线图

# In[56]:


# 生成随机数据
data = np.random.randn(1000)

# 绘制累积分布曲线
plt.hist(data, bins=30, cumulative=True, color='blue', edgecolor='black', alpha=0.7)
plt.title('CDF using Matplotlib')
plt.xlabel('Value')
plt.ylabel('Cumulative Frequency')
plt.grid(True)
plt.show()


# In[53]:


# 绘制累积分布曲线
sns.ecdfplot(data)
plt.title('CDF using Seaborn')
plt.xlabel('Value')
plt.ylabel('Cumulative Probability')
plt.show()


# In[54]:


# 绘制累积分布曲线
fig = px.ecdf(data, title='CDF using Plotly')
fig.update_layout(xaxis_title='Value', yaxis_title='Cumulative Probability')
fig.show()


# In[ ]:




