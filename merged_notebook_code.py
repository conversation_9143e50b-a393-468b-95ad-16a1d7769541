# --- Start of 10_Exercise.py ---
#!/usr/bin/env python
# coding: utf-8

# # 10 Exercise

# In[1]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Sunburst, Tree, Pie
import random
import plotly.express as px
import plotly.graph_objects as go

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号


# ## 随堂练习（1）
# 利用plotly绘制：
# data = [
#     {"name": "A", "children": [
#         {"name": "A1", "value": 10},
#         {"name": "A2", "value": 5}
#     ]},
#     {"name": "B", "children": [
#         {"name": "B1", "value": 20},
#         {"name": "B2", "value": 15}
#     ]}
# ]

# In[6]:


# 原始树形数据
data = [
    {"name": "A", "children": [
        {"name": "A1", "value": 10},
        {"name": "A2", "value": 5}
    ]},
    {"name": "B", "children": [
        {"name": "B1", "value": 20},
        {"name": "B2", "value": 15}
    ]}
]

plotly_data = []

for item in data:
    parent_name = item['name']
    for child in item['children']:
        plotly_data.append({
            'name': parent_name,
            'children': child['name'],
            'value': child['value']
        })

print(plotly_data)

# 创建 DataFrame
df = pd.DataFrame(plotly_data)
print(df)

# 使用Plotly绘制旭日图
fig = px.sunburst(df, path=['name', 'children'], values='value', title="Plotly Sunburst Chart")
fig.show()


# ## 随堂练习（2）
# 根据数据绘制基本饼图

# In[4]:


languages = ['Python', 'Java', 'C', 'PHP', 'R']
counts = [40,20,30,10,30]

# 使用plotly绘制
df = pd.DataFrame({'Languages': languages, 'Value': counts})

fig = px.pie(df, names='Languages', values='Value', title='Plotly Pie Chart')
fig.show()


# ## 随堂练习（3）
# 根据练习（2）绘制画布

# In[5]:


import matplotlib.pyplot as plt

# 数据
languages = ['Python', 'Java', 'C', 'PHP', 'R']
counts = [40, 20, 30, 10, 30]

# 创建一行两列的子图
fig, axs = plt.subplots(1, 2, figsize=(12, 6))

# 第一个饼图（基本饼图）
axs[0].pie(counts, labels=languages, autopct='%1.1f%%', startangle=90)
axs[0].set_title("Basic Pie Chart")

# 第二个饼图（甜甜圈饼图）
axs[1].pie(counts, labels=languages, autopct='%1.1f%%', startangle=90, wedgeprops={'width': 0.3})
axs[1].set_title("Donut Pie Chart")

plt.tight_layout()  # 自动调整布局
plt.show()


# In[23]:


from pyecharts.charts import Pie, Grid
from pyecharts import options as opts

# 数据
languages = ['Python', 'Java', 'C', 'PHP', 'R']
counts = [40, 20, 30, 10, 30]

# 基本饼图
pie1 = (
    Pie()
    .add("", [list(z) for z in zip(languages, counts)])
    .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {d}%"))
)

# 甜甜圈饼图
pie2 = (
    Pie()
    .add("", [list(z) for z in zip(languages, counts)], radius=["40%", "70%"])
    .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {d}%"))
    .set_global_opts(title_opts=opts.TitleOpts(title="Donut Pie Chart"))
)

# 使用Grid布局
grid = (
    Grid()
    .add(pie2, grid_opts=opts.GridOpts(pos_left='5%',pos_right="55%"))  # 左边的饼图
    .add(pie2, grid_opts=opts.GridOpts(pos_right="5%",pos_left='55%'))   # 右边的甜甜圈饼图
)

grid.render_notebook()  # Jupyter 里展示
# grid.render("grid.html")  # 生成html的时候只能显示第一个，如果保存成html的时候，可以采用下一个cell的代码


# In[21]:


from pyecharts.charts import Pie
from pyecharts import options as opts

# 数据
languages = ['Python', 'Java', 'C', 'PHP', 'R']
counts = [40, 20, 30, 10, 30]

# 同一个 Pie 里，画两个饼图
pie = Pie()

# 第一个饼图
pie.add(
    series_name="普通饼图",
    data_pair=[list(z) for z in zip(languages, counts)],
    center=["25%", "50%"],  # 位置：画布25%的宽度，50%的高度
)

# 第二个甜甜圈
pie.add(
    series_name="甜甜圈图",
    data_pair=[list(z) for z in zip(languages, counts)],
    radius=["40%", "70%"],   # 甜甜圈样式
    center=["75%", "50%"],   # 位置：画布75%的宽度，50%的高度
)

# 全局配置
pie.set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {d}%"))
pie.set_global_opts(title_opts=opts.TitleOpts(title="左右双饼图展示"))

pie.render("dual_pie.html")


# In[8]:


import plotly.graph_objects as go
from plotly.subplots import make_subplots

# 数据
languages = ['Python', 'Java', 'C', 'PHP', 'R']
counts = [40, 20, 30, 10, 30]

# 创建1行2列的子图，并指定子图类型为 'pie'
fig = make_subplots(rows=1, cols=2, 
                    subplot_titles=["Basic Pie Chart", "Donut Pie Chart"],
                    specs=[[{'type': 'pie'}, {'type': 'pie'}]])

# 第一个子图：基本饼图
fig.add_trace(go.Pie(labels=languages, values=counts, hole=0, name='Basic Pie'), row=1, col=1)

# 第二个子图：甜甜圈饼图
fig.add_trace(go.Pie(labels=languages, values=counts, hole=0.3, name='Donut Pie'), row=1, col=2)

fig.update_layout(height=400, width=800, title_text="Pie and Donut Pie Charts")
fig.show()


# ## 随堂练习（4）
# 读取文件后绘制嵌套饼图

# In[36]:


# 读取数据
df = pd.read_csv("pokemon.csv")

# 处理缺失值——用None
df['Type 2'] = df['Type 2'].fillna('None')

# 筛选最多的两个主类型 Type 1
top2_type1 = df["Type 1"].value_counts().nlargest(2).index.tolist()
df_filtered = df[df["Type 1"].isin(top2_type1)]

# 内环数据（Type 1）
inner_labels = df_filtered["Type 1"].value_counts().index.tolist()
inner_sizes = df_filtered["Type 1"].value_counts().values.tolist()
print(inner_labels, inner_sizes)

# 外环数据（Type 2 在每个 Type 1 下的分布）
outer = df_filtered.groupby(["Type 1", "Type 2"]).size().reset_index(name="count")

# 思考：如果屏蔽下面的代码，嵌套饼图有什么问题？
'''
# 将type1设置为一个有顺序的分类变量（Categorical Variable），顺序是inner_labels给定的顺序
outer["Type 1"] = pd.Categorical(outer["Type 1"], categories=inner_labels, ordered=True)
# 按照定义的顺序（不是字母顺序）来排
outer = outer.sort_values(["Type 1", "Type 2"])
# print(outer)
'''

# 拼接标签时，强制转换成str
outer_labels = list(outer["Type 1"].astype(str) + " - " + outer["Type 2"].astype(str))
outer_sizes = list(outer["count"])
print(outer_labels, outer_sizes)


# In[30]:


# 绘图
fig, ax = plt.subplots(figsize=(8, 8))
# 绘制外层饼图
ax.pie(outer_sizes, labels=outer_labels, radius=1)
# 绘制内层饼图，调整半径，使其成为嵌套图
ax.pie(inner_sizes, labels=inner_labels, radius=0.5)

ax.set(aspect="equal", title="嵌套饼图 - Matplotlib（Type1 和 Type2）")
plt.show()


# In[31]:


import plotly.express as px

sunburst_df = df_filtered.copy()
sunburst_df["Type 2"] = sunburst_df["Type 2"].fillna("None")

fig = px.sunburst(sunburst_df, path=["Type 1", "Type 2"], values=[1]*len(sunburst_df),
                  title="嵌套饼图（Plotly Sunburst）")
fig.show()


# In[32]:


# 内环
inner_data = [list(z) for z in zip(inner_labels, inner_sizes)]
# 外环
outer_data = [list(z) for z in zip(outer_labels, outer_sizes)]
# print(outer_data)

pie = (
    Pie()
    .add("", outer_data, radius=["50%", "70%"])  # 再添加外环
    .add("", inner_data, radius=["20%", "50%"])  # 先添加内环
    .set_global_opts(title_opts=opts.TitleOpts(title="嵌套饼图 - Pyecharts"))
    .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {d}%"))
)
pie.render("nested_pie_pyecharts.html")


# In[ ]:





# --- End of 10_Exercise.py ---

# --- Start of 10_Hierarchical_Local_Global_Data.py ---
#!/usr/bin/env python
# coding: utf-8

# # Lecture 10: 层次关系&局部整体关系数据可视化

# In[25]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Sunburst, Tree, Pie
import random
import plotly.express as px
import plotly.graph_objects as go


# ## 层次关系图

# ### 旭日图

# In[2]:


# 使用pyecharts绘制

# 构造数据: 嵌套的字典列表 data，用于描述旭日图的数据结构。
# 每个字典表示一个层级的数据:
# - name 是节点名称，children 则是该节点的子节点。
# - 每个子节点包含 name 和 value（表示该节点的数值）
data = [
    {"name": "A", "children": [
        {"name": "A1", "value": 10},
        {"name": "A2", "value": 5}
    ]},
    {"name": "B", "children": [
        {"name": "B1", "value": 20},
        {"name": "B2", "value": 15}
    ]}
]

sunburst = (
    Sunburst() # Sunburst()：创建一个旭日图对象。
    .add(series_name="Sunburst", data_pair=data)
    # - series_name="Sunburst"：设置图表的系列名称为 "Sunburst"。
    # - data_pair=data：将前面定义的 data 数据传入图表。
    .set_global_opts(title_opts=opts.TitleOpts(title="旭日图（Sunburst）")) # 设置全局选项
)

sunburst.render('sunburst.html')
sunburst.render_notebook()


# In[10]:


# 利用plotly中express绘制

# 使用Plotly Express提供的示例数据tips()
# total_bill：账单总金额
# tip：小费金额
# sex：顾客性别
# smoker：是否吸烟
# day：星期几
# time：就餐时间（Lunch 或 Dinner）
# size：用餐人数

df=px.data.tips()	

df.head()


# In[12]:


# 创建旭日图，路径为'day','time','sex'，数值列为'total_bill'
# 用 total_bill（账单总金额）来决定每个区域的大小
fig1=px.sunburst(df,path=['day','time','sex'],values='total_bill')
fig1.show()

# 创建旭日图，并设置路径、数值列，根据'day'进行颜色着色
fig2=px.sunburst(df,path=['sex','day','time'],
						  values='total_bill',color='day')
fig2.show()

# 创建旭日图，并设置路径、数值列，根据'time'进行颜色着色
fig3=px.sunburst(df,path=['sex','day','time'],
					 values='total_bill',color='time')
fig3.show()

# 创建旭日图，并设置路径、数值列，根据'time'进行颜色着色，
# 并使用离散颜色映射(color_discrete_map)为不同的时间段(time)设置不同的颜色
# '(?)'：将缺失的 time 数据着色为黑色。
# 'Lunch'：将 "Lunch" 的区域着色为金色。
# 'Dinner'：将 "Dinner" 的区域着色为深蓝色。
fig4=px.sunburst(df,path=['sex','day','time'],
					values='total_bill',color='time',
                    color_discrete_map={'(?)':'black','Lunch':'gold',
										  'Dinner':'darkblue'})
fig4.show()


# ### 树状图与谱系图

# In[3]:


# 使用pyecharts绘制树状图
data = [
    {"name": "A", "children": [
        {"name": "A1", "value": 10},
        {"name": "A2", "value": 5}
    ]},
    {"name": "B", "children": [
        {"name": "B1", "value": 20},
        {"name": "B2", "value": 15}
    ]}
]

tree = Tree()
tree.add("Tree", data)
tree.set_global_opts(title_opts=opts.TitleOpts(title="树状图"))
tree.render('tree_chart.html')


# In[17]:


# 使用plotly绘制

# 使用列表推导式进行数据转换
data_flat = [
    {"parent": item["name"], "child": child["name"], "value": child["value"]}
    for item in data
    for child in item["children"]
]

print(data_flat)

# 创建 DataFrame
df = pd.DataFrame(data_flat)

print(df)


# 使用 plotly.express 创建 treemap 图
fig = px.treemap(df, 
                 path=['parent', 'child'],  # 设置路径的层次
                 values='value',            # 设置每个矩形的大小
                 title="树形图 (Treemap)")

# 显示图表
fig.show()


# In[26]:


# 从plotly中导入gapminder数据集，并选择2007年的数据
# 绘制树状图（矩形树状图）

# gapminder 数据集包含多个国家在不同年份的数据
# 涉及的变量包括 country、continent、year、lifeExp（预期寿命）、pop（人口）、gdpPercap（人均 GDP）等。
df=px.data.gapminder().query("year==2007")

# 使用treemap图表绘制
fig=px.treemap(df,
                 path=[px.Constant("world"),'continent','country'],
                 values='pop',color='lifeExp',
                 hover_data=['iso_alpha'],
                 color_continuous_scale='RdBu',
                 color_continuous_midpoint=np.average(df['lifeExp'],
						         weights=df['pop']))

fig.update_layout(margin=dict(t=50,l=25,r=25,b=25))	# 更新图表布局
fig.show()


# In[18]:


# 使用plotly绘制谱系图
import plotly.figure_factory as ff	# 导入P figure_factory模块，用于创建图表

# 生成随机数据，创建一个18x10的随机数组，表示18个样本，每个样本有10个维度
X=np.random.rand(18,10)
# print(X)
# 创建谱系图，使用随机数据X，设置颜色阈值为1.5
# create_dendrogram函数来创建谱系图。
# 该函数接受一个二维数组 X（即样本和特征矩阵），并基于该数据计算层次聚类结果。
# color_threshold=1.5 设置了一个颜色阈值，用于根据聚类的相似度为不同的簇分配不同的颜色。
# 如果聚类的相似度高于阈值，则会为它们分配相同的颜色。
fig=ff.create_dendrogram(X,color_threshold=1.5)
# 更新图表布局，设置宽度为800像素，高度为500像素
fig.update_layout(width=800,height=500)
fig.show()


# In[29]:


# matplotlib 本身不提供直接绘制谱系图的功能，但它可以结合 scipy（科学计算库）中的层次聚类（hierarchical clustering）模块来绘制谱系图。
# seaborn 是基于 matplotlib 的库，虽然 seaborn 本身没有提供直接绘制谱系图的函数，
# 但它提供了一个基于 scipy 层次聚类的 clustermap 函数，能够通过热图与层次聚类图组合的方式间接展示谱系图。
# pyecharts它本身不支持绘制传统意义上的谱系图（即层次聚类谱系图）


# ### 桑葚图

# In[27]:


from matplotlib.sankey import Sankey

# 创建Sankey对象
sankey = Sankey()

# 添加流动数据，格式为 (流入流出，流量)
# flows：指定了各个流的数值。正数表示流入，负数表示流出。
# labels：为每个流添加标签，用于标识每个流动的来源或去向。
# 指定每个箭头的方向。orientations 的数量应与 flows 和 labels 的数量一致。
# https://matplotlib.org/stable/api/sankey_api.html
sankey.add(flows=[10, -10, 5, -5], 
           labels=['Input1', 'Output1', 'Input2', 'Output2'], 
           orientations=[1, -1, 1, -1])

# 绘制图形
sankey.finish()

# 显示图表
plt.title('Sankey Diagram')
plt.show()


# In[15]:


from pyecharts.charts import Sankey

# 构造节点数据
nodes = [
    {"name": "A"},
    {"name": "B"},
    {"name": "C"},
    {"name": "D"}
]

# 构造流动数据
links = [
    {"source": "A", "target": "B", "value": 10},
    {"source": "A", "target": "C", "value": 20},
    {"source": "B", "target": "D", "value": 5},
    {"source": "C", "target": "D", "value": 15}
]

# 创建Sankey图
sankey = (
    Sankey()
    .add("Sankey Diagram", nodes=nodes, links=links)  # 传入 `nodes` 和 `links`
    .set_global_opts(title_opts=opts.TitleOpts(title="Sankey Diagram"))
)

# 渲染并展示图表
sankey.render("sankey_chart.html")


# In[17]:


import plotly.graph_objects as go

# 创建桑基图
fig=go.Figure(go.Sankey(
    arrangement="snap",							# 设置节点位置的排列方式
    node={"label":["A","B","C","D","E","F"],	# 节点标签
            "x":[0.2,0.1,0.5,0.7,0.3,0.5],		# 节点的x坐标
            "y":[0.7,0.5,0.2,0.4,0.2,0.3],		# 节点的y坐标
            'pad':10},							# 节点的间距
    link={"source":[0,0,1,2,5,4,3,5],			# 每条链接的源节点索引
           "target":[5,3,4,3,0,2,2,3],			# 每条链接的目标节点索引
           "value":[1,2,1,1,1,1,1,2]} 			# 每条链接的值，表示流动的数量
))
fig.show()


# ## 局部整体型数据

# ### 饼图

# In[42]:


# 随机生成分类和对应值
categories = ['A', 'B', 'C', 'D']
values = [random.randint(10, 100) for _ in categories]
print(categories)
print(values)


# In[43]:


# 使用matplotlib绘制
plt.figure(figsize=(6, 6))
plt.pie(values, # 饼图中的数据值，它代表每个类别的数量或占比
        labels=categories, # 设置每个扇区的标签，categories 是一个包含分类名称的列表
        autopct='%1.1f%%', # 自动显示每个扇区所占比例，格式为浮动小数点，保留 1 位小数
        startangle=140, #设置饼图的起始角度。默认情况下，饼图的第一个扇区从 0 度开始
       )
plt.title("Matplotlib Pie Chart")
plt.axis('equal')
plt.show()


# In[47]:


# 使用pyecharts绘制饼图
# 直接创建数据： [['A', 35], ['B', 99], ['C', 11], ['D', 48]]
data_pair = [['A', 35], ['B', 99], ['C', 11], ['D', 48]]

# 或者使用函数完成这项操作
# data_pair = [list(z) for z in zip(categories, values)]
print(data_pair)

pie = (
    Pie()
    .add("", data_pair)
    .set_global_opts(title_opts=opts.TitleOpts(title="Pyecharts Pie Chart"))
    .set_series_opts(label_opts=opts.LabelOpts(formatter="{b}: {d}%"))
)

pie.render("random_pie_pyecharts.html")


# In[48]:


# 使用plotly绘制
df = pd.DataFrame({'Category': categories, 'Value': values})

fig = px.pie(df, names='Category', values='Value', title='Plotly Pie Chart')
fig.show()


# In[51]:


# 使用pandas内嵌plot进行绘图
# 准备数据

# 绘制饼图，将 Category 列设置为索引列，使得每个饼图的扇区都有对应的标签
df.set_index('Category')['Value'].plot(kind='pie', autopct='%1.1f%%', figsize=(6, 6), startangle=140)

# 添加标题
plt.title("Category Distribution")

# 显示图形
plt.show()


# In[53]:


# 创建一个大小为(6,4)的图，并设置子图属性为“等比例”
fig,ax=plt.subplots(figsize=(6,4),subplot_kw=dict(aspect="equal"))

# 配方和数据
recipe=["225 g flour","90 g sugar","1 egg",
          "60 g butter","100 ml milk","1/2 package of yeast"]
data=[225,90,50,60,100,5]
# 绘制饼图
wedges,texts=ax.pie(data,wedgeprops=dict(width=0.5),startangle=-40)

# 注释框的属性
bbox_props=dict(boxstyle="square,pad=0.3",fc="w",ec="k",lw=0.72)
kw=dict(arrowprops=dict(arrowstyle="-"),
          bbox=bbox_props,zorder=0,va="center")

# 遍历每个扇形并添加注释
for i,p in enumerate(wedges):
    # 计算注释的位置
    ang=(p.theta2-p.theta1)/2.+p.theta1
    y=np.sin(np.deg2rad(ang))
    x=np.cos(np.deg2rad(ang))
    # 水平对齐方式根据 x 坐标的正负确定
    horizontalalignment={-1:"right",1:"left"}[int(np.sign(x))]
    # 设置连接线的样式
    connectionstyle=f"angle,angleA=0,angleB={ang}"
    kw["arrowprops"].update({"connectionstyle":connectionstyle})
    # 添加注释
    ax.annotate(recipe[i],xy=(x,y),xytext=(1.35*np.sign(x),1.4*y),
                horizontalalignment=horizontalalignment,**kw)
ax.set_title("Matplotlib bakery:A donut")	# 设置标题
plt.show()


# ### 环形饼图

# In[3]:


# 使用matplotlib绘制
# 数据
labels = ['A', 'B', 'C', 'D']
sizes = [15, 30, 45, 10]

# 绘制环形饼图
# 修改饼图的 wedgeprops 来实现环形饼状图。
# 通过设置 wedgeprops 中的 width 参数，可以控制饼图的厚度。
plt.figure(figsize=(6, 6))
plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=140, wedgeprops={'width': 0.3})

# 添加标题
plt.title("Doughnut Chart")

# 显示图表
plt.axis('equal')  # 保证饼图是圆形
plt.show()


# In[4]:


# 使用pyecharts绘制
# 数据
data = [
    ("A", 30),
    ("B", 40),
    ("C", 50),
    ("D", 80)
]

# 创建环形饼图
# 通过设置 radius 属性来实现。radius 由两个值构成，第一个值是内半径，第二个值是外半径。
pie = (
    Pie()
    .add("", data, radius=["40%", "70%"])
    .set_global_opts(title_opts=opts.TitleOpts(title="Doughnut Chart"))
)

# 渲染并展示图表
pie.render("doughnut_chart.html")


# In[6]:


# 使用plotly绘制
# 数据
labels = ['A', 'B', 'C', 'D']
values = [15, 30, 45, 10]

# 绘制环形饼图
# 通过设置 hole 参数来创建一个环形图
fig = px.pie(names=labels, values=values, hole=0.5)

# 设置标题
fig.update_layout(title="Doughnut Chart")

# 显示图表
fig.show()


# ### 嵌套饼图

# In[20]:


# 使用matplotlib绘制
# 通过绘制多个饼图，并调整它们的半径来实现。
# 通常使用 axes 参数来控制多个饼图的显示。
# 示例数据
inner_labels = ['Fruit', 'Vegetable']
outer_labels = ['Apple', 'Banana', 'Carrot', 'Broccoli']

# 内圈：大分类
inner_sizes = [60, 40]

# 外圈：小分类
outer_sizes = [30, 30, 20, 20]  # 注意加起来要和对应内圈比例匹配

# 创建画布
fig, ax = plt.subplots(figsize=(6,6))

# 画内圈
ax.pie(inner_sizes, radius=1, labels=inner_labels, labeldistance=0.7, wedgeprops=dict(width=0.3))

# 画外圈
ax.pie(outer_sizes, radius=1.3, labels=outer_labels, labeldistance=0.85, wedgeprops=dict(width=0.3))

plt.title('Nested Pie Chart')
plt.show()


# In[21]:


# 使用pyecharts进行绘制

# 外层数据
data_outer = [
    ("A", 60),
    ("B", 30),
    ("C", 10)
]

# 内层数据
data_inner = [
    ("A1", 20),
    ("A2", 20),
    ("A3", 20),
    ("B1", 20),
    ("B2", 10),
    ("C", 10),
]

# 创建图表
pie = (
    Pie()
    .add("", data_outer, radius=["50%", "70%"])  # 外层饼图
    .add("", data_inner, radius=["20%", "50%"])  # 内层饼图
    .set_global_opts(title_opts=opts.TitleOpts(title="Nested Pie Chart"))
)

# 渲染并展示图表
pie.render("nested_pie_chart.html")


# In[24]:


# 使用plotly绘制
# 外层数据
labels_outer = ['A', 'B', 'C']
values_outer = [60, 30, 10]

# 内层数据
labels_inner = ['A1', 'A2', 'A3', 'B1', 'B2', 'C1']
values_inner = [20, 20, 20, 20, 10, 10]

# 创建外层饼图
fig = go.Figure(go.Pie(labels=labels_outer, values=values_outer, hole=0.5, title="Nested Pie Chart"))

# 创建内层饼图
# domain表示控制内层饼图的位置：内层饼图占据整个画布宽度（高度）的30%到70%，使其位于外层饼图的中央
# 去掉domain看下效果
fig.add_trace(go.Pie(labels=labels_inner, values=values_inner, hole=0.6, domain=dict(x=[0.3, 0.7], y=[0.3, 0.7])))

# 更新布局
fig.update_layout(
    title_text="Nested Pie Chart",
    showlegend=True
)

# 显示图表
fig.show()


# In[ ]:





# --- End of 10_Hierarchical_Local_Global_Data.py ---

# --- Start of 12_Data_Distribution.py ---
#!/usr/bin/env python
# coding: utf-8

# # Lecture 12: 数据分布可视化

# In[29]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Bar, Boxplot
import random
import plotly.express as px
import plotly.graph_objects as go

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号


# ## 数据分布可视化

# ### 直方图

# In[4]:


# 生成数据
# 从均值为 0（通过loc参数设置），标准差为 1 （通过scale参数设置）的标准正态分布中随机抽取1000个元素
data = np.random.normal(loc=0, scale=1, size=1000)
data


# In[5]:


# 使用matplotlib绘制

# data传递一维数据,bins设置直方个数
plt.hist(data, bins=30, color='skyblue', edgecolor='black')
plt.title("Histogram using Matplotlib")
plt.xlabel("Value")
plt.ylabel("Frequency")
plt.grid(True)
plt.show()


# In[6]:


# 使用seaborn绘制
sns.histplot(data, bins=30, color='orange')
plt.title("Histogram using Seaborn")
plt.xlabel("Value")
plt.ylabel("Frequency")
plt.grid(True)
plt.show()


# In[8]:


# 使用pyecharts绘制
# 无法直接做分箱统计


# In[7]:


# 使用plotly绘制

# 预处理数据
df = pd.DataFrame({'value': data})

fig = px.histogram(df, x="value", nbins=30, title="Histogram using Plotly")
fig.show()


# ### 核密度图

# In[15]:


# sns.kdeplot(data, fill=True,color='green')
data = np.random.normal(loc=0, scale=1, size=1000)

sns.kdeplot(x=data, fill=True, color='green')

plt.title("KDE using Seaborn")
plt.xlabel("Value")
plt.ylabel("Density")
plt.grid(True)
plt.show()


# In[12]:


import plotly.figure_factory as ff

fig = ff.create_distplot([data], group_labels=['Data'], show_hist=False)
fig.update_layout(title="KDE using Plotly")
fig.show()


# In[17]:


fig = ff.create_distplot([data], group_labels=['Data'], show_hist=True)
fig.update_layout(title="KDE using Plotly")
fig.show()


# ### 箱线图

# In[32]:


# 重新生成数据
data = np.random.normal(loc=0, scale=1, size=100)


# In[19]:


# 使用matplotlib绘制
plt.boxplot(data)
plt.title("Boxplot using Matplotlib")
plt.ylabel("Value")
plt.grid(True)
plt.show()


# In[20]:


# 使用seaborn绘制
sns.boxplot(y=data, color='skyblue')
plt.title("Boxplot using Seaborn")
plt.ylabel("Value")
plt.grid(True)
plt.show()


# In[38]:


# 使用pyecharts绘制
# 需要对传入数据进行预处理,要求嵌套列表
data_group = [data.tolist()]
print(data_group)


boxplot = Boxplot()

# 输出： min, Q1, median, Q3, max
box_data = boxplot.prepare_data(data_group)  

print(box_data)

boxplot.add_xaxis(["Group A"])
boxplot.add_yaxis("Boxplot", box_data)
boxplot.set_global_opts(title_opts=opts.TitleOpts(title="Boxplot using Pyecharts"))
boxplot.render_notebook()
boxplot.render("boxplot_pyecharts.html")


# In[40]:


# 使用plotly.express绘制
fig = px.box(y=data, title="Boxplot using Plotly")
fig.show()


# ### 小提琴图

# In[45]:


# 重新生成数据
data = np.random.normal(loc=0, scale=1, size=100)


# In[46]:


# 使用matplotlib绘制
plt.violinplot(data, showmeans=True, showmedians=True)
plt.title("Violin Plot using Matplotlib")
plt.ylabel("Value")
plt.grid(True)
plt.show()


# In[47]:


# 使用seaborn绘制
sns.violinplot(y=data, inner="box", color='skyblue')
plt.title("Violin Plot using Seaborn")
plt.ylabel("Value")
plt.grid(True)
plt.show()


# In[48]:


# 使用seaborn绘制
sns.violinplot(y=data, inner="quartile", color='skyblue')
plt.title("Violin Plot using Seaborn")
plt.ylabel("Value")
plt.grid(True)
plt.show()


# In[51]:


# 使用plotly.express绘制
fig = px.violin(
    y=data,
    box=True,          # 内嵌箱线图
    title="Plotly Express 小提琴图",
    labels={"y": "Value"}
)
fig.show()


# ### 累积分布曲线图

# In[56]:


# 生成随机数据
data = np.random.randn(1000)

# 绘制累积分布曲线
plt.hist(data, bins=30, cumulative=True, color='blue', edgecolor='black', alpha=0.7)
plt.title('CDF using Matplotlib')
plt.xlabel('Value')
plt.ylabel('Cumulative Frequency')
plt.grid(True)
plt.show()


# In[53]:


# 绘制累积分布曲线
sns.ecdfplot(data)
plt.title('CDF using Seaborn')
plt.xlabel('Value')
plt.ylabel('Cumulative Probability')
plt.show()


# In[54]:


# 绘制累积分布曲线
fig = px.ecdf(data, title='CDF using Plotly')
fig.update_layout(xaxis_title='Value', yaxis_title='Cumulative Probability')
fig.show()


# In[ ]:





# --- End of 12_Data_Distribution.py ---

# --- Start of 12_Exercise(1).py ---
#!/usr/bin/env python
# coding: utf-8

# # 12 随堂练习参考答案

# In[11]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Bar
import random
import plotly.express as px
import plotly.graph_objects as go

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号


# # 随堂练习（1）

# In[3]:


import pandas as pd
import matplotlib.pyplot as plt

# 读取Excel文件
file_path = "exercise_1.xlsx"

# 读取Sheet1绘制直方图（连续数据分布）
df_sheet1 = pd.read_excel(file_path, sheet_name="Sheet1")
plt.figure(figsize=(8, 4))
plt.hist(df_sheet1["scores"], bins=5, edgecolor="black", alpha=0.7)
plt.title("Sheet1: 成绩分布直方图")
plt.xlabel("分数")
plt.ylabel("频数")
plt.grid(True)
plt.show()
df_sheet1


# In[4]:


# 读取Sheet2绘制条形图（离散分类统计）
df_sheet2 = pd.read_excel(file_path, sheet_name="Sheet2")
plt.figure(figsize=(8, 4))
# 传递x（score），传递y（频数）
plt.bar(df_sheet2["score"], df_sheet2["count"], color="skyblue", edgecolor="black")
plt.title("Sheet2: 成绩分段统计条形图")
plt.xlabel("分数段")
plt.ylabel("人数")
plt.grid(axis="y")
plt.show()



# In[5]:


# 读取Sheet3并观察数据
df_sheet3 = pd.read_excel(file_path, sheet_name="Sheet3")
df_sheet3


# In[8]:


# 假如用直方图，直接传递数据进去
plt.figure(figsize=(8, 4))
plt.hist(df_sheet3["grade"], bins=5, edgecolor="black", alpha=0.7)
plt.title("Sheet3: 成绩等级直方图")
plt.xlabel("等级")
plt.ylabel("频数")
plt.grid(True)
plt.show()


# In[9]:


# 条形图——需要先处理数据

grade_counts = df_sheet3["grade"].value_counts().sort_index()
grade_counts


# In[10]:


plt.figure(figsize=(8, 4))
plt.bar(grade_counts.index, grade_counts.values, color="lightgreen", edgecolor="black")
plt.title("Sheet3: 成绩等级条形图")
plt.xlabel("等级")
plt.ylabel("人数")
plt.grid(axis="y")
plt.show()


# # 随堂练习（2）

# In[2]:


# 生成数据
data = np.random.normal(loc=0, scale=1, size=1000)

# 不同带宽的核密度图
sns.kdeplot(x=data, fill=True, color='green', bw_adjust=0.5)
sns.kdeplot(x=data, fill=True, color='black', bw_adjust=1)
sns.kdeplot(x=data, fill=True, color='red', bw_adjust=4)

plt.title("不同带宽的核密度图")
plt.xlabel("值")
plt.ylabel("密度")
plt.show()


# In[3]:


# 生成数据
data = np.random.normal(loc=0, scale=1, size=1000)

# 绘制直方图 + KDE 曲线
sns.histplot(data, stat='density', bins=30, color='lightgray', edgecolor='black')
sns.kdeplot(x=data, fill=True, color='green', bw_adjust=1)

plt.title("直方图 + 核密度图 (Seaborn)")
plt.xlabel("值")
plt.ylabel("密度")
plt.show()


# # 随堂练习（3）

# In[12]:


# 读取数据
import pandas as pd

df = pd.read_csv("starbucks.csv")
df = df[['Beverage_category', 'Calories']].dropna()
df


# In[13]:


# 使用matplotlib绘制
# plt.boxplot(data)
# plt.title("Boxplot using Matplotlib")
# plt.ylabel("Value")
# plt.grid(True)
# plt.show()

plt.figure(figsize=(10, 6))
categories = df['Beverage_category'].unique()
data = [df[df['Beverage_category'] == cat]['Calories'] for cat in categories]

plt.boxplot(data, labels=categories)

plt.xticks(rotation=45)
plt.title('Calories by Beverage Category (Matplotlib)')
plt.ylabel('Calories')
plt.tight_layout()
plt.show()


# In[14]:


# 使用seaborn绘制
# sns.boxplot(y=data, color='skyblue')
# plt.title("Boxplot using Seaborn")
# plt.ylabel("Value")
# plt.grid(True)
# plt.show()

plt.figure(figsize=(10, 6))
sns.boxplot(x='Beverage_category', y='Calories', data=df)
plt.xticks(rotation=45)
plt.title('Calories by Beverage Category (Seaborn)')
plt.tight_layout()
plt.show()


# In[9]:


# 使用pyecharts绘制
# 需要对传入数据进行预处理,要求嵌套列表
# data_group = [data.tolist()]
# print(data_group)


# boxplot = Boxplot()

# # 输出： min, Q1, median, Q3, max
# box_data = boxplot.prepare_data(data_group)  

# print(box_data)

# boxplot.add_xaxis(["Group A"])
# boxplot.add_yaxis("Boxplot", box_data)
# boxplot.set_global_opts(title_opts=opts.TitleOpts(title="Boxplot using Pyecharts"))
# boxplot.render_notebook()
# boxplot.render("boxplot_pyecharts.html")

from pyecharts.charts import Boxplot
# 准备数据
categories = df['Beverage_category'].unique().tolist()
box_data = [df[df['Beverage_category'] == cat]['Calories'].tolist() for cat in categories]

boxplot = Boxplot()
boxplot.add_xaxis(categories)
boxplot.add_yaxis("Calories", boxplot.prepare_data(box_data))
boxplot.set_global_opts(title_opts=opts.TitleOpts(title="Calories by Beverage Category (Pyecharts)"),
                        xaxis_opts=opts.AxisOpts(axislabel_opts={"rotate": 45}),
                        toolbox_opts=opts.ToolboxOpts())
boxplot.render("boxplot_pyecharts_exercise.html") 


# In[26]:


# 使用plotly.express绘制
# fig = px.box(y=data, title="Boxplot using Plotly")
# fig.show()

fig = px.box(df, x='Beverage_category', y='Calories',
             title='Calories by Beverage Category (Plotly)')
fig.update_layout(xaxis_tickangle=-45)
fig.show()


# # 随堂练习（4）

# In[31]:


import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 生成随机数据
data = np.random.randn(1000)

# 创建绘图区域
plt.figure(figsize=(8, 6))

# 绘制累积分布曲线 (CDF)
plt.hist(data, bins=30, cumulative=True, color='blue', edgecolor='black', alpha=0.5, label='CDF', density=True)

# 绘制核密度估计曲线 (KDE)
sns.kdeplot(data, color='red', label='KDE', linewidth=2, fill=True)

# 添加标题和标签
plt.title('CDF and KDE in One Plot')
plt.xlabel('Value')
plt.ylabel('Density / Cumulative Probability')

# 显示图例
plt.legend()

# 显示图表
plt.show()


# In[ ]:





# --- End of 12_Exercise(1).py ---

# --- Start of 13_Exercise.py ---
#!/usr/bin/env python
# coding: utf-8

# # 13 随堂练习参考答案

# In[62]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Line, Grid
import random
import plotly.express as px
import plotly.graph_objects as go

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号


# # 随堂练习（1）
# - 预处理：按年份聚合，计算平均评分和平均票房两个指标
# - 绘图：用4个包分别绘制
#     - 两个数据系列共享Y轴（示例中均值分数和均值票房）
#     - 两个数据系列不同Y轴（分数和票房尺度差距大，适合双Y轴）

# In[63]:


# 数据预处理过程
df = pd.read_csv("movies.csv")

# 去掉year score gross有缺失值的观测值
df_clean = df[['year', 'score', 'gross']].dropna()

# 以year为祖，对score和gross做平均值操作，并且分别命名为avg_score和avg_gross
df_yearly = df_clean.groupby('year').agg(
    avg_score=('score', 'mean'),
    avg_gross=('gross', 'mean')
).reset_index()

# 对year重命名为Year
df_yearly.rename(columns={'year': 'Year'}, inplace=True)

df_yearly.head()


# In[66]:


# 使用matplotlib绘制
# 多个数据系列（但共享Y轴）
plt.figure(figsize=(10,5))
plt.plot(df_yearly['Year'], df_yearly['avg_score'], label='Avg Score')

# plt.plot(df_yearly['Year'], df_yearly['avg_gross'], label='Avg Gross (x10M)')
# 观察除1e7的意义在于？（思考）
plt.plot(df_yearly['Year'], df_yearly['avg_gross'] / 1e7, label='Avg Gross (x10M)')

plt.xlabel('Year')
plt.ylabel('Value')
plt.title('Movies Avg Score and Gross Over Years (Shared Y axis)')
plt.legend()
plt.show()


# In[ ]:


# 观察除1e7的意义在于？（思考）
# plt.plot(df_yearly['Year'], df_yearly['avg_gross'] / 1e7, label='Avg Gross (x10M)')


# In[51]:


# 多个数据系列（不共享Y轴）
# 创建一个绘图窗口和主坐标轴 ax1，设置图形大小为10x5
fig, ax1 = plt.subplots(figsize=(10,5))

# 在第一个坐标轴（左侧Y轴）上绘制蓝色折线，X轴是年份，Y轴是电影的平均评分。
ax1.plot(df_yearly['Year'], df_yearly['avg_score'], 'b-', label='Avg Score')
ax1.set_xlabel('Year')
ax1.set_ylabel('Avg Score', color='b')
ax1.tick_params(axis='y', labelcolor='b')

# 实例化一个共享x轴的第2个坐标轴
# 新建第二个坐标轴 ax2，它共享ax1的X轴，但拥有独立的Y轴（位于右侧）
ax2 = ax1.twinx()
ax2.plot(df_yearly['Year'], df_yearly['avg_gross'], 'r-', label='Avg Gross')
ax2.set_ylabel('Avg Gross', color='r')
ax2.tick_params(axis='y', labelcolor='r')

plt.title('Movies Score and Gross Over Years (Dual Y axes)')
plt.show()


# In[5]:


# 使用seaborn绘图（共享Y轴）
# 需要将数据转为长格式（参考第7讲melt函数）
df_melt = df_yearly.melt(id_vars='Year', value_vars=['avg_score', 'avg_gross'], var_name='Metric', value_name='Value')
# print(df_melt)
plt.figure(figsize=(10,5))
sns.lineplot(data=df_melt, x='Year', y='Value', hue='Metric')
plt.title('Movies Avg Score and Gross Over Years (Seaborn)')
plt.show()


# In[52]:


# 使用pyecharts绘制
# 多个数据系列（但共享Y轴）
line = (
    Line()
    .add_xaxis(df_yearly['Year'].astype(str).tolist())
    .add_yaxis("Avg Score", df_yearly['avg_score'].round(2).tolist())
    .add_yaxis("Avg Gross (x10M)", (df_yearly['avg_gross'] / 1e7).round(2).tolist())
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Movies Avg Score & Gross (Shared Y) - Pyecharts"),
        yaxis_opts=opts.AxisOpts(name="Value")
    )
)
line.render_notebook()


# In[53]:


# 多个数据系列（不共享Y轴）
line2 = (
    Line()
    .add_xaxis(df_yearly['Year'].astype(str).tolist())
    .add_yaxis("Avg Score", df_yearly['avg_score'].round(2).tolist(), yaxis_index=0)
    .extend_axis(yaxis=opts.AxisOpts(name="Avg Gross", position="right"))
    .add_yaxis("Avg Gross", df_yearly['avg_gross'].round(0).tolist(), yaxis_index=1)
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Movies Avg Score & Gross (Dual Y) - Pyecharts"),
        yaxis_opts=opts.AxisOpts(name="Avg Score"),
        tooltip_opts=opts.TooltipOpts(trigger="axis"),
    )
)
line2.render_notebook()


# In[3]:


# 共享Y轴——方式1
df_long = df_yearly.melt(id_vars='Year', value_vars=['avg_score', 'avg_gross'],
                        var_name='Metric', value_name='Value')
print(df_long)
fig = px.line(df_long, x='Year', y='Value', color='Metric',
              labels={'Year':'Year', 'Value':'Value', 'Metric':'Metric'},
              title='Plotly Express - Shared Y-axis Line Plot')
fig.show()


# In[4]:


# 共享Y轴——方式2
# 用 px.line 画第一个系列
fig = px.line(df_yearly, x='Year', y='avg_score', labels={'avg_score': 'avg_score'}, title='共享Y轴的双线图')

# 用 add_scatter 添加第二个系列
fig.add_scatter(x=df_yearly['Year'], y=df_yearly['avg_gross'], mode='lines', name='avg_gross')

fig.show()


# In[67]:


# 不共享Y轴（双Y轴）
fig = go.Figure()

fig.add_trace(go.Scatter(
    x=df_yearly['Year'], y=df_yearly['avg_score'],
    name='Avg Score', mode='lines', yaxis='y1'
))

fig.add_trace(go.Scatter(
    x=df_yearly['Year'], y=df_yearly['avg_gross'],
    name='Avg Gross', mode='lines', yaxis='y2'
))

fig.update_layout(
    title='Plotly - Dual Y-axis Line Plot',
    xaxis=dict(title='Year'),
    yaxis=dict(title='Avg Score', side='left'),
    yaxis2=dict(title='Avg Gross', overlaying='y', side='right')
)
fig.show()


# In[ ]:





# # 随堂练习（2）
# - 预处理：读取数据（ratings.csv）
# - 绘图：用不同的包分别绘制1*2的格式（参考第10讲随堂练习）

# In[6]:


# 数据预处理过程
df = pd.read_csv("ratings.csv")

df.head()


# In[7]:


# 创建一行两列的子图
fig, axes = plt.subplots(1, 2, figsize=(12, 6))

# 第一个子图：折线图
axes[0].plot(df['Date'].tolist(), df['Python'].tolist(), marker='o')
axes[0].set_title("Line Chart - Matplotlib")
axes[0].set_xlabel("X Axis")
axes[0].set_ylabel("Y Axis")
axes[0].grid(True)
axes[0].tick_params(axis='x', rotation=45)


# 第二个子图：面积图
plt.fill_between(df['Date'], df['C'], color='skyblue', alpha=0.5)
axes[1].plot(df['Date'].tolist(), df['C'].tolist(), marker='o')
axes[1].set_title("Line Chart (Time Series) - Matplotlib")
axes[1].set_xlabel("Date")
axes[1].set_ylabel("Value")
axes[1].grid(True)

axes[1].tick_params(axis='x', rotation=45)

plt.tight_layout()  # 自动调整布局
plt.show()


# 观察下面两个图，x轴标签有什么问题？


# In[59]:


from pyecharts.charts import Line, Grid
from pyecharts import options as opts

# 创建折线图（左边）
line_chart = (
    Line()
    .add_xaxis(df['Date'].tolist())
    .add_yaxis("Line Chart", df['Python'].tolist(), 
               symbol="circle",
               label_opts=opts.LabelOpts(is_show=False),
               linestyle_opts=opts.LineStyleOpts(width=3))
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Line Chart (Left)"),
        xaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(rotate=45)),
        yaxis_opts=opts.AxisOpts(name="Value"),
    )
)

# 创建面积图（右边）
area_chart = (
    Line()
    .add_xaxis(df['Date'].tolist())
    .add_yaxis("Area Chart", df['C'].tolist(),
               areastyle_opts=opts.AreaStyleOpts(opacity=0.5),
               symbol="circle",
               label_opts=opts.LabelOpts(is_show=False),
               linestyle_opts=opts.LineStyleOpts(width=3))
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Area Chart (Right)"),
        xaxis_opts=opts.AxisOpts(axislabel_opts=opts.LabelOpts(rotate=45)),
        yaxis_opts=opts.AxisOpts(name="Value"),
    )
)

# 使用 Grid 布局（1行2列）
grid = (
    Grid()
    .add(line_chart, grid_opts=opts.GridOpts(pos_left="5%", pos_right="55%"))  # 左边折线图
    .add(area_chart, grid_opts=opts.GridOpts(pos_left="55%", pos_right="5%"))   # 右边面积图
)

# 显示图表
grid.render_notebook()


# In[8]:


import plotly.express as px
import pandas as pd
from plotly.subplots import make_subplots

# 创建折线图（左边）
line_chart = px.line(df, x="Date", y="Python", title="Line Chart (Left)")

# 创建面积图（右边）
area_chart = px.area(df, x="Date", y="C", title="Area Chart (Right)")

# 组合成 1×2 布局

fig = make_subplots(rows=1, cols=2)
fig.add_trace(line_chart.data[0], row=1, col=1)
fig.add_trace(area_chart.data[0], row=1, col=2)

# 调整布局
fig.update_layout(
    title_text="1×2 Layout with Plotly Express",
    showlegend=True,
    height=500,
    width=900,
)

fig.show()


# # 随堂练习（3）

# In[9]:


# 下载后，假设有edges.csv，格式：Source, Target
df_edges = pd.read_csv('edges.csv')  # 节点关系数据

# 创建无向图
G = nx.from_pandas_edgelist(df_edges, 'source', 'target')

plt.figure(figsize=(12,12))
pos = nx.spring_layout(G, k=0.15)  # 通过弹簧布局确定节点位置
nx.draw_networkx_nodes(G, pos, node_size=20, node_color='blue', alpha=0.7)
nx.draw_networkx_edges(G, pos, alpha=0.3)
plt.title('Marvel Universe Network')
plt.axis('off')
plt.show()


# In[12]:


import pandas as pd
import networkx as nx
import matplotlib.pyplot as plt

# 读取dges.csv，格式：Source, Target
df_edges = pd.read_csv('edges.csv')  # 节点关系数据

G = nx.Graph()

G = nx.from_pandas_edgelist(df_edges, 'source', 'target')

plt.figure(figsize=(12,12))
pos = nx.spring_layout(G, k=0.15)  # 通过弹簧布局确定节点位置

nx.draw(G, 
        pos, 
        # with_labels=True, 
        node_color='skyblue', 
        edge_color='gray', 
        node_size=100, 
        font_size=16)
plt.title('Marvel Universe Network')
plt.axis('off')
plt.show()


# In[61]:


from pyecharts import options as opts
from pyecharts.charts import Graph

# 读取边数据
df_edges = pd.read_csv('edges.csv')  # 假设格式：source,target

# 提取所有唯一节点
all_nodes = set(df_edges['source']).union(set(df_edges['target']))
# print(all_nodes)

# 构建节点列表（默认大小，可根据需要调整）
nodes = [{"name": node, "symbolSize": 20} for node in all_nodes]

# 构建边列表
links = [
    {"source": row['source'], "target": row['target']}
    for _, row in df_edges.iterrows()
]

graph = (
    Graph()
    .add(
        "",
        nodes,
        links,
        repulsion=400,  # 排斥力，节点间距离
        layout="force",  # 力导向布局
        edge_symbol=["none", "arrow"],  # 边的箭头
        edge_symbol_size=10,
        linestyle_opts=opts.LineStyleOpts(opacity=0.6, width=1, curve=0.3),
        label_opts=opts.LabelOpts(is_show=True),
    )
    .set_global_opts(title_opts=opts.TitleOpts(title="Marvel Heroes Relationship Graph"))
)

graph.render_notebook()
# graph.render("graph.html")


# In[ ]:





# --- End of 13_Exercise.py ---

# --- Start of 13_TimeSeries_Dimensional_Network.py ---
#!/usr/bin/env python
# coding: utf-8

# # Lecture 13: 
# - 时间序列数据
# - 多维数据
# - 单个纬度数据
# - 网络数据

# In[1]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Line, Kline
from pyecharts.options import AreaStyleOpts, TitleOpts
import random
import plotly.express as px
import plotly.graph_objects as go

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号


# ## 时间序列可视化

# ### 折线图

# In[2]:


# 使用matplotlib绘制简单的折线图
x = [1, 2, 3, 4, 5]
y = [2, 3, 5, 7, 11]

plt.plot(x, y, marker='o')
plt.title("Line Chart - Matplotlib")
plt.xlabel("X Axis")
plt.ylabel("Y Axis")
plt.grid(True)
plt.show()


# In[3]:


# 构造 2025年1月的数据
# date_range用于生成日期范围的函数，start为初始日期，end为结束日期，freq是频率为“天”（Daily）
date_range = pd.date_range(start="2025-01-01", end="2025-01-31", freq="D")
df = pd.DataFrame({
    'date': date_range,
    'value': np.random.randint(0, 100, len(date_range))
})
plt.plot(df['date'].tolist(), df['value'].tolist(), marker='o')
plt.title("Line Chart （Time Series） - Matplotlib")
plt.xlabel("X Axis")
plt.ylabel("Y Axis")
plt.grid(True)
plt.show()


# In[6]:


# 创建 1x2 子图布局
fig, axes = plt.subplots(1, 2, figsize=(14, 5))

# 第一个子图：普通折线图
axes[0].plot(x, y, marker='o')
axes[0].set_title("Line Chart - Matplotlib")
axes[0].set_xlabel("X Axis")
axes[0].set_ylabel("Y Axis")
axes[0].grid(True)

# 第二个子图：时间序列折线图
axes[1].plot(df['date'].tolist(), df['value'].tolist(), marker='o')
axes[1].set_title("Line Chart (Time Series) - Matplotlib")
axes[1].set_xlabel("Date")
axes[1].set_ylabel("Value")
axes[1].grid(True)

# 注意下面的参数使得X轴标签有什么变化？
axes[1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()


# In[4]:


# 使用seaborn绘制
sns.lineplot(data=df, x='date',y='value')
plt.title("Line Chart - Seaborn")
plt.xlabel("X Axis")
plt.ylabel("Y Axis")
plt.grid(True)
plt.show()


# In[5]:


# 使用pyecharts绘制
line = Line()
line.add_xaxis(df['date'].tolist())
line.add_yaxis("Y-Value", df['value'].tolist())
# 在 Jupyter 中显示
# line.render_notebook()  
# 输出为HTML文件
line.render("line_chart.html")  


# In[6]:


# 使用plotly绘制
fig = px.line(df, 
              x='date', 
              y='value', 
              title='Line Chart - Plotly Express',
              markers=True)
fig.show()


# ### 面积图

# In[4]:


# 使用matplotlib绘制
plt.fill_between(df['date'], df['value'], color='skyblue', alpha=0.5)
plt.plot(df['date'].tolist(), df['value'].tolist(), color='blue')  # 边缘线
plt.title('Area Chart - Matplotlib')
plt.xlabel('X Axis')
plt.ylabel('Y Axis')
plt.grid(True)
plt.show()


# In[5]:


# 使用seaborn绘制
plt.figure(figsize=(6, 4))
plt.fill_between(df['date'], df['value'], color='lightgreen', alpha=0.6)
sns.lineplot(x='date', y='value', data=df, color='green')
plt.title('Area Chart - Seaborn Style')
plt.xlabel('X Axis')
plt.ylabel('Y Axis')
plt.show()


# In[40]:


# 使用pyecharts绘制
line = Line()
line.add_xaxis(df['date'].tolist())
line.add_yaxis("Series",
               df['value'].tolist(),
               is_smooth=True,
               areastyle_opts=AreaStyleOpts(opacity=0.5)
              )
line.set_global_opts(title_opts=TitleOpts(title="Area Chart - Pyecharts"))
line.render("area_chart.html")


# In[41]:


# 使用plotly中的express进行绘制
fig = px.area(df, 
              x='date', 
              y='value', 
              title='Area Chart - Plotly Express')
fig.show()


# ### K线图

# In[6]:


data = {
    'date': pd.date_range(start='2025-01-01', periods=5),
    'open': [100, 102, 101, 105, 107],
    'close': [102, 101, 105, 107, 106],
    'high': [103, 103, 106, 108, 109],
    'low': [99, 100, 100, 104, 105]
}
df = pd.DataFrame(data)
df


# In[7]:


df[['open', 'close', 'low', 'high']].values.tolist()


# In[46]:


# 使用pyecharts绘制
kline = Kline()
kline.add_xaxis(df['date'].dt.strftime("%Y-%m-%d").tolist())
kline.add_yaxis("Price", df[['open', 'close', 'low', 'high']].values.tolist())
kline.set_global_opts(title_opts=TitleOpts(title="K Line - Pyecharts"))
kline.render("kline.html")


# In[47]:


# 使用plotly中的graph_objects里的Candlestick绘制
fig = go.Figure(data=[go.Candlestick(
    x=df['date'],
    open=df['open'],
    high=df['high'],
    low=df['low'],
    close=df['close']
)])
fig.update_layout(title='K Line - Plotly', xaxis_title='Date', yaxis_title='Price')
fig.show()


# ### 日历图

# In[7]:


# 构造 2025年一整年的数据
date_range = pd.date_range(start="2025-01-01", end="2025-12-31", freq="D")
df = pd.DataFrame({
    'date': date_range,
    'value': np.random.randint(0, 100, len(date_range))
})
df


# In[8]:


# 使用matplotlib绘制_搭配 calmap
# !pip install calmap
import calmap

# 将 DataFrame 的 date 列设为索引
# calmap 要求输入数据的索引必须是 DatetimeIndex 或类似的时间类型
df_cal = df.set_index('date')
plt.figure(figsize=(16, 4))

# df_cal['value']： 要可视化的数据列
# 聚合方式： how='sum'，其他可选值如 mean（均值）、max（最大值）等
# cmap：颜色映射为 黄绿色渐变（Yellow-Green），颜色越深表示值越大
# fillcolor：无数据的日期格子填充为浅灰色
# 设置日历格子边框线的宽度为0.5
calmap.calendarplot(df_cal['value'], 
                    how='sum', 
                    cmap='YlGn', 
                    fillcolor='lightgrey', 
                    linewidth=0.5)
plt.title('Calendar Heatmap - Matplotlib (calmap)')
plt.show()


# In[19]:


# 使用pyecharts绘制
from pyecharts.charts import Calendar
from pyecharts import options as opts

# 将 df['date'] 和 df['value'] 组合成 PyEcharts 需要的格式
# data最终会变成： [("2023-01-01", 50), ("2023-01-02", 30), ...]
data = [(d.strftime("%Y-%m-%d"), int(v)) for d, v in zip(df['date'], df['value'])]

calendar = (
    Calendar() #创建一个日历热力图实例
    .add("", data, calendar_opts=opts.CalendarOpts(range_="2023"))
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Calendar Heatmap - Pyecharts"),
        visualmap_opts=opts.VisualMapOpts(
            max_=100,
            min_=0,
            orient="horizontal",
            is_piecewise=False,
        ),
    )
)
calendar.render("calendar.html")


# ## 多维数据可视化

# ### 热图

# In[10]:


# 创建 10x10 的随机整数矩阵
data = np.random.randint(0, 100, (10, 10))
df = pd.DataFrame(data, columns=[f"Col{i}" for i in range(10)], index=[f"Row{i}" for i in range(10)])
df


# In[12]:


# 使用matplotlib绘制
plt.figure(figsize=(8, 6))
plt.imshow(df, cmap='viridis')
plt.colorbar(label='Value')
plt.xticks(ticks=np.arange(df.shape[1]), labels=df.columns, rotation=45)
plt.yticks(ticks=np.arange(df.shape[0]), labels=df.index)
plt.title("Heatmap - Matplotlib")
plt.tight_layout()
plt.show()


# In[16]:


# 使用seaborn绘制
plt.figure(figsize=(8, 6))
sns.heatmap(df, annot=True, fmt="d", cmap="YlGnBu", linewidths=0.5)
plt.title("Heatmap - Seaborn")
plt.tight_layout()
plt.show()


# In[17]:


# 使用pyecharts绘制
from pyecharts.charts import HeatMap
from pyecharts import options as opts

# 转换数据为 Pyecharts 所需格式 [(x轴位置, y轴位置, 值), ...]
data_pair = [(j, i, int(df.iloc[i, j])) for i in range(df.shape[0]) for j in range(df.shape[1])]
# print(data_pair)

heatmap = (
    HeatMap()
    .add_xaxis(df.columns.tolist())
    .add_yaxis("Heat", df.index.tolist(), data_pair)
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Heatmap - Pyecharts"),
        visualmap_opts=opts.VisualMapOpts(min_=0, max_=100)
    )
)

heatmap.render("heatmap.html")


# In[18]:


print(df.values)
# 使用plotly.express绘制
fig = px.imshow(df.values, #二维数组
                labels=dict(x="Columns", y="Rows", color="Value"),
                x=df.columns,
                y=df.index,
                text_auto=True,
                color_continuous_scale='Viridis')

fig.update_layout(title="Heatmap - Plotly")
fig.show()


# ### 矩阵散点图

# In[19]:


# 加载数据
df = sns.load_dataset('iris')
df


# In[25]:


# 使用panda里带的模块绘制
from pandas.plotting import scatter_matrix

scatter_matrix(df.iloc[:, :-1], figsize=(10, 10), diagonal='hist')
# scatter_matrix是在绘制多个子图，因此调用 plt.title()，它只会对最后一个子图起作用
# “suptitle” 代表 “super title”——整个图（所有子图组合起来的大图）的主标题
plt.suptitle("Scatter Matrix - Matplotlib", fontsize=16)
plt.show()


# In[26]:


# 使用seaborn绘制
sns.pairplot(df, hue='species')
plt.suptitle("Pair Plot - Seaborn", y=1.02)
plt.show()


# In[28]:


# 使用plotly绘制
fig = px.scatter_matrix(df,
                        dimensions=["sepal_length", "sepal_width", "petal_length", "petal_width"],
                        color="species",
                        title="Scatter Matrix - Plotly")

# fig.update_traces(diagonal_visible=True)
fig.show()


# ### 平行坐标图

# In[36]:


# 加载数据
df = sns.load_dataset('iris')
df


# In[29]:


from pandas.plotting import parallel_coordinates
import matplotlib.pyplot as plt

plt.figure(figsize=(10, 6))
parallel_coordinates(df, 'species', colormap=plt.cm.Set2)
plt.title("Parallel Coordinates - Matplotlib")
plt.show()


# In[41]:


from pyecharts.charts import Parallel
from pyecharts import options as opts

# 将分类列species转为数字
df_copy = df.copy()
df_copy['species'] = df_copy['species'].astype('category').cat.codes

# 将数据封装成列表
data = df_copy.values.tolist()

# 维度定义
schema = [
    {"dim": 0, "name": "sepal_length"},
    {"dim": 1, "name": "sepal_width"},
    {"dim": 2, "name": "petal_length"},
    {"dim": 3, "name": "petal_width"},
    {"dim": 4, "name": "species"},
]

# 使用pyecharts绘图
parallel = (
    Parallel()
    .add_schema(schema)
    .add("Iris", data)
    .set_global_opts(title_opts=opts.TitleOpts(title="Parallel Coordinates - Pyecharts"))
)

parallel.render("parallel_pyecharts.html")


# In[39]:


fig = px.parallel_coordinates(
    df,
    dimensions=["sepal_length", "sepal_width", "petal_length", "petal_width"],
    title="Parallel Coordinates - Plotly"
)
fig.show()


# In[40]:


# 进阶
# 将species转换成数字编码
df['species_code'] = df['species'].astype('category').cat.codes

fig = px.parallel_coordinates(
    df,
    color='species_code',  # 传入数值列
    dimensions=["sepal_length", "sepal_width", "petal_length", "petal_width"],
    color_continuous_scale=px.colors.diverging.Tealrose,
    labels={"species_code": "Species"},
    title="Parallel Coordinates - Plotly"
)
fig.show()


# ## 单个维度比较图

# ### 子弹图

# In[31]:


# 参数说明：
# mode="number+gauge+delta"
# 显示数值、仪表盘和与目标的偏差。

# value
# 实际值。

# delta={'reference': xx}
# 指定目标值，并显示与其的差异。

# gauge.shape="bullet"
# 将仪表盘变成水平子弹图样式。

# steps
# 定义性能区间背景：如差、中、好。

# threshold
# 设置目标线（红线），视觉引导目标值。


# In[43]:


# 子弹图1：简单的子弹图，只显示了实际值和参考值之间的比较。
fig=go.Figure(go.Indicator(
    mode="number+gauge+delta",			# 指示器模式，包括数字、仪表盘和增减值
    gauge={'shape':"bullet"},			# 设置子弹图的形状为bullet
    value=220,							# 实际值
    delta={'reference':300},			# 增减值的参考值
    domain={'x':[0,1],'y':[0,1]},		# 子弹图所占的区域
    title={'text':"Profit"}))			# 子弹图的标题

fig.update_layout(height=250)			# 更新布局，设置图表的高度
fig.show()


# In[49]:


# 子弹图2：增加阈值和颜色阶梯，显示更详细的信息，包括颜色的变化表示不同的区间范围
fig=go.Figure(go.Indicator(
    mode="number+gauge+delta",value=220,	# 实际值
    domain={'x':[0.1,1],'y':[0,1]},			# 子弹图所占的区域
    title={'text':"<b>Profit</b>"},			# 子弹图的标题
    delta={'reference':200},				# 增减值的参考值
    gauge={'shape':"bullet",				# 设置子弹图的形状为bullet
         'axis':{'range':[None,300]},		# 指示器轴的范围
         'threshold':{'line':{'color':"red",'width':2},		# 阈值线的样式
					  'thickness':0.75,'value':280},			# 阈值的样式和值
         'steps':[{'range':[0,150],'color':"lightgray"},	# 不同范围的颜色
                  {'range':[150,250],'color':"gray"}]}))
fig.update_layout(height=250)				# 更新布局，设置图表的高度
fig.show()


# In[27]:


# matplotlib也可以绘制，但是需要自制
fig, ax = plt.subplots(figsize=(8, 2))

# 背景色条（分段）
ax.barh(0, ranges[2], color='lightgray', height=0.6)
ax.barh(0, ranges[1], color='gray', height=0.6)
ax.barh(0, ranges[0], color='darkgray', height=0.6)

# 实际值
ax.barh(0, performance, color='steelblue', height=0.3)

# 目标线
ax.plot([target, target], [-0.3, 0.3], color='red', linewidth=3)

ax.set_yticks([])
ax.set_xlim(0, 110)
ax.set_title("子弹图 - Matplotlib")
plt.show()


# ### 仪表盘图

# In[45]:


# 示例 1:创建一个简单的仪表图
fig=go.Figure(go.Indicator(
    mode="gauge+number",				# 模式设置为仪表盘模式并显示数值
    value=270,							# 设定指示器的数值为270
    domain={'x':[0,1],'y':[0,1]},		# 指示器的位置占据整个图表空间
    title={'text':"Speed"}))			# 指示器的标题为"Speed"
fig.show()


# In[46]:


# 示例 2:创建一个带有增量和阈值的仪表图
fig=go.Figure(go.Indicator(
    domain={'x':[0,1],'y':[0,1]},		# 指示器的位置占据整个图表空间
    value=450,							# 设定指示器的数值为450
    mode="gauge+number+delta",			# 模式设置为仪表盘模式、显示数值和增量
    title={'text':"Speed"},			# 指示器的标题为"Speed"
    delta={'reference':380},			# 增量设置为380
    gauge={
        'axis':{'range':[None,500]},		# 指示器轴范围设定为0到500
        'steps' :[			# 阶梯设置，将范围划分为两段，分别设定为灰色和深灰色
            {'range':[0,250],'color':"lightgray"},
            {'range':[250,400],'color':"gray"}],
        'threshold' :{'line':{'color':"red",'width':4},
             'thickness':0.75,'value':490}  	# 设定阈值，超过阈值时显示红色
    }))
fig.show()



# In[47]:


# 示例 3:创建一个带有增量和阈值的仪表图，样式定制更多
fig=go.Figure(go.Indicator(
    mode="gauge+number+delta",			# 模式设置为仪表盘模式、显示数值和增量
    value=420,							# 设定指示器的数值为420
    domain={'x':[0,1],'y':[0,1]},		# 指示器的位置占据整个图表空间
    title={'text':"Speed",'font':{'size':24}},		# 指示器标题，字体大小
    delta={'reference':400,'increasing':{'color':"RebeccaPurple"}},
     										# 增量设置为400，且增大时显示紫色
    gauge={
        'axis':{'range':[None,500],'tickwidth':1,
                'tickcolor':"darkblue"},		# 指示器轴范围设定，设置刻度宽度和颜色
        'bar':{'color':"darkblue"},			# 指示器条颜色设定为深蓝色
        'bgcolor':"white",					# 背景色设定为白色
        'borderwidth':2,					# 边框宽度设定为2
        'bordercolor':"gray",				# 边框颜色设定为灰色
        'steps':[    			# 阶梯设置，将范围划分为两段，分别设定为青色和皇家蓝
            {'range':[0,250],'color':'cyan'},
            {'range':[250,400],'color':'royalblue'}],
        'threshold':{		# 设定阈值为490，超过阈值时指示器显示红色
            'line':{'color':"red",'width':4},
            'thickness':0.75,'value':490 }}))

# 更新图表布局，设置背景色和字体颜色
fig.update_layout(paper_bgcolor="lavender",
                 font={'color':"darkblue",'family':"Arial"})
fig.show()


# ## 网络关系图

# ### 节点链接图

# In[51]:


# !pip3 install networkx
import matplotlib.pyplot as plt
import networkx as nx

plt.figure(figsize=(6, 6))

G = nx.Graph()
# 添加节点和边
G.add_edges_from([
    ('A', 'B'),
    ('A', 'C'),
    ('B', 'D'),
    ('C', 'D'),
    ('C', 'E')
])

pos = nx.spring_layout(G)  # 计算节点布局

nx.draw(G, 
        pos, 
        with_labels=True, 
        node_color='skyblue', 
        edge_color='gray', 
        node_size=1000, 
        font_size=16)
plt.title("Network Graph - Matplotlib + NetworkX")
plt.show()


# In[52]:


from pyecharts import options as opts
from pyecharts.charts import Graph

nodes = [
    {"name": "A", "symbolSize": 40},
    {"name": "B", "symbolSize": 30},
    {"name": "C", "symbolSize": 30},
    {"name": "D", "symbolSize": 20},
    {"name": "E", "symbolSize": 20},
]

links = [
    {"source": "A", "target": "B"},
    {"source": "A", "target": "C"},
    {"source": "B", "target": "D"},
    {"source": "C", "target": "D"},
    {"source": "C", "target": "E"},
]

graph = (
    Graph()
    .add("", nodes, links, repulsion=4000)
    .set_global_opts(title_opts=opts.TitleOpts(title="Network Graph - Pyecharts"))
)

# graph.render_notebook()  # Jupyter环境显示
graph.render("graph.html")


# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:





# In[ ]:





# --- End of 13_TimeSeries_Dimensional_Network.py ---

# --- Start of 14 Exercise.py ---
#!/usr/bin/env python
# coding: utf-8

# # 随堂练习（1）

# In[3]:


import jieba
from collections import Counter
from pyecharts.charts import WordCloud
from pyecharts import options as opts


# 统计词频并返回前20个词
def get_top_words(words, top_k=20):
    # counter返回一个类似字典的结构：每个单词出现了多少次
    counter = Counter(words)
    # most_common方法：返回出现频率最高的 n 个元素
    return counter.most_common(top_k)

# 读取当前目录下的文件
with open("学院简介", 'r', encoding='utf-8') as f:
    text = f.read()

# 使用jieba进行分词
words = jieba.lcut(text)
# 去除单个字和标点符号
words = [w for w in words if len(w) > 1 and w.strip()]

# 获取前20个词和词频
top_words = get_top_words(words, top_k=20)
wordcloud = (
    WordCloud()
    .add("", top_words, word_size_range=[20, 100], shape='circle')
    .set_global_opts(title_opts=opts.TitleOpts(title="词云图 - Top20 高频词"))
)
wordcloud.render("wordcloud.html")  # 生成 HTML 文件


# # 随堂练习（2）

# In[2]:


import dash
from dash import html, dcc, Input, Output
import pandas as pd
import plotly.express as px

# 创建数据
data = {
    'City': ['Beijing'] * 4 + ['Shanghai'] * 4 + ['Shenzhen'] * 4,
    'Product': ['A', 'B', 'C', 'D'] * 3,
    'Sales': [100, 150, 80, 70, 120, 90, 60, 130, 110, 100, 85, 95]
}
df = pd.DataFrame(data)

# 初始化 Dash 应用
app = dash.Dash(__name__)

# 按产品汇总总销售额
df_sum = df.groupby('Product', as_index=False)['Sales'].sum()
print(df_sum)
pie_fig = px.pie(df_sum, names='Product', values='Sales', title="产品销量占比饼图")

# 页面布局
app.layout = html.Div([
    # 顶部标题，设置格式
    html.H1("产品销售数据大屏", style={'textAlign': 'center', 'marginBottom': '30px'}),

    

    # 统计卡片
    html.Div(id='stats-cards', style={
        'display': 'flex',
        'justifyContent': 'space-around',
        'marginBottom': '40px'
    }),

    # 控件区域
    html.Div([
        html.Label("请选择城市："),
        dcc.Dropdown(
            id='city-dropdown',
            options=[{'label': c, 'value': c} for c in df['City'].unique()],
            value='Beijing',
            style={'width': '300px'}
        )
    ], style={'marginBottom': '30px'}),
    
    # 图表区域
    html.Div([
        
        html.Div([
            dcc.Graph(id='bar-chart')
        ], style={'width': '48%', 'display': 'inline-block'}),

        html.Div([
            dcc.Graph(figure=pie_fig)
        ], style={'width': '48%', 'display': 'inline-block'})
    ])
], style={'padding': '20px'})

# 回调：更新卡片 & 图表
@app.callback(
    [Output('stats-cards', 'children'), # id是 'stats-cards' 组件的 children 属性（就是这个区域里面显示的内容，通常是子组件）
     Output('bar-chart', 'figure'),],
    [Input('city-dropdown', 'value')]
)

def update_dashboard(city):
    filtered = df[df['City'] == city]

    total_sales = filtered['Sales'].sum()
    product_count = filtered['Product'].nunique()
    avg_sales = round(filtered['Sales'].mean(), 2)

    # 卡片组件——注意这里存储的是CSS样式
    # padding：内边距，卡片内容离边框距离，20像素。
    # borderRadius：圆角，10像素圆滑边角。
    # backgroundColor：背景色，浅灰色 #f0f0f0。
    # width：卡片宽度，这里设置为父容器的 30%。
    # textAlign：文字居中。
    # boxShadow：阴影效果，使卡片有立体感。
    card_style = {
        'padding': '20px',
        'borderRadius': '10px',
        'backgroundColor': '#f0f0f0',
        'width': '30%',
        'textAlign': 'center',
        'boxShadow': '2px 2px 8px rgba(0,0,0,0.1)'
    }

    # 这是一个列表，包含了 3 个 html.Div，代表 3 个统计卡片。
    # 每个卡片结构一样：
    # - html.Div 作为卡片容器
    # - 里面有标题（html.H4）和具体数值（html.H2）
    # - 通过 style=card_style，这 3 个卡片都共享刚才定义的样式。
    # # 由于外层容器用的是 flex 布局且 justifyContent: space-around，3 个卡片会横排且间距均匀。
    cards = [
        html.Div([
            html.H4("总销量"),
            html.H2(f"{total_sales}")
        ], style=card_style),

        html.Div([
            html.H4("产品种类"),
            html.H2(f"{product_count}")
        ], style=card_style),

        html.Div([
            html.H4("平均销量"),
            html.H2(f"{avg_sales}")
        ], style=card_style)
    ]

    # 图表
    bar_fig = px.bar(filtered, x='Product', y='Sales', title=f"{city} 产品销量柱状图")

    return cards, bar_fig

# 启动应用
if __name__ == '__main__':
    app.run_server(debug=True)


# In[ ]:





# --- End of 14 Exercise.py ---

# --- Start of 14 Word Cloud&Dash.py ---
#!/usr/bin/env python
# coding: utf-8

# # 14 Dashboard with Dash

# ## 词云图

# In[6]:


# 静态图
from wordcloud import WordCloud
import matplotlib.pyplot as plt

# 词频数据：字典（键：单词，值：数值）
text = {"Python": 100, "数据": 80, "可视化": 60, "机器学习": 90}

# 创建词云对象
wordcloud = WordCloud(font_path="simhei.ttf",  # 中文字体
                      width=800, 
                      height=400, 
                      background_color='white'
                     ).generate_from_frequencies(text)

# 展示词云图
plt.figure(figsize=(10, 5))
plt.imshow(wordcloud)
plt.axis("off")
plt.show()


# In[2]:


# pyecharts绘制
from pyecharts.charts import WordCloud
from pyecharts import options as opts

# 词云数据：元组列表
# 每个元组第一个元素是“单词”，第二个元素是个数
words = [("Python", 100), 
         ("数据", 80), 
         ("可视化", 60), 
         ("机器学习", 90), 
         ("深度学习", 70), 
         ("AI", 95)]

# 创建词云图
wc = (
    WordCloud()
    .add(series_name="词云", data_pair=words, word_size_range=[20, 100])
    .set_global_opts(title_opts=opts.TitleOpts(title="词云图"))
)

# 在jupyter notebook显示
wc.render_notebook()


# ## 数据大屏

# ### 简单的dash页面

# In[7]:


import dash
from dash import html

app = dash.Dash(__name__)  # 初始化 app

app.layout = html.Div([    # 设置网页结构
    html.H1("欢迎来到数据大屏"),
    html.P("这个页面由 Python 构建")
])

app.run_server(debug=True) # 启动网页


# ### 添加图表

# In[8]:


from dash import dcc
import plotly.express as px

df = px.data.iris()
fig = px.scatter(df, x="sepal_width", y="sepal_length", color="species")

app.layout = html.Div([
    html.H1("Iris 数据可视化"),
    dcc.Graph(figure=fig)
])


# In[10]:


# 上述代码会覆盖掉之前的信息，因此采用下面的合并方式


# In[9]:


app.layout = html.Div([
    html.H1("欢迎来到数据大屏"),
    html.P("这个页面由 Python 构建"),
    
    html.Hr(),  # 添加分隔线

    html.H1("Iris 数据可视化"),
    dcc.Graph(figure=fig)
])


# ### 实现交互

# In[12]:


# 可以在该界面通过 “关闭”按钮先关闭之前的网页应用，释放端口


# In[14]:


import dash
from dash import html, dcc, Input, Output
import plotly.express as px
import pandas as pd

# 创建数据，为后续绘制饼图做准备
# 假设我们这里设置三个城市每个产品（A B C D）的销量
data = {
    'City': ['Beijing'] * 4 + ['Shanghai'] * 4 + ['Shenzhen'] * 4,
    'Product': ['A', 'B', 'C', 'D'] * 3,
    'Sales': [100, 150, 80, 70, 120, 90, 60, 130, 110, 100, 85, 95]
}
df = pd.DataFrame(data)
df


# In[15]:


# 初始化app
app = dash.Dash(__name__)

# 设置页面布局，注意这里我们开始使用图表
app.layout = html.Div([
    html.H1("产品销售分布饼图", style={'textAlign': 'center'}),
    
    html.P("请选择城市："),
    dcc.Dropdown(
        id='city-dropdown',
        options=[{'label': city, 'value': city} for city in df['City'].unique()],
        value='Beijing'
    ),
    
    dcc.Graph(id='pie-chart')
])

# 回调函数 callback
@app.callback(
    Output('pie-chart', 'figure'),
    Input('city-dropdown', 'value')
)

def update_pie_chart(selected_city):
    filtered_df = df[df['City'] == selected_city]
    fig = px.pie(filtered_df, names='Product', values='Sales',
                 title=f"{selected_city} 的产品销售占比")
    return fig

# 启动应用
if __name__ == '__main__':
    app.run_server(debug=True)


# ### 拓展布局，实现：
# - html.Div(style=...) 实现
# - 使用 dcc.Dropdown 控制数据变化
# - @app.callback 多个输出（卡片 + 图表联动更新）

# In[17]:


import dash
from dash import html, dcc, Input, Output
import pandas as pd
import plotly.express as px

# 创建数据
data = {
    'City': ['Beijing'] * 4 + ['Shanghai'] * 4 + ['Shenzhen'] * 4,
    'Product': ['A', 'B', 'C', 'D'] * 3,
    'Sales': [100, 150, 80, 70, 120, 90, 60, 130, 110, 100, 85, 95]
}
df = pd.DataFrame(data)

# 初始化 Dash 应用
app = dash.Dash(__name__)

# 页面布局
app.layout = html.Div([
    # 顶部标题，设置格式
    html.H1("产品销售数据大屏", style={'textAlign': 'center', 'marginBottom': '30px'}),

    # 控件区域
    html.Div([
        html.Label("请选择城市："),
        dcc.Dropdown(
            id='city-dropdown',
            options=[{'label': c, 'value': c} for c in df['City'].unique()],
            value='Beijing',
            style={'width': '300px'}
        )
    ], style={'marginBottom': '30px'}),

    # 统计卡片
    html.Div(id='stats-cards', style={
        'display': 'flex',
        'justifyContent': 'space-around',
        'marginBottom': '40px'
    }),

    # 图表区域
    html.Div([
        html.Div([
            dcc.Graph(id='bar-chart')
        ], style={'width': '48%', 'display': 'inline-block'}),

        html.Div([
            dcc.Graph(id='pie-chart')
        ], style={'width': '48%', 'display': 'inline-block'})
    ])
], style={'padding': '20px'})

# 回调：更新卡片 & 图表
@app.callback(
    [Output('stats-cards', 'children'), # id是 'stats-cards' 组件的 children 属性（就是这个区域里面显示的内容，通常是子组件）
     Output('bar-chart', 'figure'),
     Output('pie-chart', 'figure')],
    [Input('city-dropdown', 'value')]
)

def update_dashboard(city):
    filtered = df[df['City'] == city]

    total_sales = filtered['Sales'].sum()
    product_count = filtered['Product'].nunique()
    avg_sales = round(filtered['Sales'].mean(), 2)

    # 卡片组件——注意这里存储的是CSS样式
    # padding：内边距，卡片内容离边框距离，20像素。
    # borderRadius：圆角，10像素圆滑边角。
    # backgroundColor：背景色，浅灰色 #f0f0f0。
    # width：卡片宽度，这里设置为父容器的 30%。
    # textAlign：文字居中。
    # boxShadow：阴影效果，使卡片有立体感。
    card_style = {
        'padding': '20px',
        'borderRadius': '10px',
        'backgroundColor': '#f0f0f0',
        'width': '30%',
        'textAlign': 'center',
        'boxShadow': '2px 2px 8px rgba(0,0,0,0.1)'
    }

    # 这是一个列表，包含了 3 个 html.Div，代表 3 个统计卡片。
    # 每个卡片结构一样：
    # - html.Div 作为卡片容器
    # - 里面有标题（html.H4）和具体数值（html.H2）
    # - 通过 style=card_style，这 3 个卡片都共享刚才定义的样式。
    # # 由于外层容器用的是 flex 布局且 justifyContent: space-around，3 个卡片会横排且间距均匀。
    cards = [
        html.Div([
            html.H4("总销量"),
            html.H2(f"{total_sales}")
        ], style=card_style),

        html.Div([
            html.H4("产品种类"),
            html.H2(f"{product_count}")
        ], style=card_style),

        html.Div([
            html.H4("平均销量"),
            html.H2(f"{avg_sales}")
        ], style=card_style)
    ]

    # 图表
    bar_fig = px.bar(filtered, x='Product', y='Sales', title=f"{city} 产品销量柱状图")
    pie_fig = px.pie(filtered, names='Product', values='Sales', title=f"{city} 产品销量占比饼图")

    return cards, bar_fig, pie_fig

# 启动应用
if __name__ == '__main__':
    app.run_server(debug=True)


# ## 实时刷新数据大屏

# ### 模拟数据

# In[1]:


import dash
from dash import html, dcc, Output, Input
import plotly.express as px
import random
from collections import deque
import pandas as pd

# 初始化数据（maxlen：最多保存20个点）
# 用于存储滑动窗口的数据（避免内存爆炸）
X = deque(maxlen=20)
Y = deque(maxlen=20)
X.append(0)
Y.append(random.randint(0, 100))

app = dash.Dash(__name__)

app.layout = html.Div([
    html.H2("模拟实时数据展示", style={'textAlign': 'center'}),
    
    dcc.Graph(id='live-update-graph'),

    # 用于定时更新（如1秒刷新一次）
    dcc.Interval(
        id='interval-component',
        interval=1000,  # 每秒更新一次
        n_intervals=0
    )
])

# 每次触发时，n_intervals自增，可驱动图表刷新
@app.callback(
    Output('live-update-graph', 'figure'),
    Input('interval-component', 'n_intervals')
)
def update_graph(n):
    X.append(X[-1] + 1)
    Y.append(Y[-1] + random.randint(-10, 10))

    data = pd.DataFrame({'x': X, 'y': Y})

    fig = px.scatter(
        data, 
        x='x', 
        y='y', 
        title='模拟数据散点图',
        labels={'x': '时间', 'y': '数值'},
    )

    # uirevision='static'：保持缩放、拖动状态不会因为刷新而重置
    fig.update_layout(title='实时数据折线图', 
                      xaxis_title='时间步', 
                      yaxis_title='值', uirevision='static')

    return fig

if __name__ == '__main__':
    app.run_server(debug=True)


# ### 通过API获取数据

# In[3]:


import dash
from dash import dcc, html, Input, Output
import plotly.graph_objs as go
import plotly.express as px
import requests
from datetime import datetime

app = dash.Dash(__name__)

# 初始化数据存储
price_history = {'time': [], 'price': []}

app.layout = html.Div([
    html.H1("比特币实时价格", style={'textAlign': 'center'}),
    dcc.Graph(id='price-chart'),
    dcc.Interval(
        id='refresh', 
        interval=10*1000)  # 10秒刷新
])

@app.callback(
    Output('price-chart', 'figure'),
    Input('refresh', 'n_intervals')
)
def update_chart(n):
    # 获取实时数据
    try:
        response = requests.get(
            "https://api.coingecko.com/api/v3/simple/price",
            params={'ids': 'bitcoin', 'vs_currencies': 'usd'}
        )
        price = response.json()['bitcoin']['usd']
    except:
        price = price_history['price'][-1] if price_history['price'] else 0

    # 更新数据记录（保留最近30个点）
    price_history['time'].append(datetime.now().strftime("%H:%M:%S"))
    price_history['price'].append(price)
    if len(price_history['time']) > 30:
        price_history['time'] = price_history['time'][-30:]
        price_history['price'] = price_history['price'][-30:]

    # 构建图表
    fig = px.line(
        price_history,
        x='time',
        y='price',
        color_discrete_sequence=['#FF4B4B'],  # 设置线条颜色
        labels={
            'time': '时间',
            'price': '价格 (USD)'
        }
    )

    # 更新布局保持原样式
    fig.update_layout(
        plot_bgcolor='#1E1E1E',
        paper_bgcolor='#1E1E1E',
        xaxis=dict(color='white'),
        yaxis=dict(color='white'),
        margin=dict(t=40),
        showlegend=False  # 隐藏自动生成的图例
    )
    
    # 设置线条宽度（需单独更新）
    fig.update_traces(line=dict(width=2))
    
    return fig

if __name__ == '__main__':
    # 默认端口：8050
    app.run_server(debug=True, port=8051)


# In[ ]:





# --- End of 14 Word Cloud&Dash.py ---

# --- Start of 2_crawling.py ---
#!/usr/bin/env python
# coding: utf-8

# ## 爬虫介绍
# * 使用`requests`库

# ### 1. 获取学院的首页内容
# * 单个网页
# * 简单网站，不需要设置请求头

# In[1]:


import requests
url = 'http://www.sbm.shisu.edu.cn/'
req = requests.get(url)
content = req.text
print(content[:1000])


# In[2]:


# 解决中文编码问题
print('website encoding: ', req.encoding)
req.encoding ='utf-8' # 人工纠正为utf-8，对中文进行编码
content = req.text
print(content[:1000])


# ### 2. 抓取京东某个网页的内容
# * 设置请求头(Headers)

# In[3]:


url = "https://item.jd.com/497227.html" 
req = requests.get(url, timeout=10)
content1 = req.text
headers = req.request.headers
print('content:',content1)  # 不设置请求头时，请求成功，但没有返回页面html内容
print('headers: ',headers)  # 当前的header不是真实的浏览器，而是python的requests爬虫程序


# In[4]:


# 设置请求头
url = "https://item.jd.com/497227.html" #空气净化器
headers = {'User-Agent':'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/56.0.2924.87 Safari/537.36'}
req = requests.get(url, headers=headers) # add header information for user-agent
print(req.status_code)
req.encoding = 'utf-8'
print(req.text[:1000])
print(req.request.headers)


# ### 3. BeautifulSoup解析

# In[5]:


from bs4 import BeautifulSoup
content = """
<html><head><title>The Dormouse's story</title></head>
<body>
<p class="title" name="dromouse"><b>The Dormouse's story</b></p>
<p class="story">Once upon a time there were three little sisters; and their names were
<a href="http://example.com/elsie" class="sister" id="link1"><!-- Elsie --></a>,
<a href="http://example.com/lacie" class="sister" id="link2">Lacie</a> and
<a href="http://example.com/tillie" class="sister" id="link3">Tillie</a>;
and they lived at the bottom of a well.</p>
<p class="story">...</p>
"""
soup = BeautifulSoup(content,"html.parser")
# 格式化输出
print(soup.prettify())


# #### 3.1 Tag对象

# In[81]:


# 获取标签
print(soup.title)
print(soup.a)
print(type(soup.title))


# In[85]:


# Tag的两个重要属性，name和attrs
tag = soup.a
tag.name


# In[86]:


tag.attrs


# In[87]:


# 获取tag中的某个属性，类比字典的使用方法
tag['href']


# #### 3.2 NavigableString对象

# In[89]:


# 获取标签内部的文字
title = soup.title.string
print(title)
print(type(title))


# #### 3.3 搜索文档树

# In[92]:


from IPython.display import HTML
content = """
<html><head><title>The Dormouse's story</title></head>
<body>
<p class="title" name="dromouse"><b>The Dormouse's story</b></p>
<p class="story">Once upon a time there were three little sisters; and their names were
<a href="http://example.com/elsie" class="sister" id="link1"><!-- Elsie --></a>,
<a href="http://example.com/lacie" class="sister" id="link2">Lacie</a> and
<a href="http://example.com/tillie" class="sister" id="link3">Tillie</a>;
and they lived at the bottom of a well.</p>
<p class="story">...</p>
"""
output = HTML(content)
output


# In[93]:


soup = BeautifulSoup(content)
soup.find_all('a')


# In[94]:


soup.find_all(['a','b'])  # 对于列表，任一匹配均可


# In[95]:


# 根据标签的属性查询
soup.find_all(id ='link2') # 所有id为link2的Tag


# In[96]:


soup.find_all(id=True)  # 所有含有id属性的标签


# In[98]:


soup.find_all(attrs={"class":"story"})


# In[102]:


soup.find_all(class_ = 'title')   # 通过class，需要用class_，因为class为系统保留字


# In[97]:


# 同时指定tag及其属性
soup.find_all('a',id='link1')


# In[103]:


for each in soup.find_all('a',class_='sister'):
    print(each)
    print(each.string)


# In[105]:


# 同时指定tag及其属性，几种方式均可
print(soup.find_all(name = 'a',attrs ={'id':'link1'}))
print(soup.find_all('a',attrs ={'id':'link1'}))
print(soup.find_all('a',id = 'link1'))


# ### 4. 简单实例
# * 爬取学院网站的新闻公告内容

# In[6]:


import requests
from bs4 import BeautifulSoup


# In[7]:


# 获取网页内容
url = 'http://www.sbm.shisu.edu.cn/7290/list.htm'
req = requests.get(url)
req.encoding = 'utf-8'
content = req.text
print(content)


# In[8]:


# 解析
soup = BeautifulSoup(content, 'html.parser')
all_news = soup.find('div', id='wp_news_w24')
for news in all_news.find_all('li'):
    news_title = news.find('span', class_='news_title').string
    date = news.find('span', class_ = 'news_meta').string
    print(date,news_title)


# In[10]:


# 转成DataFrame，并存入csv文件
import pandas as pd
# 创建空的数据框
df = pd.DataFrame({'date': [],
                'news_title': []
               })
for news in all_news.find_all('li'):
    news_title = news.find('span', class_='news_title').string  # 从html中获取新闻标题
    date = news.find('span', class_ = 'news_meta').string  # 从html中获取对应的新闻时间
    record = pd.DataFrame({'date': [date],
                'news_title': [news_title]
               })   # 包装成一个数据框
    df = pd.concat([df, record], ignore_index=True)  # 循环追加每一条新闻的内容到数据框
df = df.reset_index()   # 重新索引
df = df[['date','news_title']]
df.to_csv('news.csv',encoding="utf_8_sig")
df.head()


# #### 4.1 翻页

# In[133]:


import requests
from bs4 import BeautifulSoup
# 方法一
# 观察网站，发现一共有11页，通过不同页码之间的url规律构建url池，遍历抓取每一页
for page_no in range(1,12):
    print("抓取第 ",page_no," 页的新闻内容...")
    # 获取网页内容
    url = 'http://www.sbm.shisu.edu.cn/7290/list' + str(page_no) + '.htm'
    req = requests.get(url)
    req.encoding = 'utf-8'
    content = req.text
    
    # 解析网页内容
    soup = BeautifulSoup(content, 'html.parser')
    all_news = soup.find('div', id='wp_news_w24')
    for news in all_news.find_all('li'):
        news_title = news.find('span', class_='news_title').string   # 新闻标题
        date = news.find('span', class_ = 'news_meta').string  # 发布日期
        print(date,news_title)


# In[134]:


import requests
from bs4 import BeautifulSoup
# 方法二
# 从当前网页中找到下一页的url
href = '/7290/list.htm'   # 起始页url后缀
count = 0 # 用于记录当前的页码
while 'htm' in href:
    count += 1
    print("抓取第 ",count," 页的新闻内容...")
    # 获取网页内容
    url = 'http://www.sbm.shisu.edu.cn' + href   # 每一次爬取时完整的url
    req = requests.get(url)
    req.encoding = 'utf-8'
    content = req.text
    
    # 解析网页内容
    soup = BeautifulSoup(content, 'html.parser')
    all_news = soup.find('div', id='wp_news_w24')
    for news in all_news.find_all('li'):
        news_title = news.find('span', class_='news_title').string   # 新闻标题
        date = news.find('span', class_ = 'news_meta').string  # 发布日期
        print(date,news_title)
    
    # 获取下一页的url，更新herf变量
    next_url = soup.find('a',class_='next')
    href = next_url['href']


# In[136]:


# 将解析后的内容存储到csv文件中

import requests
from bs4 import BeautifulSoup
import pandas as pd

saved_data = {'date':[],'news_title':[]}  # 将所有内容存储到一个字典中
# 方法二
# 从当前网页中找到下一页的url
href = '/7290/list.htm'   # 起始页url后缀
count = 0 # 用于记录当前的页码
while 'htm' in href:
    count += 1
    print("抓取第 ",count," 页的新闻内容...")
    # 获取网页内容
    url = 'http://www.sbm.shisu.edu.cn' + href   # 每一次爬取时完整的url
    req = requests.get(url)
    req.encoding = 'utf-8'
    content = req.text
    
    # 解析网页内容
    soup = BeautifulSoup(content, 'html.parser')
    all_news = soup.find('div', id='wp_news_w24')
    for news in all_news.find_all('li'):
        news_title = news.find('span', class_='news_title').string   # 新闻标题
        date = news.find('span', class_ = 'news_meta').string  # 发布日期
        # print(date,news_title)
        # 更新字典
        saved_data['date'].append(date)
        saved_data['news_title'].append(news_title)
        
    
    # 获取下一页的url，更新herf变量
    next_url = soup.find('a',class_='next')
    href = next_url['href']

# 解析完成，将所有内容存入csv
df = pd.DataFrame(saved_data)
df.to_csv('news.csv',encoding="utf_8_sig")
df


# --- End of 2_crawling.py ---

# --- Start of 2_douban_only_crawling.py ---
#!/usr/bin/env python
# coding: utf-8

# In[2]:


import requests
from bs4 import BeautifulSoup
import time

top250_url = "https://movie.douban.com/top250?start={}&filter="
headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.99 Safari/537.36'}

page = 0

for i in range(10):
    start = i*25
    page += 1
    print('抓取第'+str(page)+'页的内容...')
    url_visit = top250_url.format(start)
    req = requests.get(url_visit,headers=headers)  # 注意加入headers
    req.encoding = req.apparent_encoding
    content = req.text
    time.sleep(2)

    # 解析
    soup = BeautifulSoup(content, 'html.parser')
    all_item_divs = soup.find_all('div',class_='item')
    
    # 之前给的代码
    # for each_item in all_item_divs:
    #     pic_div = each_item.find(class_='pic')
    #     rank = pic_div.find('em').string  # 排名
    #     title = pic_div.find('img')['alt']  #电影名称
    #     url = pic_div.find('a')['href']      #电影链接
    # 
    #     star_div = each_item.find(class_='star')
    #     avg_rating = star_div.find('span',class_='rating_num').string  # 平均得分
    #     rating_num = star_div.find_all('span')[3].get_text()[:-3]  # 评分人数
    # 
    #     print(rank,title,url,avg_rating,rating_num)
    
    # 新代码
    for each_item in all_item_divs:
        pic_div = each_item.find(class_='pic')
        rank = pic_div.find('em').string  # 排名
        title = pic_div.find('img')['alt']  #电影名称
        url = pic_div.find('a')['href']      #电影链接
    
        star_div = each_item.find(class_='bd')
        attributes_div = star_div.find('div')
        avg_rating = attributes_div.find('span',class_='rating_num').string  # 平均得分
        rating_num = star_div.find_all('span')[3].get_text()[:-3]  # 评分人数
    
        print(rank,title,url,avg_rating,rating_num)


# In[ ]:





# --- End of 2_douban_only_crawling.py ---

# --- Start of 3_processing.py ---
#!/usr/bin/env python
# coding: utf-8

# ## Lecture 3: 序列和数据框处理 `Pandas`

# ### 关于Pandas
# * 主要的数据结构有三种，Series, DataFrame, Panel

# ### 创建Series

# #### 1）Data是多维数组

# In[3]:


import pandas as pd
import numpy as np
s = pd.Series(np.random.randn(5),index = ['a','b','c','d','e'])  # 索引index长度必须与data长度一致
print(s)


# In[2]:


t = pd.Series(np.random.randn(5)) # 没有指定index参数时，默认创建数值型索引
print(t)


# In[3]:


n = pd.Series(['Nanjing','Hangzhou','Fuzhou'])
print(n)


# #### 2）Data是字典

# In[ ]:


# 未设置index参数时，如果Python版本>=3.6且Pandas版本>=0.23，Series按照字典的插入顺序排列索引；否则按字母顺序排序字典的key列表
d = {'b': 1, 'a': 0, 'c': 2}
d1 = pd.Series(d)
print(d1)


# In[6]:


# 如果设置了index参数，则按索引标签提取data里面的值。使用NaN (Not a Number)表示缺失数据
d = {'b': 1, 'a': 0, 'c': 2}
d2 = pd.Series(d, index=['b', 'c', 'd', 'a'])
print(d2)


# #### 3）Data是标量值

# In[7]:


# 必须提供索引。Series按索引长度重复该标量值
e = pd.Series(5., index=['a', 'b', 'c', 'd', 'e'])
print(e)


# ### 读取Series数据

# In[8]:


# 使用序列的属性 values 和 index 得到数据值和索引值
import pandas as pd
e = pd.Series(5., index=['a', 'b', 'c', 'd', 'e'])
print(e.values)
print(e.index)


# In[9]:


# 通过索引的方式选择 Series序列对象中的值
s = pd.Series(['Python','SQL','Java','English'], index=['a', 'b', 'c', 'd'])
print(s['d'])


# In[10]:


# 支持索引切片
print(s['c':'d'])


# ### 创建DataFrame

# #### 1) 从字典中创建DataFrame

# In[40]:


d = {'one': [1., 2., 3., 4.],
     'two': [6., 7., 8., 9.]}
df = pd.DataFrame(d)
df


# #### 2）从Series字典中创建DataFrame

# In[41]:


d = {'one': pd.Series([1., 2., 3.], index=['a', 'b', 'c']),
     'two': pd.Series([1., 2., 3., 4.], index=['a', 'b', 'c', 'd'])}
pd.DataFrame(d)


# In[50]:


s1 = pd.Series({'city': 'Nanjing',
                'province': 'Jiangsu',
                'population': 8.335})
s2 = pd.Series({'city': 'Hangzhou',
                'province': 'Zhejiang',
                'population': 9.468})
s3 = pd.Series({'city': 'Fuzhou',
                'province': 'Fujian',
                'population': 7.64})
df = pd.DataFrame([s1, s2, s3])
df


# #### 3) 从Numpy二维数据中创建数据框。

# In[120]:


import numpy as np
data = np.random.randint(0,150,size=(4,4))
index = ['张三','李四','王五','赵六']
columns = ['语文','数学','英语','python']
df = pd.DataFrame(data=data, index = index, columns = columns)
df


# #### 4）指定index和columns

# In[52]:


# 原本数据集不包含index时，指定index将为其赋予新的index
d = {'one': [1., 2., 3., 4.],
     'two': [6., 7., 8., 9.]}
pd.DataFrame(d, index = ['a','b','c','d'])


# In[55]:


# 原本数据集包含index时，指定index将仅展示所指定的index（行）
d = {'one': pd.Series([1., 2., 3.], index=['a', 'b', 'c']),
     'two': pd.Series([1., 2., 3., 4.], index=['a', 'b', 'c', 'd'])}
df = pd.DataFrame(d, index = ['a','b','c','e'], columns = ['two','three'])
df


# #### 5) 访问行和列标签

# In[56]:


df.index


# In[57]:


df.columns


# ### 读取数据：CSV， EXCEL

# In[21]:


# 从csv文件中读取数据
r = pd.read_csv('data/Fish.csv')
r


# In[24]:


# 只输出前几行
r.head()


# In[33]:


# 从Excel文件中读取数据
r = pd.read_excel('data/fish_new.xlsx',sheet_name = 'Fish')
r.head()


# ### 写入数据：CSV

# #### 写入到csv文件中

# In[37]:


import pandas as pd
df = pd.DataFrame(
    dict(A=range(1, 4), B=range(4, 7), C=range(7, 10)),
    columns=['A','B','C'],
    index=['x','y','z'],
)
df


# In[38]:


df.to_csv('data/test1.csv')


# ### 切片和查询

# #### 1）查看DataFrame的头部和尾部数据

# In[60]:


import pandas as pd
r = pd.read_csv('data/Fish.csv')
r.head(8)


# In[61]:


r.tail()


# #### 2) 描述与排序

# In[62]:


r.describe()


# In[64]:


# 转置数据
r.T


# In[69]:


# 按轴排序 （行标签 or 列标签）
r.sort_index(axis = 1, ascending = False)


# In[72]:


# 按某一列的值排序
r.sort_values(by  = 'Width')


# #### 3） 选择列：使用列索引

# In[79]:


r['Weight'].head()
r[['Weight','Length1']].head()


# #### 4) 选择行：使用行号

# In[78]:


r[0:10:2]


# #### 5) 同时选择行和列，使用属性 `loc`，按索引值选择

# In[80]:


r.loc[0:10:2,['Weight','Length1']]


# #### 6) 同时选择行和列，使用属性 `iloc`，按位置选择

# In[82]:


r.iloc[0:10:2,0:10:2]


# In[83]:


r.iloc[[10,15],]


# #### 7) 布尔索引，找出满足某个条件的值

# In[85]:


r[r.Weight>500].head()


# In[90]:


r[r.Species == 'Bream']
r[r['Species'] == 'Bream']
r[r['Species'].isin(['Bream','Pike'])]


# ### 数据框的运算

# #### 1）简单的运算：加减乘除

# In[91]:


# Series的运算，将在index上对齐后计算
s1 = pd.Series([7.3, -2.5, 3.4, 1.5], index = ['a', 'c', 'd', 'e'])
s2 = pd.Series([-2.1, 3.6, -1.5, 4, 3.1], index = ['a', 'c', 'e', 'f', 'g'])
s1 + s2


# In[92]:


# DataFrame的运算，将分别在行和列的索引上对齐后计算
df1 = pd.DataFrame(np.arange(9).reshape((3,3)), columns = list('bcd'), index = ['Ohio', 'Texas', 'Colorado'])
df2 = pd.DataFrame(np.arange(12).reshape((4,3)), columns = list('bde'), index = ['Utah', 'Ohio', 'Texas', 'Oregon'])
df1 + df2


# In[93]:


# 填充默认值
df1 = pd.DataFrame(np.arange(9).reshape((3,3)), columns = list('bcd'), index = ['Ohio', 'Texas', 'Colorado'])
df2 = pd.DataFrame(np.arange(12).reshape((4,3)), columns = list('bde'), index = ['Utah', 'Ohio', 'Texas', 'Oregon'])
df1.add(df2, fill_value = -100)


# #### 2) 一些常见的统计函数

# In[96]:


df = pd.read_csv('data/Fish.csv')
df.mean()


# In[97]:


df['Width'].mean()


# In[99]:


df.mean(1)   # 在轴1上执行均值计算（即，每行计算均值）


# #### 3）聚合：Groupby函数

# In[105]:


grouped = df.groupby(by='Species')  # 按照列 Species进行分组
grouped  # 返回一个groupby分组对象


# In[107]:


# 对groupby分组对象使用统计函数
grouped.size()
grouped.sum()
grouped.mean()


# In[115]:


# 按照多个列/变量进行分组
df1 = pd.DataFrame(
    dict(A=['Ana','Ana','Bob','Bob'], 
         B=['English','English','English','Math'],
         C=range(7, 11)),
    columns=['A','B','C'],
    index=['x','y','z','t'],
)
df1


# In[116]:


df1.groupby(['A','B']).count()


# ### 添加、删除、修改操作

# #### 1）添加新列

# In[123]:


df = pd.read_csv('data/Fish.csv')
df['new_column'] = -100    # 添加一列，具有相同的取值
df.head()


# In[131]:


# 可添加多个具有实际含义的列
df['total_length'] = df['Length1'] + df['Length2'] + df['Length3']
df['ratio'] = df['Height']/df['Width']
df.iloc[0:5,-4:]   # 只展示前5行，最后4列


# #### 2) 添加新行

# In[132]:


df1 = pd.DataFrame(
    {
    "A": ["A0", "A1", "A2", "A3"],
    "B": ["B0", "B1", "B2", "B3"],
    "C": ["C0", "C1", "C2", "C3"],
    "D": ["D0", "D1", "D2", "D3"],},
    index=[0, 1, 2, 3],
)
df1


# In[133]:


df2 = pd.DataFrame(
    {
    "A": ["A4", "A5", "A6", "A7"],
    "B": ["B4", "B5", "B6", "B7"],
    "C": ["C4", "C5", "C6", "C7"],
    "D": ["D4", "D5", "D6", "D7"],},
    index=[0, 1, 2, 3],
)
df2


# In[134]:


# 将两个数据集按照行连接：df1与df2的变量名完全相同
result = df1.append(df2)
result


# In[135]:


df4 = pd.DataFrame(
    {
        "B": ["B2", "B3", "B6", "B7"],
        "D": ["D2", "D3", "D6", "D7"],
        "F": ["F2", "F3", "F6", "F7"],},
    index=[2, 3, 6, 7],
)
df4


# In[139]:


# 将df1与df4按照行连接，对索引值index重新编号，且两个数据集的列不完全相同时，生成的新数据集为所有列的并集
result = df1.append(df4, ignore_index=True)
result 


# In[146]:


# 也可以使用concat进行行或者列的添加
result = pd.concat([df1,df4[0:3]], axis = 1)  # 沿着轴1（列）进行添加
result


# In[147]:


# 也可以使用concat进行行或者列的添加
result = pd.concat([df1,df4[0:3]], axis = 0)  # 沿着轴0（行）进行添加
result


# #### 3）删除数据

# In[161]:


df = pd.read_csv('data/Fish.csv')
df['new_column'] = -100    
del df['new_column']  # 使用del来删除列
# 使用pop来删除列，并返回删除的内容
popped_var = df.pop('Width')
popped_var


# In[168]:


# 使用drop进行删除行或者列
df.drop([0,3]).loc[0:6,]


# In[170]:


df.drop(['Height','Weight'],axis=1).head()


# #### 4) 修改行或者列标签（索引名）

# In[180]:


df.rename({'Weight':'Weight_new','Height':'Height_new'},axis = 1).head()
# df.rename(columns={'Weight':'Weight_new','Height':'Height_new'}).head()


# #### 5) 修改某些数据

# In[183]:


# 使用前面讲过的查找数据（切片）的方法来定位要修改的数据，然后通过赋值操作进行修改。
# 找到Sepecies为Bream的行，对这些行的Weight变量进行修改
df.loc[df['Species']=='Bream', 'Weight'] = -1000
df.head()


# ### 处理缺失值

# #### 1）发现缺失值

# In[184]:


type(np.nan)  # NaN和None的区别


# In[185]:


type(None)


# In[186]:


data = pd.Series([1,np.nan,'Hello',None])
data.isnull()


# #### 2）剔除缺失值

# In[187]:


df = pd.DataFrame([[1, np.nan, 2],
                   [2, 3, 5],
                   [np.nan, 4, 6]])
df


# In[188]:


df.dropna()  # 默认删除所有包含缺失值的数据


# In[191]:


df.dropna(axis='columns')  # 删除包含缺失值的列


# In[192]:


# 删除绝大多数是缺失值的行或列，则可以设置how或者thresh参数。
df[3] = np.nan
df


# In[195]:


# 删除全部是缺失值的列
df.dropna(axis='columns',how='all')


# In[201]:


# thresh是阈值，表示至少有thresh个非空数值的才保留下来。
df.dropna(axis='index',thresh=3)


# #### 3) 填充缺失值

# In[202]:


data = pd.Series([1, np.nan, 2, None, 3], index = list('abcde'))
data


# In[203]:


data.fillna(0)


# In[204]:


data.fillna(method='ffill') #用缺失值前面的有效值来从前往后填充（forward-fill）


# In[205]:


data.fillna(method='bfill') # 用缺失值后面的有效值来从前往后填充（backward-fill）


# In[209]:


df = pd.DataFrame([[1, np.nan, 2],
                   [2, 3, 5],
                   [np.nan, 4, 6]])
df.fillna(method='ffill',axis=1)


# --- End of 3_processing.py ---

# --- Start of 6_demo_packages.py ---
#!/usr/bin/env python
# coding: utf-8

# ## matplotlib包

# #### 利用axes()函数可以在一幅图中生成多个坐标图形（axes）

# In[1]:


import matplotlib.pyplot as plt

plt.figure()
plt.axes([0.0,0.0,1,1])
plt.axes([0.1,0.1,.5,.5],facecolor='blue')
plt.axes([0.2,0.2,.5,.5],facecolor='pink')
plt.axes([0.3,0.3,.5,.5],facecolor='green')
plt.axes([0.4,0.4,.5,.5],facecolor='skyblue')
plt.show()


# ### 创建图形的简单实例

# In[4]:


import matplotlib.pyplot as plt
from sklearn.datasets import load_iris

# 加载 iris 数据集
iris=load_iris()
data=iris.data
target=iris.target

# 提取数据
sepal_length=data[:,0]
petal_length=data[:,2]

# 创建图形和子图
fig,axs=plt.subplots(1,2,figsize=(10,5))	# 创建包含两个子图的图形
fig.suptitle('Sepal Length vs Petal Length',fontsize=16)		# 设置图形标题

# 第1个子图：线图
axs[0].plot(sepal_length,label='Sepal Length',color='blue',
             linestyle='-')				# 绘制线图
axs[0].plot(petal_length,label='Petal Length',color='green',
             linestyle='--')				# 绘制另一个线图
axs[0].set_xlabel('Sample')				# 设置x轴标签
axs[0].set_ylabel('Length')				# 设置y轴标签
axs[0].legend()							# 添加图例
axs[0].grid(True)						# 添加网格线

# 第2个子图：散点图
scatter=axs[1].scatter(sepal_length,petal_length,c=target,
						       cmap='viridis',label='Data Points')	# 绘制散点图
axs[1].set_xlabel('Sepal Length')		# 设置x轴标签
axs[1].set_ylabel('Petal Length')		# 设置y轴标签
axs[1].legend()							# 添加图例
axs[1].grid(True)						# 添加网格线
fig.colorbar(scatter,ax=axs[1],label='Species')				# 添加颜色条

plt.tight_layout()						# 自动调整子图布局
plt.show()		# 显示图形


# ### 使用plt.subplot()函数创建子图

# In[7]:


import matplotlib.pyplot as plt
import seaborn as sns				# seaborn 库内置了iris数据集

import ssl
ssl._create_default_https_context = ssl._create_unverified_context

# 加载iris数据集并查看其结构
iris=sns.load_dataset('iris')
iris.head()							# 输出略
plt.figure(figsize=(10,6))			# 设置画布大小

# 第1个子图
plt.subplot(2,2,1)					# 2行2列的第1个
plt.hist(iris['sepal_length'],color='blue')
plt.title('Sepal Length')
# 第2个子图
plt.subplot(2,2,2)				# 2行2列的第2个
plt.hist(iris['sepal_width'],color='orange')
plt.title('Sepal Width')
# 第3个子图
plt.subplot(2,2,3)				# 2行2列的第3个
plt.hist(iris['petal_length'],color='green')
plt.title('Petal Length')
# 第4个子图
plt.subplot(2,2,4)				# 2行2列的第4个
plt.hist(iris['petal_width'],color='red')
plt.title('Petal Width')

plt.tight_layout()				# 自动调整子图间距
plt.show()


# ### 使用plt.subplots()函数创建子图

# In[8]:


import matplotlib.pyplot as plt
import seaborn as sns

import ssl
ssl._create_default_https_context = ssl._create_unverified_context

data=sns.load_dataset("iris")				# 加载内置的iris数据集

# 使用plt.subplots()创建一个2行3列的子图布局
fig,axs=plt.subplots(2,3,figsize=(15,8))

# 第1个子图：绘制sepal_length和sepal_width的散点图
axs[0,0].scatter(data['sepal_length'],data['sepal_width'])
axs[0,0].set_title('Sepal Length vs Sepal Width')

# 第2个子图：绘制petal_length和petal_width的散点图
axs[0,1].scatter(data['petal_length'],data['petal_width'])
axs[0,1].set_title('Petal Length vs Petal Width')

# 第3个子图：绘制sepal_length的直方图
axs[0,2].hist(data['sepal_length'],bins=20)
axs[0,2].set_title('Sepal Length Distribution')

# 4个子图：绘制petal_length的直方图
axs[1,0].hist(data['petal_length'],bins=20)
axs[1,0].set_title('Petal Length Distribution')

# 第5和第6位置合并为一个大图，展示species的计数条形图
# 为了合并第二行的中间和最右侧位置，使用subplot2grid功能
plt.subplot2grid((2,3),(1,1),colspan=2)
sns.countplot(x='species',data=data)
plt.title('Species Count')

plt.tight_layout()			# 调整子图之间的间距
plt.show()


# ### 使用figure.add_subplot()函数创建子图

# In[9]:


import matplotlib.pyplot as plt

fig = plt.figure(figsize=(8,4))		# 创建一个图形实例

# 添加第1个子图：1行2列的第1个位置
ax1=fig.add_subplot(1,2,1)
ax1.plot([1,2,3,4],[1,4,2,3])		# 绘制一条简单的折线图
ax1.set_title('First Subplot')

# 添加第2个子图：1行2列的第2个位置
ax2=fig.add_subplot(122)
ax2.bar([1,2,3,4],[10,20,15,25])	# 绘制一个条形图
ax2.set_title('Second Subplot')

# 显示图形
plt.tight_layout()					# 自动调整子图参数，使之填充整个图形区域
plt.show()


# ### 使用subplot2grid()函数创建子图

# In[9]:


import matplotlib.pyplot as plt
from sklearn.datasets import load_iris

# 载入鸢尾花数据集
iris=load_iris()
data=iris.data
target=iris.target
feature_names=iris.feature_names
target_names=iris.target_names

grid_size=(3,3)			# 定义网格大小为3x3

# 第1个子图占据位置 (0,0)
ax1=plt.subplot2grid(grid_size,(0,0),facecolor='orange')
ax1.scatter(data[:,0],data[:,1],c=target,cmap='viridis')
ax1.set_xlabel(feature_names[0])
ax1.set_ylabel(feature_names[1])

# 第2个子图占据位置(0,1)，并跨越2列
ax2=plt.subplot2grid(grid_size,(0,1),colspan=2,facecolor='pink')
ax2.scatter(data[:,1],data[:,2],c=target,cmap='viridis')
ax2.set_xlabel(feature_names[1])
ax2.set_ylabel(feature_names[2])

# 第3个子图占据位置(1,0)，并跨越2行
ax3=plt.subplot2grid(grid_size,(1,0),rowspan=2,facecolor='grey')
ax3.scatter(data[:,0],data[:,2],c=target,cmap='viridis')
ax3.set_xlabel(feature_names[0])
ax3.set_ylabel(feature_names[2])

# 第4个子图占据位置 (1,1)，并跨越到最后
ax4=plt.subplot2grid(grid_size,(1,1),colspan=2,
						     rowspan=2,facecolor='skyblue')
ax4.scatter(data[:,2],data[:,3],c=target,cmap='viridis')
ax4.set_xlabel(feature_names[2])
ax4.set_ylabel(feature_names[3])

plt.tight_layout()
plt.show()


# ### 使用gridspec.GridSpec()函数创建子图

# In[10]:


import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from sklearn.datasets import load_iris

# 载入Iris数据集
iris=load_iris()
data=iris.data
target=iris.target
feature_names=iris.feature_names
target_names=iris.target_names

# 创建一个2x2的子图网格
fig=plt.figure(figsize=(10,6))
gs=gridspec.GridSpec(2,2,height_ratios=[1,1],width_ratios=[1,1])

# 在网格中创建子图
ax1=plt.subplot(gs[0,0])
ax1.scatter(data[:,0],data[:,1],c=target,cmap='viridis')
ax1.set_xlabel(feature_names[0])
ax1.set_ylabel(feature_names[1])
ax1.set_title('Sepal Length vs Sepal Width')

ax2=plt.subplot(gs[0,1])
ax2.scatter(data[:,1],data[:,2],c=target,cmap='viridis')
ax2.set_xlabel(feature_names[1])
ax2.set_ylabel(feature_names[2])
ax2.set_title('Sepal Width vs Petal Length')

ax3=plt.subplot(gs[1,:])
ax3.scatter(data[:,2],data[:,3],c=target,cmap='viridis')
ax3.set_xlabel(feature_names[2])
ax3.set_ylabel(feature_names[3])
ax3.set_title('Petal Length vs Petal Width')

plt.tight_layout()				# 调整布局
plt.show()


# ### 图表元素的添加

# In[11]:


import matplotlib.pyplot as plt
import numpy as np
from sklearn.datasets import load_iris

# 载入Iris数据集
iris=load_iris()
data=iris.data
target=iris.target
feature_names=iris.feature_names
target_names=iris.target_names

fig,ax=plt.subplots(figsize=(6,4))		# 创建图形和子图
# 绘制散点图
for i in range(len(target_names)):
    ax.scatter(data[target==i,0],data[target==i,1],label=target_names[i])
ax.set_title('Sepal Length vs Sepal Width',fontsize=16)	# 添加标题
ax.legend(fontsize=12)						# 添加图例
ax.grid(True,linestyle='--',alpha=0.5)	# 添加网格线

# 自定义坐标轴标签
ax.set_xlabel(feature_names[0],fontsize=14)
ax.set_ylabel(feature_names[1],fontsize=14)
# 设置坐标轴刻度标签大小
ax.tick_params(axis='both',which='major',labelsize=12)

plt.tight_layout()							# 调整图形边界
plt.show()


# ### 极坐标示例

# In[12]:


import matplotlib.pyplot as plt
import numpy as np

# 创建一些示例数据
theta=np.linspace(0,2*np.pi,100)
r=np.abs(np.sin(theta))

plt.figure(figsize=(6,6))
ax=plt.subplot(111,projection='polar')			# 创建极坐标系图形
ax.plot(theta,r,color='blue',linewidth=2)		# 绘制极坐标系图形
ax.set_title('Polar Plot',fontsize=16)			# 添加标题
plt.show()										# 显示图形


# In[10]:


import numpy as np
import matplotlib.pyplot as plt

# 生成角度数据（0 到 2π）
theta = np.linspace(0, 2 * np.pi, 100)

# 生成对应的半径数据
r = np.abs(np.sin(2 * theta))

# 使用 plt.polar() 绘制极坐标图
plt.polar(theta, r, color='b', linewidth=2, label="r = |sin(2θ)|")

# 添加标题和图例
plt.title("Polar Plot Example")
plt.legend()

# 显示图像
plt.show()


# ## seaborn包

# ### 与matplotlib配合使用

# In[12]:


import seaborn as sns
import matplotlib.pyplot as plt

# 加载数据集
iris=sns.load_dataset("iris",data_home='seaborn-data',cache=True)
tips=sns.load_dataset("tips",data_home='seaborn-data',cache=True)
car_crashes=sns.load_dataset("car_crashes",data_home='seaborn-data',cache=True)
penguins=sns.load_dataset("penguins",data_home='seaborn-data',cache=True)
diamonds=sns.load_dataset("diamonds",data_home='seaborn-data',cache=True)

plt.figure(figsize=(15,8))				# 设置画布
# 第1幅图：iris数据集的散点图
plt.subplot(2,3,1)
sns.scatterplot(x="sepal_length",y="sepal_width",hue="species",
                   data=iris)
plt.title("Iris scatterplot")

# 第2幅图：tips 数据集的箱线图
plt.subplot(2,3,2)
tips=sns.load_dataset("tips",data_home='seaborn-data',cache=True)
sns.boxplot(x="day",y="total_bill",hue="smoker",data=tips)
plt.title("Tips boxplot")

# 第3幅图：tips 数据集的小提琴图
plt.subplot(2,3,3)
sns.violinplot(x="day",y="total_bill",hue="smoker",data=tips)
plt.title("Tips violinplot")

# 第4幅图：car_crashes 数据集的直方图
plt.subplot(2,3,4)
sns.histplot(car_crashes['total'],bins=20)
plt.title("Car Crashes histplot")

# 第5幅图：penguins 数据集的点图
plt.subplot(2,3,5)
sns.pointplot(x="island",y="bill_length_mm",hue="species",data=penguins)
plt.title("Penguins pointplot")

# 第6幅图：diamonds 数据集的计数图
plt.subplot(2,3,6)
sns.countplot(x="cut",data=diamonds)
plt.title("Diamonds countplot")

plt.tight_layout()
plt.show()


# ### 使用图表分面绘制多个散点图

# In[13]:


import seaborn as sns
import matplotlib.pyplot as plt

# import ssl
# ssl._create_default_https_context = ssl._create_unverified_context

iris=sns.load_dataset("iris")			# 加载iris数据集

# 创建 FacetGrid 对象，按照种类（'species'）进行分面
g=sns.FacetGrid(iris,col="species",margin_titles=True)
# 在每个子图中绘制花萼长度与花萼宽度的散点图
g.map(sns.scatterplot,"sepal_length","sepal_width")
g.set_axis_labels("Sepal Length","Sepal Width")		# 设置子图标题

plt.show()


# ## pyecharts包

# ### 简单柱状图

# In[9]:


from pyecharts.charts import Bar

bar = Bar()
bar.add_xaxis(["衬衫", "羊毛衫", "雪纺衫", "裤子", "高跟鞋", "袜子"])
bar.add_yaxis("商家A", [5, 20, 36, 10, 75, 90])
# render 会生成本地 HTML 文件，默认会在当前目录生成 render.html 文件
# 也可以传入路径参数，如 bar.render("mycharts.html")
bar.render()
# bar.render_notebook()


# ### 绘图流程展示

# In[10]:


from pyecharts import options as opts
from pyecharts.charts import Line

# 1. 选择图表类型 - 在这里选择的是折线图 (Line)
line = Line()

# 2. 声明图形类并添加数据
x_data = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
y_data = [820, 932, 901, 934, 1290, 1330, 1320]
line.add_xaxis(x_data)
line.add_yaxis("销量", y_data)

# 3. 选择全局变量 - 设置图表的一些全局配置项
line.set_global_opts(
    title_opts=opts.TitleOpts(title="一周销量数据", subtitle="2025年销售统计"),
    tooltip_opts=opts.TooltipOpts(trigger="axis"),
    xaxis_opts=opts.AxisOpts(name="星期"),
    yaxis_opts=opts.AxisOpts(name="销量"),
)

# 4. 系列配置项 - 设置具体数据系列的样式
line.set_series_opts(
    label_opts=opts.LabelOpts(is_show=True, position="top"),  # 显示每个数据点的标签
    linestyle_opts=opts.LineStyleOpts(width=2, color="blue", type_="solid"),  # 设置线条样式
)

# 5. 显示及保存图表 - 渲染图表并保存为HTML文件
line.render("sales_data_line_chart.html")  # 保存为HTML文件
# line.render_notebook() # 展示到该界面


# ### 普通方式配置

# In[3]:


from pyecharts import options as opts
from pyecharts.charts import Line

# 创建一个折线图实例
line = Line()

# 普通方式配置
line.add_xaxis(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'])
line.add_yaxis("销量", [820, 932, 901, 934, 1290, 1330, 1320])
line.set_global_opts(
    title_opts=opts.TitleOpts(title="一周销量数据", subtitle="2025年销售统计"),
    tooltip_opts=opts.TooltipOpts(trigger="axis"),
    xaxis_opts=opts.AxisOpts(name="星期"),
    yaxis_opts=opts.AxisOpts(name="销量"),
)
line.set_series_opts(
    label_opts=opts.LabelOpts(is_show=True, position="top"),
    linestyle_opts=opts.LineStyleOpts(width=2, color="blue", type_="solid"),
)

# 渲染图表
line.render("sales_data_line_chart_normal.html")


# ### 链式使用

# In[4]:


from pyecharts import options as opts
from pyecharts.charts import Line

# 创建一个折线图实例并使用链式调用
line = Line().add_xaxis(['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']) \
             .add_yaxis("销量", [820, 932, 901, 934, 1290, 1330, 1320]) \
             .set_global_opts(
                 title_opts=opts.TitleOpts(title="一周销量数据", subtitle="2025年销售统计"),
                 tooltip_opts=opts.TooltipOpts(trigger="axis"),
                 xaxis_opts=opts.AxisOpts(name="星期"),
                 yaxis_opts=opts.AxisOpts(name="销量"),
             ) \
             .set_series_opts(
                 label_opts=opts.LabelOpts(is_show=True, position="top"),
                 linestyle_opts=opts.LineStyleOpts(width=2, color="blue", type_="solid"),
             )

# 渲染图表
line.render("sales_data_line_chart_chain.html")


# ## plotly包

# In[5]:


import plotly.graph_objects as go
# 创建轨迹
trace = go.Scatter(x=[1, 2, 3], y=[4, 5, 6])

# 创建布局
layout = go.Layout(title='我的散点图', xaxis={'title': 'x轴'}, yaxis={'title': 'y轴'})

# 创建图表对象
fig = go.Figure(data=[trace], layout=layout)

# 显示图表
fig.show()


# In[6]:


import plotly.graph_objects as go

# 创建带有工具提示的散点图轨迹
trace1 = go.Scatter(x=[1, 2, 3, 4], y=[10, 11, 12, 13], text=['点1', '点2', '点3', '点4'], mode='markers')

# 创建图表对象并显示
fig = go.Figure(data=[trace1])
# fig.update_traces(hoverinfo='text+x+y')
fig.show()


# In[7]:


import plotly.graph_objects as go

# 创建带有工具提示的散点图轨迹
trace7 = go.Scatter(x=[1, 2, 3, 4], y=[10, 11, 12, 13], text=['点1', '点2', '点3', '点4'], mode='markers')

# 创建图表对象并显示
fig = go.Figure(data=[trace7])
fig.update_traces(hoverinfo='text+x+y')
fig.show()


# In[8]:


import plotly.express as px

# 创建散点图
fig = px.scatter(x=[1, 2, 3], y=[10, 11, 12], title='散点图')

# 显示图表
fig.show()


# ## dash包

# In[9]:


from dash.dependencies import Input, Output
import pandas as pd

# 创建一个示例数据集
df = pd.DataFrame({
    "Fruit": ["Apples", "Oranges", "Bananas", "Apples", "Oranges", "Bananas"],
    "Amount": [4, 1, 2, 2, 4, 5],
    "City": ["SF", "SF", "SF", "NYC", "NYC", "NYC"]
})

# 更新应用的布局，添加一个下拉菜单
app.layout = html.Div(children=[
    html.H1(children='Hello Dash'),

    html.Div(children='''
        Dash: A web application framework for Python.
    '''),

    # 添加了一个 dcc.Dropdown 组件，用户可以选择不同的城市
    dcc.Dropdown(
        id='city-dropdown',
        options=[
            {'label': 'San Francisco', 'value': 'SF'},
            {'label': 'New York City', 'value': 'NYC'}
        ],
        value='SF'
    ),

    dcc.Graph(
        id='example-graph',
    )
])

# 定义回调函数：使用 @app.callback 装饰器定义了一个回调函数 update_graph，当用户选择不同的城市时，图表会动态更新
@app.callback(
    Output('example-graph', 'figure'),
    [Input('city-dropdown', 'value')]
)
# 回调函数根据用户选择的城市过滤数据集，并更新图表
def update_graph(selected_city):
    filtered_df = df[df['City'] == selected_city]
    fig = px.bar(filtered_df, x="Fruit", y="Amount", color="City", barmode="group")
    return fig

# 运行应用
if __name__ == '__main__':
    app.run_server(debug=True)


# In[ ]:





# --- End of 6_demo_packages.py ---

# --- Start of 7_Categorical_Data.py ---
#!/usr/bin/env python
# coding: utf-8

# # Lecture 7：类别比较数据

# In[1]:


# 导入必备的包
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
import plotly.express as px
from pyecharts.charts import Bar, Line, Radar
from pyecharts import options as opts


# ## 柱状图

# ### 单一柱状图

# In[2]:


# 自定义数据集
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value': [10, 15, 7, 12, 8]})  # 创建一个包含类别和值的DataFrame
# 查看数据结构
data


# In[3]:


# 利用matplotlib创建单一柱状图

plt.figure(figsize=(6, 4))  # 创建图形对象，并设置图形大小
# bar函数绘制柱状图
plt.bar(data['category'], data['value'], color='steelblue')
# 绘制柱状图，指定x轴为类别，y轴为值，柱状颜色为钢蓝色
plt.xlabel('Category')  # 设置x轴标签
plt.ylabel('Value')  # 设置y轴标签
plt.title('Single Bar Chart')  # 设置图表标题

# 添加网格线，采用虚线，设置为灰色，透明度为0.5
plt.grid(linestyle='-', color='gray', alpha=0.5)
plt.show()


# In[8]:


# 使用 Seaborn 画柱状图

# 创建图形对象，并设置大小
plt.figure(figsize=(6, 4))

# 设置 Seaborn 样式
# sns.set_style("whitegrid")

# 利用barplot绘制
sns.barplot(x='category', y='value', data=data, color='steelblue')

# 设置标题和坐标轴标签
plt.xlabel('Category')
plt.ylabel('Value')
plt.title('Single Bar Chart')

# 显示图表
plt.show()


# In[5]:


# 利用pyecharts绘制

# 创建柱状图对象
bar = (
    Bar()
    .add_xaxis(data["category"].tolist())  # 设置X轴数据
    .add_yaxis("Value", data["value"].tolist(), color="steelblue")  # 设置Y轴数据，并指定颜色
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Single Bar Chart"),  # 设置标题
        xaxis_opts=opts.AxisOpts(name="Category"),  # 设置X轴标签
        yaxis_opts=opts.AxisOpts(name="Value"),  # 设置Y轴标签
        toolbox_opts=opts.ToolboxOpts(),  # 添加工具栏
        legend_opts=opts.LegendOpts(is_show=False),  # 隐藏图例
    )
)

# 渲染图表
# bar.render_notebook()  # 在Jupyter Notebook中渲染，如果由于版本兼容问题出现问题，可以采用下述代码保存到html中
bar.render("basic_bar_chart.html") # 生成HTML文件


# In[6]:


# 利用plotly创建柱状图

fig = px.bar(
    data, 
    x="category", 
    y="value", 
    title="Single Bar Chart", 
    labels={"category": "Category", "value": "Value"}, 
    color_discrete_sequence=["steelblue"]  # 设置柱状颜色
)

# 显示图表
fig.show()


# ### 分组柱状图
# 创建包含5个类别和4个对应的数值列的分组柱状图

# In[13]:


# 自定义一个包含多列数据的数据框DataFrame，包含类别和多列值
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value1': [10, 15, 7, 12, 8], 'value2': [6, 9, 5, 8, 4],
                     'value3': [3, 5, 2, 4, 6], 'value4': [9, 6, 8, 3, 5]})
# 查看数据框
data


# In[14]:


# 使用DataFrame的plot方法绘制分组柱状图
'''
方法说明:
dataframe.plot(x, kind, figsize)
    x: 标签
    kind: 绘制的图表类型 (e.g., bar)
'''
data.plot(x='category', kind='bar', figsize=(6, 4))

# 指定x轴为'category'列，图表类型为'bar'，图形大小为(6,4)
plt.xlabel('Category')  # 设置x轴标签
plt.xticks(rotation=0)  # 旋转x轴文本，使其水平显示
plt.ylabel('Value')  # 设置y轴标签
plt.title('Grouped Bar Chart')  # 设置图表标题
plt.legend(title='Values')  # 添加图例，并设置标题为'Values'
plt.show()



# In[9]:


# 数据转换（将宽数据变成长数据，以便 Seaborn 处理）
'''
方法说明：
pandas.melt() 函数可以实现将 “宽数据” → “长数据”的一种列转行变换
这一种格式其中一个或多个列是标识符变量（id_vars），而所有其他列，被视为测量变量（value_vars），被“解开”到行轴，只留下两个非标识符列，'variable'和'value'。
    # id_vars -- 不需要被转换的列名
    # var_name、value_name -- 自定义设置对应的列名
'''
data_melted = data.melt(id_vars="category", var_name="Group", value_name="Value")

data_melted


# In[10]:


# 使用seaborn的barplot方法绘制分组柱状图

# 设置 Seaborn 样式
sns.set_style("whitegrid")

# 创建图形对象
plt.figure(figsize=(6, 4))

# 使用 Seaborn 绘制分组柱状图
'''

'''
sns.barplot(x="category", y="Value", hue="Group", data=data_melted)

# 设置标题和坐标轴标签
plt.xlabel("Category")
plt.ylabel("Value")
plt.title("Grouped Bar Chart")

# 旋转 x 轴标签
plt.xticks(rotation=0)

# 显示图例
plt.legend(title="Group")

# 显示图表
plt.show()


# In[11]:


# 利用pyecharts的Bar绘制分组柱状图

# 提取类别和数据
categories = data["category"].tolist()
values = [data[col].tolist() for col in data.columns[1:]]  # 依次提取 value1 - value4

# 创建 Pyecharts 柱状图
bar = (
    Bar()
    .add_xaxis(categories)
    .add_yaxis("value1", values[0])
    .add_yaxis("value2", values[1])
    .add_yaxis("value3", values[2])
    .add_yaxis("value4", values[3])
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Grouped Bar Chart with Pyecharts"),
        xaxis_opts=opts.AxisOpts(name="Category"),
        yaxis_opts=opts.AxisOpts(name="Value"),
    )
)

# 渲染图表
# bar.render_notebook()  # 在Jupyter Notebook中渲染，如果由于版本兼容问题出现问题，可以采用下述代码保存到html中
bar.render("grouped_bar_chart.html") # 生成HTML文件


# In[12]:


# 利用plotly.express的bar绘制分组柱状图

fig = px.bar(
    data,
    x="category",
    y=["value1", "value2", "value3","value4"],
    barmode="group",  # 关键参数：分组模式
    title="Grouped Bar Chart with plotly",
)

# 显示图表
fig.show()


# ### 堆积柱状图
# 创建包含5个类别和4个对应的数值列的堆积柱状图

# In[15]:


# 自定义一个包含多列数据的数据框DataFrame，包含类别和多列值
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value1': [10, 15, 7, 12, 8], 'value2': [6, 9, 5, 8, 4],
                     'value3': [3, 5, 2, 4, 6], 'value4': [9, 6, 8, 3, 5]})
# 查看数据框
data


# In[16]:


# 将'category'列设置为索引，并创建堆积柱状图

# 使用DataFrame的plot方法绘制堆积柱状图
data.set_index('category').plot(kind='bar', stacked=True, figsize=(6, 4))
# 设置索引为'category'列，图表类型为'bar'，堆积模式为True，图形大小为(6,4)

plt.xlabel('Category')  # 设置x轴标签
plt.ylabel('Value')  # 设置y轴标签
plt.title('Stacked Bar Chart')  # 设置图表标题
plt.xticks(rotation=0)  # 旋转x轴文本，使其水平显示

# 添加图例，并设置标题为'Values'，并放置在图的右侧
plt.legend(title='Values', loc='center left', bbox_to_anchor=(1, 0.5))
plt.show()


# In[18]:


# 利用seaborn绘制（错误代码）——柱子会重叠而非堆叠
# 将数据转换为长格式
df_melted = data.melt(id_vars='category', var_name='Values', value_name='Value')
# print(df_melted)
# 使用seaborn设置样式
sns.set(style="whitegrid")

# 创建堆积柱状图
plt.figure(figsize=(6, 4))
sns.barplot(data=df_melted, x='category', y='Value', hue='Values', dodge=False)

plt.xlabel('Category')
plt.ylabel('Value')
plt.title('Stacked Bar Chart with Seaborn')
plt.legend(title='Values', bbox_to_anchor=(1.05, 1), loc='upper left')

plt.tight_layout()
plt.show()


# In[21]:


# 使用pyecharts进行绘制
# 创建柱状图
bar = Bar()

# 添加x轴数据
bar.add_xaxis(categories)

# 添加系列数据，并设置stack参数为相同的值以实现堆叠
bar.add_yaxis("Value1", data['value1'].tolist(), stack="stack1")
bar.add_yaxis("Value2", data['value2'].tolist(), stack="stack1")
bar.add_yaxis("Value3", data['value3'].tolist(), stack="stack1")
bar.add_yaxis("Value4", data['value4'].tolist(), stack="stack1")

# 设置全局配置
bar.set_global_opts(
    title_opts=opts.TitleOpts(title="Stacked Bar Chart with Pyecharts"),
    xaxis_opts=opts.AxisOpts(name="Category"),
    yaxis_opts=opts.AxisOpts(name="Value"),
    legend_opts=opts.LegendOpts(pos_right="right")
)

# 渲染图表
# bar.render_notebook()  # 在Jupyter Notebook中渲染，如果由于版本兼容问题出现问题，可以采用下述代码保存到html中
bar.render("stacked_bar_chart.html") # 生成HTML文件


# In[22]:


# 利用plotly.express的bar绘制堆叠柱状图

fig = px.bar(
    data,
    x="category",
    y=["value1", "value2", "value3","value4"],
    barmode="stack",  # 关键参数：堆叠模式
    title="Stacked Bar Chart with plotly",
)

# 显示图表
fig.show()


# ### 百分比柱状图
# 创建包含5个类别和4个对应的数值列的百分比柱状图

# In[23]:


# 自定义一个包含多列数据的数据框DataFrame，包含类别和多列值
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value1': [10.0, 15.0, 7.0, 12.0, 8.0], 'value2': [6.0, 9.0, 5.0, 8.0, 4.0],
                     'value3': [3.0, 5.0, 2.0, 4.0, 6.0], 'value4': [9.0, 6.0, 8.0, 3.0, 5.0]})
# 查看数据框
data


# In[24]:


# 续上例，创建百分比柱状状图（对数据进行预处理）
# 复制数据集到新的DataFrame以便进行百分比计算
data_percentage = data.copy()

data_percentage.iloc[:, 1:] = data_percentage.iloc[:, 1:].astype(float)

# 计算每个数值列的百分比，除以每行的总和并乘以100
data_percentage.iloc[:, 1:] = data_percentage.iloc[:, 1:].div(
    data_percentage.iloc[:, 1:].sum(axis=1), axis=0) * 100

# print(data_percentage)
data_percentage


# In[ ]:


# 随堂练习


# ### 均值柱状图
# 创建包含5个类别和4个对应的数值列的均值柱状图。

# In[25]:


# 创建创建一个包含类别、值和标准差的DataFrame数据集
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value': [10, 15, 7, 12, 8], 'std': [1, 2, 1.5, 1.2, 2.5]})

# 计算每个类别的均值和标准差
mean_values = data['value']
std_values = data['std']


# In[26]:


# import matplotlib.cm as cm
# 生成颜色
# colors = [cm.tab10(i / len(data['category'])) for i in range(len(data['category']))]

# 创建均值柱状图
plt.figure(figsize=(6, 4))  # 设置图形大小
bars = plt.bar(data['category'], mean_values)

# 添加误差线
for bar, std in zip(bars, std_values): # 让我们可以遍历每个柱子及其对应的标准差
    plt.errorbar(bar.get_x() + bar.get_width() / 2, # 计算误差线的X轴位置
                 bar.get_height(), # 误差线的Y轴位置，设置在柱子顶部（即柱子的高度）
                 yerr=std, fmt='none', color='black', ecolor='gray', # 误差棒的长度（误差值）
                 capsize=5, capthick=2)  # 误差线样式

# 添加标题和标签
plt.xlabel('Category')  # x轴标签
plt.ylabel('Mean Value')  # y轴标签
plt.title('Mean Bar Chart with Error Bars')  # 图表标题

# 设置网格线
plt.grid(axis='both', linestyle='-', color='gray', alpha=0.5)

# 显示图表
plt.show()


# In[13]:


# 使用 seaborn 绘制带误差棒的柱状图

plt.figure(figsize=(6, 4))
ax = sns.barplot(data=data, x="category", y="value")  # 先画柱状图，不让 seaborn 自动计算误差棒

# 获取每个柱子的 x 轴位置
x_positions = range(len(data["category"]))

# 使用 matplotlib 添加误差棒
plt.errorbar(x_positions, data["value"], yerr=data["std"], fmt='none', ecolor='gray', capsize=5)

# 添加标题和标签
plt.xlabel('Category')
plt.ylabel('Mean Value')
plt.title('Mean Bar Chart with Error Bars')

# 显示图表
plt.show()


# In[27]:


# pyecharts 本身不直接支持误差棒（error bars），但我们可以通过叠加 line 图表来模拟误差棒。
# 效果并不理想

# 计算误差棒的上下界
upper = [v + s for v, s in zip(mean_values, std_values)]
lower = [v - s for v, s in zip(mean_values, std_values)]

# 创建柱状图
bar = (
    Bar()
    .add_xaxis(list(data['category']))
    .add_yaxis("Mean Value", list(mean_values), color="blue")
    .set_global_opts(title_opts=opts.TitleOpts(title="Bar Chart with Error Bars"))
)

# 创建误差棒（用 Line 模拟）
error_bar = (
    Line()
    .add_xaxis(data['category'])
    .add_yaxis("Upper Bound", upper, symbol="circle", linestyle_opts=opts.LineStyleOpts(width=1, type_="dashed"))
    .add_yaxis("Lower Bound", lower, symbol="circle", linestyle_opts=opts.LineStyleOpts(width=1, type_="dashed"))
)

# 叠加误差棒到柱状图
bar.overlap(error_bar)
# bar.render_notebook()  # 在 Jupyter Notebook 里渲染 (如果由于版本问题无法渲染，可以保存到html中）
bar.render('pyecharts_errorbar.html')  # 在 Jupyter Notebook 里渲染


# In[28]:


# plotly 原生支持误差棒，我们只需指定 error_y 参数。
# 绘制带误差棒的柱状图
fig = px.bar(data, x="category", y="value", error_y="std",
             title="Bar Chart with Error Bars",
             labels={"value": "Mean Value", "category": "Category"},
             color_discrete_sequence=["blue"])  # 颜色设置

# 显示图表
fig.show()


# ### 不等宽柱状图
# 创建包含5个类别和5个对应的值及宽度值的不等宽柱状图

# In[77]:


# 创建数据集
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value': [10, 15, 7, 12, 8],
                     'width': [0.8, 0.4, 1.0, 0.5, 0.9]})
print("数据结构：")
print(data) 


# In[78]:


# 自定义颜色列表，每个柱子使用不同的配色
colors = ['red', 'green', 'blue', 'orange', 'purple']
# 创建不等宽柱状图
plt.figure(figsize=(6, 4))
for i in range(len(data)): # 遍历五个类别
    plt.bar(data['category'][i], data['value'][i],
            width=data['width'][i], color=colors[i])

# 添加标题和标签
plt.xlabel('Category')
plt.ylabel('Value')
plt.title('Unequal Width Bar Chart')

# 设置网格线
plt.grid(axis='both', linestyle='-', color='gray', alpha=0.5)
plt.show()


# #### seaborn 需要用 matplotlib 叠加
# #### pyecharts没有直接修改宽度的方法
# #### plotly需要用基本的plotly.graph_objects方法

# ### 有序柱状图
# 创建有序柱状图

# In[2]:


import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns


# In[5]:


# 自定义一个包含多列数据的数据框DataFrame，包含类别和多列值
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value': [10.0, 15.0, 7.0, 12.0, 8.0]})
# 查看数据框
data


# In[3]:


# 按 value 排序
data_sorted = data.sort_values(by='value', ascending=True)

# 添加颜色列（红色表示负数，蓝色正数）
data_sorted['color'] = data_sorted['value'].apply(lambda x: 'red' if x < 0 else 'blue')

# 绘图
plt.figure(figsize=(6, 4))
sns.barplot(y='category', x='value', data=data_sorted, palette=data_sorted['color'].tolist())
plt.axvline(x=0, color='black', linewidth=1)  # 中心线
plt.xlabel('Value')
plt.ylabel('Category')
plt.title('Bar Chart Sorted by Value')
plt.tight_layout()
plt.show()


# ### 条形图
# 使用水平或垂直的矩形条（条形）来表示数据

# In[29]:


# 自定义数据集
data = pd.DataFrame({'category': ["A", "B", "C", "D", "E"],
                     'value': [10, 15, 7, 12, 8]})  # 创建一个包含类别和值的DataFrame
# 查看数据结构
data


# In[30]:


# 利用matplotlib创建条形图

plt.figure(figsize=(6, 4))  # 创建图形对象，并设置图形大小
# bar函数绘制柱状图
plt.barh(data['category'], data['value'], color='steelblue')
# 绘制柱状图，指定x轴为类别，y轴为值，柱状颜色为钢蓝色
plt.xlabel('Category')  # 设置x轴标签
plt.ylabel('Value')  # 设置y轴标签
plt.title('Single Bar Chart')  # 设置图表标题

# 添加网格线，采用虚线，设置为灰色，透明度为0.5
plt.grid(linestyle='-', color='gray', alpha=0.5)
plt.show()


# In[31]:


# 使用 Seaborn 画柱状图

# 创建图形对象，并设置大小
plt.figure(figsize=(6, 4))

# 设置 Seaborn 样式
# sns.set_style("whitegrid")

# 利用barplot绘制
sns.barplot(y='category', x='value', data=data, color='steelblue')

# 设置标题和坐标轴标签
plt.xlabel('Value')
plt.ylabel('Category')
plt.title('Single Bar Chart')

# 显示图表
plt.show()


# In[32]:


# 利用pyecharts绘制

bar = (
    Bar()
    .add_xaxis(data["category"].tolist())  # 设置X轴数据
    .add_yaxis("Value", data["value"].tolist(), color="steelblue")  # 设置Y轴数据，并指定颜色
    .reversal_axis() # 关键：转换为横向条形图
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Single Bar Chart"),  # 设置标题
        xaxis_opts=opts.AxisOpts(name="Category"),  # 设置X轴标签
        yaxis_opts=opts.AxisOpts(name="Value"),  # 设置Y轴标签
        toolbox_opts=opts.ToolboxOpts(),  # 添加工具栏
        legend_opts=opts.LegendOpts(is_show=False),  # 隐藏图例
    )
)

# 渲染图表
# bar.render_notebook()  # 在Jupyter Notebook中渲染
bar.render("bar_chart.html") # 生成HTML文件


# In[33]:


# 利用plotly创建柱状图

fig = px.bar(
    data, 
    y="category", 
    x="value", 
    title="Single Bar Chart", 
    labels={"category": "Category", "value": "Value"}, 
    color_discrete_sequence=["steelblue"]  # 设置柱状颜色
)

# 显示图表
fig.show()


# #### 发散条形图
# 创建发散条形图数据
# 其中条形的颜色根据数据的标准化值而变化，正值使用绿色，负值使用红色。

# In[37]:


# 创建数据
data = pd.DataFrame({
    'category': ["A", "B", "C", "D", "E"],
    'value': [10, -15, 7, -12, 8]  # 既有正值也有负值
})
data


# In[38]:


# 设置颜色：正值用蓝色，负值用红色
colors = ['steelblue' if v >= 0 else 'red' for v in data['value']]

# 创建横向发散条形图
plt.figure(figsize=(6, 4))
plt.barh(data['category'], data['value'], color=colors)

# 添加网格线
plt.axvline(x=0, color='black', linewidth=1)  # 竖直中心线
plt.grid(axis='x', linestyle='--', alpha=0.5)

# 添加标签和标题
plt.xlabel('Value')
plt.ylabel('Category')
plt.title('Diverging Bar Chart')

# 显示图表
plt.show()


# In[44]:


# 使用seaborn绘制
# 添加颜色列
data['color'] = data['value'].apply(lambda x: 'blue' if x >= 0 else 'red')

# 创建图形对象
plt.figure(figsize=(6, 4))

# 使用 seaborn.barplot 绘制发散条形图
sns.barplot(y='category', x='value', data=data, palette=data['color'].tolist())


# 添加竖直中心线
plt.axvline(x=0, color='black', linewidth=1)

# 添加标签和标题
plt.xlabel('Value')
plt.ylabel('Category')
plt.title('Diverging Bar Chart')

# 显示图表
plt.show()


# In[40]:


# 使用pyecharts绘制

# 创建横向发散条形图
bar = (
    Bar()
    .add_xaxis(data["category"].tolist())  
    .add_yaxis("Value", data["value"].tolist(), color="auto")  # 自动匹配颜色
    .reversal_axis()  # 横向条形图
    .set_global_opts(
        title_opts=opts.TitleOpts(title="Diverging Bar Chart"),
        xaxis_opts=opts.AxisOpts(name="Value"),
        yaxis_opts=opts.AxisOpts(name="Category"),
    )
)

# 渲染图表
# bar.render_notebook()  # 或者 bar.render("diverging_bar_chart.html")
bar.render("diverging_bar_chart.html")


# In[41]:


# 使用plotly.express绘制

# 创建发散条形图
fig = px.bar(
    data, 
    x='value', 
    y='category', 
    title='Diverging Bar Chart',
    labels={'category': 'Category', 'value': 'Value'},
    color='value',  # 按值变化颜色
    color_continuous_scale=['red', 'steelblue']  # 颜色渐变
)

# 显示图表
fig.show()


# ### 雷达图

# In[34]:


# 设置数据
df = pd.DataFrame({'group': ['A', 'B', 'C', 'D', 'E'],  # 五组数据
                   'var1': [38, 1.5, 30, 4, 29], 'var2': [29, 10, 9, 34, 18],
                   'var3': [8, 39, 23, 24, 19], 'var4': [7, 31, 33, 14, 33],
                   'var5': [28, 15, 32, 14, 22]})  # 每组数据的变量
print(df)


# In[37]:


# 利用matplotlib绘制

# 获取变量列表（除了group以外的列名）
categories = list(df.columns[1:])
N = len(categories)

# 通过复制第1个值来闭合雷达图
# 绘制A的雷达图

# df.loc[0]获取第一行数据，然后去掉第一列名（group）
# .values.flatten().tolist()：将 Series 转换为一个一维数组，然后转化为列表 values
values = df.loc[0].drop('group').values.flatten().tolist()
# 为了使雷达图闭合，需要将第一个值再次添加到列表末尾，这样绘制的图形才会形成闭环。
values += values[:1] # 38 29 8 7 28 38

# 计算每个变量的角度
# angles 列表存储每个变量对应的角度。由于雷达图是圆形的，角度从 0 到 2π 之间分布
# 根据变量数量 N 计算每个变量在圆上的角度。n 是当前变量的索引，N 是总的变量数量，2 * np.pi 是完整圆的角度。
# 为了闭合雷达图，添加第一个角度值到列表末尾，这样图形在绘制时能够回到起点。
angles = [n / float(N) * 2 * np.pi for n in range(N)]
angles += angles[:1]

# 初始化雷达图
# 创建一个 matplotlib 图形对象 fig 和坐标轴对象 ax，并设置图形大小为 4x4 英寸。
# 指定 subplot 为极坐标系统，这使得坐标轴可以表示为极坐标形式（即圆形坐标系），适合绘制雷达图
fig, ax = plt.subplots(figsize=(4, 4), subplot_kw=dict(polar=True))
# 绘制每个变量的轴，并添加标签
# 将每个角度值对应到类别（变量名）上，angles[:-1] 去掉了最后一个角度（因为雷达图已经闭合），categories 是变量名列表。
plt.xticks(angles[:-1], categories, color='grey', size=8)

# 添加y轴标签
# 设置 y 轴标签的位置，0 表示标签从图的顶部开始
ax.set_rlabel_position(0)
# 设置 y 轴的刻度值（10, 20, 30）以及对应的标签。
plt.yticks([10, 20, 30], ["10", "20", "30"], color="grey", size=7)
# 设置 y 轴的范围，即雷达图的值范围在 0 到 40 之间
plt.ylim(0, 40)

# 关键代码
# 绘制数据：绘制雷达图的边框，即每个变量的值在极坐标系统中的连线。
ax.plot(angles, values, linewidth=1, linestyle='solid')  
# 填充区域：在雷达图的区域内填充颜色，'b' 表示蓝色，alpha=0.1 设置透明度为 0.1（即图形的填充颜色比较浅）
ax.fill(angles, values, 'b', alpha=0.1)  
plt.show()


# In[41]:


# 利用pyecharts绘制
# 提取A组数据
a_data = df[df['group'] == 'A'].iloc[:, 1:].values.tolist()[0]
print(a_data)

# 绘制雷达图
radar = (
    Radar()
    .add_schema(schema=[
        {"name": "var1", "max": 40},
        {"name": "var2", "max": 40},
        {"name": "var3", "max": 40},
        {"name": "var4", "max": 40},
        {"name": "var5", "max": 40},
    ]) # 雷达图的维度（变量名）和最大值（用于标准化）
    .add("A组数据", [a_data], color="#FF4500", areastyle_opts=opts.AreaStyleOpts(opacity=0.3))
    .set_series_opts(label_opts=opts.LabelOpts(is_show=True)) # # 显示数值标签
    .set_global_opts(title_opts=opts.TitleOpts(title="A组数据雷达图"))
)

radar.render("radar_chart_A.html")


# In[113]:


# 使用plotly实现
# 提取类别和数据
categories = list(df.columns[1:])
values = df.loc[0, categories].tolist() 

# 构建长格式数据以适配 plotly.express
# 'category': 将 categories 列表作为一列数据，表示雷达图的每个维度。
# 'value': 将之前提取的 values 列表作为数据列，表示每个维度的数值。
# 'group': 为所有数据指定一个组，通常在这里是 'A'，表明所有数据来自同一组。这个字段用于区分不同的系列数据，尽管在这里只有一个系列（group 始终为 'A'）。
long_df = pd.DataFrame({
    'category': categories,
    'value': values,
    'group': ['A'] * len(categories)
})
print(long_df)
# 绘制雷达图
fig = px.line_polar(long_df, # 传入之前构建的长格式数据框
                    r='value', # 指定每个维度的数值（即数据）在 r 轴上
                    theta='category', # 指定每个维度（即类别）在 theta（角度）轴上的位置
                    line_close=True, # 过将 line_close 设置为 True，确保雷达图的边缘闭合，即从最后一个点连接回第一个点，形成封闭的图形
                    title="Radar Chart with Plotly Express", 
                    color='group', # 使用 group 列来为不同的系列着色。尽管这里只有一个系列（A），这个参数还是用于确定分组颜色
                    markers=True # 在每个数据点上添加标记，便于查看数据点。
                   )

# 显示图表
fig.show()


# In[ ]:


# 随堂练习
# 绘制六边形战士！


# ### 径向柱状图

# In[47]:


# 创建数据集，第一列为数据集各项的名称。第二列各项的数值
df = pd.DataFrame(
    {'Name': ['Ding ' + str(i) for i in list(range(1, 51))],
     'Value': np.random.randint(low=10, high=100, size=50)}
)
df.head(3)  # 显示前 3 行数据，输出略


# In[118]:


# 使用matplotlib绘制
plt.figure(figsize=(20, 10))  # 设置图形大小
ax = plt.subplot(111, polar=True)  # 绘制极坐标轴
plt.axis('off')  # 移除网格线

upperLimit = 100  # 设置坐标轴的上限
lowerLimit = 30  # 设置坐标轴的下限
max_value = df['Value'].max()  # 计算数据集中的最大值

# 计算每个条形图的高度，它们是在新坐标系中将每个条目值转换的结果
# 数据集中的0转换为lowerLimit(30)，最大值被转换为upperLimit(100)
slope = (max_value - lowerLimit) / max_value
heights = slope * df.Value + lowerLimit

width = 2 * np.pi / len(df.index)  # 计算每个条形图的宽度，共有2*Pi=360°

# 计算每个条形图中心的角度：
indexes = list(range(1, len(df.index) + 1))
angles = [element * width for element in indexes]

# 绘制条形图
bars = ax.bar(x=angles, height=heights, width=width,
              bottom=lowerLimit, linewidth=2, edgecolor="white")


plt.show()


# In[49]:


# 利用plotly绘制
# 参数设置
upperLimit = 100
lowerLimit = 30
max_value = df['Value'].max()
slope = (upperLimit - lowerLimit) / max_value
df["ScaledValue"] = slope * df['Value'] + lowerLimit
df["Angle"] = np.linspace(0, 360, len(df), endpoint=False)  # 均匀分布角度

# 创建径向柱状图
fig = px.bar_polar(
    df,
    r="ScaledValue",
    theta="Angle",
    color="Name",
    template="plotly_dark",
    color_discrete_sequence=px.colors.qualitative.Pastel,
    range_r=[lowerLimit, upperLimit],
    direction="clockwise",
    start_angle=0
)

# 调整布局
# fig.update_layout(
#     title="径向柱状图 (Plotly)",
#     polar=dict(
#         radialaxis=dict(visible=True, range=[lowerLimit, upperLimit]),
#         angularaxis=dict(showticklabels=True, ticks="outside")
#     ),
#     showlegend=True
# )

fig.show()  # 显示图表（Jupyter/浏览器）
# fig.write_html("radial_bar_plotly.html")  # 保存为HTML


# In[ ]:





# --- End of 7_Categorical_Data.py ---

# --- Start of 8_Exercise.py ---
#!/usr/bin/env python
# coding: utf-8

# # Lecture 8: Exercise

# In[1]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Scatter
import random
import plotly.express as px


# ### 随堂练习(1)
# 生成一个数据框，包含行名为length、列名为width的100个数据点，其中每个数值为500～1000之间整数
# 
# 根据上述数据框绘制散点图
# 
# 扩充数据点为10000个，观察是否需要改为六边形图？
# 

# In[2]:


data = {
    'length': np.random.randint(500, 1001, size=100),
    'width': np.random.randint(500, 1001, size=100)
}
df = pd.DataFrame(data)
df.head()


# In[3]:


plt.figure(figsize=(8, 6))
sns.scatterplot(data=df, x='length', y='width', alpha=0.7)
plt.title('Scatter Plot of Length vs Width (100 Points)')
plt.show()


# In[4]:


# 生成10,000个数据点
large_data = {
    'length': np.random.randint(500, 1001, size=10000),
    'width': np.random.randint(500, 1001, size=10000)
}
large_df = pd.DataFrame(large_data)
large_df


# In[5]:


plt.figure(figsize=(8, 6))
sns.scatterplot(data=large_df, x='length', y='width', alpha=0.5)
plt.title('Scatter Plot of Length vs Width (10,000 Points)')
plt.show()


# In[5]:


plt.hexbin(large_df['length'],large_df['width'], gridsize=30, cmap='Blues')
plt.show()


# In[6]:


plt.figure(figsize=(10, 8))
sns.jointplot(data=large_df, x='length', y='width', kind='hex', color='blue')
plt.suptitle('Hexbin Plot of Length vs Width (10,000 Points)', y=1.02)
plt.show()


# ### 随堂练习(2)
# 
# 生成一个数据框，包含行名为length、列名为width的100个数据点，其中每个数值为500～1000之间整数，并为这100个数据点新增一个参考大小的值
# 
# 根据上述数据框绘制气泡图
# 
# 

# In[8]:


data = {
    'length': np.random.randint(500, 1001, size=100),
    'width': np.random.randint(500, 1001, size=100)
}
df = pd.DataFrame(data)
df['ratio']=df['length']/df['width']
df.head()


# In[9]:


# 创建气泡图
plt.figure(figsize=(8, 6))

plt.scatter(df['length'], df['width'], s=df['ratio'], alpha=0.5, c='skyblue', edgecolors='black')

# 添加标签
plt.xlabel("X-axis")
plt.ylabel("Y-axis")
plt.title("Example Bubble Chart")
plt.grid(True)
plt.show()


# In[10]:


# 气泡大小不明显

df['ratio'] = df['ratio']*100
plt.figure(figsize=(8, 6))

plt.scatter(df['length'], df['width'], s=df['ratio'], alpha=0.5, c='skyblue', edgecolors='black')

# 添加标签
plt.xlabel("X-axis")
plt.ylabel("Y-axis")
plt.title("Example Bubble Chart")
plt.grid(True)
plt.show()


# ### 随堂练习(3)
# 读取文件steam_game_data.csv
# 
# 
# 
利用气泡图可视化——x轴为价格，y轴为好评率，大小由ranking控制，排名越高气泡越大
# 

# In[11]:


# 读取数据
df = pd.read_csv('steam_game_data.csv')

# 数据预处理
def clean_price(price):
    if price == 'Free To Play':
        return 0
    elif isinstance(price, str) and '$' in price:
        return float(price.replace('$', ''))
    else:
        return float(price)  # 如果已经是数字则直接转换

# 定义转换函数：移除 % 并除以 100 → 转换为 0.9（浮点数）
def percent_to_float(user_review_percentage):
    return float(user_review_percentage.replace('%',''))

df['price'] = df['price'].apply(clean_price)
df_new = df[~df['user_review_percentage'].isna()]
df_new['user_review_percentage'] = df_new['user_review_percentage'].apply(percent_to_float)

# 检查
print(df_new)


# In[12]:


fig = px.scatter(df_new, x='price', y='user_review_percentage', 
                 size='rank', 
                 title='Plotly Scatter Plot',
                 labels={'price': 'price', 'user_review_percentage': 'user review'},
                 hover_data=['rank'])

# Update layout
fig.update_layout(
    yaxis_range=[0, 100],  # 强制y轴从0%到100%
    plot_bgcolor='white',
    xaxis_title='价格 (USD)',
    yaxis_title='好评率 (%)'
)

# Show the plot
fig.show()


# In[42]:


plt.figure(figsize=(8, 6))

# 传递x值和y值
# x='x', y='y' - 设置x轴和y轴所对应的数据列名
# size='size' - 指定用于控制点大小的数据列
sns.scatterplot(data=df_new, x='price', y='user_review_percentage', size='rank')

# 增加x轴和y轴标签，添加标题
plt.xlabel('X-axis')
plt.ylabel('Y-axis')
plt.title('Seaborn Scatter Plot')

plt.show()


# In[17]:


# 但是ranking和气泡大小是相反的，因此要做处理
df_new['new_rank'] = 20 - df_new['rank']
# df_new.loc[:, 'new_rank'] = 20 - df_new['rank'] # 该代码可以消除警告
df_new


# In[16]:


fig = px.scatter(df_new, x='price', y='user_review_percentage', 
                 size='new_rank', 
                 title='Plotly Scatter Plot',
                 labels={'price': 'price', 'user_review_percentage': 'user review'},
                 hover_data=['rank'])

# Update layout
fig.update_layout(
    yaxis_range=[0, 100],  # 强制y轴从0%到100%
    plot_bgcolor='white',
    xaxis_title='价格 (USD)',
    yaxis_title='好评率 (%)'
)

# Show the plot
fig.show()


# In[ ]:





# --- End of 8_Exercise.py ---

# --- Start of 8_Numerical_Data.py ---
#!/usr/bin/env python
# coding: utf-8

# # Lecture 8: 数值关系数据可视化

# In[2]:


# 导入必备的包
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd
from pyecharts import options as opts
from pyecharts.charts import Scatter
import random
import plotly.express as px


# ## 散点图

# ### 基本散点图

# In[5]:


# 利用matplotlib绘制

# 生成随机样本
x = np.random.rand(50)
y = np.random.rand(50)

plt.figure(figsize=(8, 6))

# 传递x值和y值
plt.scatter(x, y, c='blue', alpha=0.6, s=100)

# 增加x轴和y轴标签，添加标题
plt.xlabel('X-axis')
plt.ylabel('Y-axis')
plt.title('Matplotlib Scatter Plot')

# 显示网格线
plt.grid(True)
plt.show()


# In[15]:


# 生成随机样本
data = pd.DataFrame({
    'x': np.random.rand(50),
    'y': np.random.rand(50)
})

plt.figure(figsize=(8, 6))

# 传递x值和y值
# x='x', y='y' - 设置x轴和y轴所对应的数据列名
sns.scatterplot(data=data, x='x', y='y')

# 增加x轴和y轴标签，添加标题
plt.xlabel('X-axis')
plt.ylabel('Y-axis')
plt.title('Seaborn Scatter Plot')

plt.show()


# In[36]:


# 创建两个包含50个随机数的列表
x_data = [random.random() for _ in range(50)]
y_data = [random.random() for _ in range(50)]

# 创建散点图
scatter = (
    Scatter()                               # 初始化散点图对象
    .add_xaxis(xaxis_data=x_data)           # 添加x轴数据
    .add_yaxis(                             # 添加y轴数据和相关配置
        series_name="Scatter Data",         # 数据系列名称，会显示在图例中
        y_axis=y_data,                      # y轴数据
        symbol_size=10,                     # 散点大小设置为10像素
        label_opts=opts.LabelOpts(is_show=False),  # 不显示数据标签
    )
    .set_global_opts( # 用于设置全局图表选项
        title_opts=opts.TitleOpts(title="Pyecharts Scatter Plot"),  # 设置图表标题
        xaxis_opts=opts.AxisOpts(                                   # 配置x轴
            type_="value",                                          # 数值轴
            name="X-axis",                                          # 轴名称
            splitline_opts=opts.SplitLineOpts(is_show=True)         # 显示网格线
        ),
        yaxis_opts=opts.AxisOpts(                                   # 配置y轴
            type_="value",                                          # 数值轴
            name="Y-axis",                                          # 轴名称
            splitline_opts=opts.SplitLineOpts(is_show=True)         # 显示网格线
        ),
    )
)

# Render to HTML file or display
# scatter.render_notebook()  # If in Jupyter notebook
scatter.render("scatter_plot.html")


# In[16]:


# 生成随机生成样本
data = pd.DataFrame({
    'x': np.random.rand(50),
    'y': np.random.rand(50),
    'category': np.random.choice(['A', 'B', 'C', 'D'], 50)
})

# x='x', y='y'：指定X轴和Y轴使用的数据列
# title='Plotly Scatter Plot'：设置图表标题
# labels={'x': 'X-axis', 'y': 'Y-axis'}：自定义轴标签
# hover_data=['category']：当鼠标悬停在点上时显示类别信息
fig = px.scatter(data, x='x', y='y', 
                 title='Plotly Scatter Plot',
                 labels={'x': 'X-axis', 'y': 'Y-axis'},
                 hover_data=['category'])

# Update layout
fig.update_layout(
    plot_bgcolor='white',
    legend_title_text='Category'
)

# Show the plot
fig.show()


# ### 回归散点图
# 在散点图上添加回归线或曲线，直观显示变量间的相关关系和趋势

# In[3]:


df = sns.load_dataset('iris',data_home='seaborn',cache=True)  # 加载 iris 数据集
# 绘制散点图，默认线性拟合
sns.regplot(x=df["sepal_length"], y=df["sepal_width"])
plt.show()


# In[4]:


# 绘制散点图（默认进行线性拟合，设置不进行线性拟合，fit_reg=False）
sns.regplot(x=df["sepal_length"], y=df["sepal_width"], fit_reg=False)
plt.show()


# #### 其他包没有那么便捷实现

# ### 抖动散点图（Jittered Scatter Plots）

# In[5]:


# 创建样本数据集
categories = ['A', 'B', 'C']
x = np.random.choice(categories, size=100)  # 分类变量
y = np.random.normal(0, 1, size=100)        # 连续变量

# 创建DataFrame
df = pd.DataFrame({
    'category': x,
    'value': y
})

# 绘制对比图
plt.figure(figsize=(12, 6))

# 无抖动的点图
plt.subplot(1, 2, 1)
sns.stripplot(x='category', y='value', data=df, jitter=False, alpha=0.6)
plt.title('Seaborn Strip Plot (No Jitter)')
plt.xlabel('Category')
plt.ylabel('Value')

# 有抖动的点图
plt.subplot(1, 2, 2)
sns.stripplot(x='category', y='value', data=df, jitter=True, alpha=0.6)
plt.title('Seaborn Strip Plot (With Jitter)')
plt.xlabel('Category')
plt.ylabel('Value')

plt.tight_layout()
plt.show()


# In[8]:


# 使用plotly绘制
fig = px.strip(df, 
               x='category', 
               y='value', 
               title='Jitter Plot with Plotly Express')
fig.show()


# ## 六边形图

# In[39]:


# 使用matplotlib绘制
# 生成随机数据
x = np.random.randn(10000)
y = np.random.randn(10000)

plt.figure(figsize=(8, 6))

# 绘制六边形图，gridsize控制六边形的大小
plt.hexbin(x, y, gridsize=30, cmap='Blues')

# 添加颜色条
plt.colorbar(label='Density')

plt.title("Hexbin Plot")
plt.xlabel("X")
plt.ylabel("Y")
plt.show()


# In[41]:


# 使用seaborn绘制
data = pd.DataFrame({
    'x': np.random.rand(50),
    'y': np.random.rand(50)
})

# 绘制六边形图
sns.jointplot(data=data, x="x", y="y", kind="hex", cmap="Blues")

plt.show()


# pyecharts 没有内建的六边形图功能

# In[37]:


# 使用plotly绘制（矩形网格，而不是六边形网格）
# 生成随机数据
x = np.random.randn(10000)
y = np.random.randn(10000)

# 使用plotly.express绘制六边形图
# density_heatmap() 用于绘制二维密度热图（实际上是通过对数据进行分箱并绘制矩形网格来展示数据的密度分布）。
# x=x, y=y 指定了 x 和 y 的数据，用于绘制热图。
# nbinsx=30 和 nbinsy=30 表示将数据分别分为 30 个箱子（或网格）沿 X 和 Y 轴分布。这决定了矩形网格的数量，实际上每个箱子是矩形而不是六边形。
# color_continuous_scale='Blues' 设置了颜色映射的范围，这里使用了蓝色渐变（Blues）。数据密度越高的区域会被赋予更深的颜色，密度较低的区域会呈现较浅的颜色。
fig = px.density_heatmap(x=x, y=y, nbinsx=30, nbinsy=30, color_continuous_scale='Blues')

# 设置标题和标签
fig.update_layout(
    title="六边形图 (Hexbin Plot)",
    xaxis_title="X轴",
    yaxis_title="Y轴"
)

# 显示图形
fig.show()


# ## 气泡图
# 可视化三个变量之间关系的图表类型

# In[22]:


# 使用matplotlib绘制
# 随机生成样本
x = [10, 20, 30, 40, 50]  # 横坐标
y = [15, 25, 35, 20, 10]  # 纵坐标
sizes = [100, 300, 500, 200, 400]  # 气泡大小（可以理解为“权重”或“值”）

# 创建气泡图
plt.figure(figsize=(8, 6))
# x、y：气泡的位置。
# s：每个点的大小（可以是列表或数组）。
# alpha：透明度（0~1，适合让气泡“重叠”时不太遮挡）。
# c：颜色，可以是单个颜色，也可以是颜色列表。
# edgecolors：气泡边缘的颜色。
plt.scatter(x, y, s=sizes, alpha=0.5, c='skyblue', edgecolors='black')

# 添加标签
plt.xlabel("X-axis")
plt.ylabel("Y-axis")
plt.title("Matplotlib Bubble Chart")
plt.grid(True)
plt.show()


# In[24]:


# 增加颜色映射

colors = [1, 2, 3, 4, 5]  # 颜色映射依据变量（比如代表某个数值属性）

plt.figure(figsize=(8, 6))

# 创建气泡图，使用 colormap（c与cmap）
# c=colors：使用 colors 列表指定每个气泡的“颜色权重”。
# cmap='viridis'：指定颜色映射表（常用的还有 'plasma'、'coolwarm'、'inferno' 等）。
# plt.colorbar()：添加右侧颜色图例（legend）
scatter = plt.scatter(x, y, s=sizes, c=colors, cmap='viridis', alpha=0.6, edgecolors='black')

# 添加 colorbar
cbar = plt.colorbar(scatter)
cbar.set_label('Color Map')

# 图形美化
plt.xlabel("X-axis")
plt.ylabel("Y-axis")
plt.title("Matplotlib Bubble Chart")
plt.grid(True)
plt.show()


# In[17]:


# 使用seaborn绘制

# 生成随机样本
data = pd.DataFrame({
    'x': np.random.rand(50),
    'y': np.random.rand(50),
    'size': np.random.randint(10, 100, 50),
    'category': np.random.choice(['A', 'B', 'C', 'D'], 50)
})

plt.figure(figsize=(8, 6))

# 传递x值和y值
# x='x', y='y' - 设置x轴和y轴所对应的数据列名
# size='size' - 指定用于控制点大小的数据列
# hue='category' - 指定用于控制点颜色的数据列
# palette='viridis' - 设置颜色主题
sns.scatterplot(data=data, x='x', y='y', size='size', hue='category', palette='viridis')

# 增加x轴和y轴标签，添加标题
plt.xlabel('X-axis')
plt.ylabel('Y-axis')
plt.title('Seaborn Scatter Plot')

plt.show()


# In[26]:


# 使用pyechart绘制
np.random.seed(42)
data = pd.DataFrame({
    'x': np.random.rand(50),
    'y': np.random.rand(50),
    'size': np.random.randint(10, 100, 50),
    'category': np.random.choice(['A', 'B', 'C', 'D'], 50)
})

# 绘制气泡图
scatter = (
    Scatter()
    .add_xaxis(data['x'].tolist())
    .add_yaxis(
        series_name="气泡图",
        y_axis=data.apply(lambda row: [row['y'], row['size'], row['category']], axis=1).tolist(),  # 格式: [y, size, category]
        symbol_size=1,  # 基础大小（会被visualmap覆盖）
    )
    .set_global_opts(
        title_opts=opts.TitleOpts(title="PyEcharts 2.x 气泡图"),
        xaxis_opts=opts.AxisOpts(name="X轴"),
        yaxis_opts=opts.AxisOpts(name="Y轴"),
        # 视觉映射：控制大小
        visualmap_opts=[
            opts.VisualMapOpts(
                type_="size",  # 映射类型为大小
                min_=10,       # 最小值
                max_=100,      # 最大值
                dimension=2,   # 使用数据第三列（size）控制大小
                range_size=[10, 50],  # 实际显示的像素范围
                pos_right="10%"
            ),
        ]
    )
)

scatter.render("bubble_v2_fixed.html")


# In[18]:


# 使用plotly绘制

# 生成随机生成样本
data = pd.DataFrame({
    'x': np.random.rand(50),
    'y': np.random.rand(50),
    'size': np.random.randint(10, 100, 50),
    'category': np.random.choice(['A', 'B', 'C', 'D'], 50)
})

# x='x', y='y'：指定X轴和Y轴使用的数据列
# size='size'：使用'size'列的值来调整点的大小
# color='category'：根据'category'列的值为点着色
# title='Plotly Scatter Plot'：设置图表标题
# labels={'x': 'X-axis', 'y': 'Y-axis'}：自定义轴标签
# hover_data=['category']：当鼠标悬停在点上时显示类别信息
fig = px.scatter(data, x='x', y='y', 
                 size='size', 
                 color='category',
                 title='Plotly Scatter Plot',
                 labels={'x': 'X-axis', 'y': 'Y-axis'},
                 hover_data=['category'])

# Update layout
fig.update_layout(
    plot_bgcolor='white',
    legend_title_text='Category'
)

# Show the plot
fig.show()


# In[ ]:


# 随堂练习：读取文件，绘制气泡图


# ## 等高线图

# In[26]:


# 使用matplotlib绘制

# 生成数据
# 在 x 和 y 方向上生成均匀分布的 100 个点，范围从 -2 到 2。
x = np.linspace(-2, 2, 100)
y = np.linspace(-2, 2, 100)
# np.meshgrid 将 x 和 y 的一维数组转换为二维网格坐标矩阵，X 和 Y 分别表示网格中每个点的横纵坐标。
X, Y = np.meshgrid(x, y)
# 根据网格坐标计算每个点的函数值 Z = sin(x) * cos(y)，这个就是等高线图的“高度”。
Z = np.sin(X) * np.cos(Y)

# 将函数值从 -1 到 1 均分为 20 个等级（即 20 条等高线）；定义等高线水平
levels = np.linspace(-1, 1, 20)  

# 使用 plt.contour() 画出等高线图，其中 X 和 Y 是坐标网格，Z 是函数值，levels 控制绘制哪些高度的等高线（指定等级）。
plt.contour(X, Y, Z, levels=levels) 

# 添加标签和标题
plt.xlabel('X-axis')
plt.ylabel('Y-axis')
plt.title('Contour Plot with Specific Levels')

plt.show()


# In[30]:


import plotly.graph_objects as go
import numpy as np

# 生成数据
x = np.linspace(-2, 2, 100)
y = np.linspace(-2, 2, 100)
X, Y = np.meshgrid(x, y)
Z = np.sin(X) * np.cos(Y)

# 创建 Plotly contour plot
fig = go.Figure(
    data=go.Contour(
        z=Z, # 等高线依据的函数值（高度）
        x=x, # 坐标轴的网格范围
        y=y, # 坐标轴的网格范围
        colorscale='Viridis', # 设置色带为 Viridis（从深蓝到亮黄）
        contours=dict(
            start=-1, # 最小等高值
            end=1, # 最大等高值
            size=0.1, # 每两个等高线之间的间隔
            coloring='lines'  # 只画线；可改成 'fill'（填充区域）或 'heatmap'（类似热力图）。
        )
    )
)
fig.update_layout(
    title="Plotly 等高线图",
    xaxis_title="X 轴",
    yaxis_title="Y 轴"
)
fig.show()


# Pyecharts 没有直接的 contour plot 接口
# Seaborn 并不直接支持任意函数绘制等高线，更适合用在密度估计场景。

# ## 边际图

# In[28]:


df = sns.load_dataset('iris')  # 从Seaborn中加载iris数据集

# 创建带有散点图的边际图
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='scatter')
# 创建带有六边形图的边际图
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='hex')
# 创建带有核密度估计图的边际图
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='kde')
plt.show()


# In[34]:


# 自定义联合图中的散点图
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"],
              kind='scatter', s=200, color='m',
              edgecolor="skyblue", linewidth=2)

# 自定义颜色
sns.set_theme(style="white", color_codes=True)
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='kde',
              color="skyblue")
plt.show()


# In[35]:


# 自定义直方图
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='hex',
              marginal_kws=dict(bins=30, fill=True))
plt.show()

# 无间隔
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='kde',
              color="blue", space=0)
# 大间隔
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"], kind='kde',
              color="blue", space=3)
# 调整边际图比例
sns.jointplot(x=df["sepal_length"], y=df["sepal_width"],
              kind='kde', ratio=2)
plt.show()


# --- End of 8_Numerical_Data.py ---

# --- Start of data_visualization_demo.py ---
#!/usr/bin/env python
# coding: utf-8

# # 可视化举例
# ## 《数据可视化》
# ## 2024-2025-2 春季学期

# ### 1. matplotlib

# In[3]:


import matplotlib.pyplot as plt

# 数据
x = [1, 2, 3, 4, 5]
y = [1, 4, 9, 16, 25]

# 创建图形
plt.plot(x, y)

# 添加标题和标签
plt.title("Matplotlib Example")
plt.xlabel("X Axis")
plt.ylabel("Y Axis")

# 显示图形
plt.show()


# ### 2. Seaborn

# In[4]:


import seaborn as sns
import matplotlib.pyplot as plt

# 数据
tips = sns.load_dataset("tips")

# 创建散点图
sns.scatterplot(data=tips, x="total_bill", y="tip", hue="time")

# 添加标题
plt.title("Seaborn Example")

# 显示图形
plt.show()


# ### 3. plotly

# In[1]:


import plotly.graph_objects as go

# 数据
categories = ['A', 'B', 'C', 'D', 'E']
sales = [150, 200, 120, 180, 90]

# 创建柱状图
fig = go.Figure(data=[go.Bar(x=categories, y=sales)])

# 添加标题和标签
fig.update_layout(
    title="Sales by Category",
    xaxis_title="Category",
    yaxis_title="Sales",
    template="plotly_dark"  # 更换主题
)

# 显示图形
fig.show()


# ### 4. pyecharts

# In[3]:


from pyecharts import options as opts
from pyecharts.charts import Bar

# 数据
bar = Bar()
bar.add_xaxis(["A", "B", "C", "D", "E"])
bar.add_yaxis("Series 1", [5, 20, 36, 10, 75])

# 设置全局配置
bar.set_global_opts(title_opts=opts.TitleOpts(title="Pyecharts Example"))

# 在Jupyter中直接显示图表
bar.render_notebook()


# ### 5. dash

# In[2]:


import dash
from dash import dcc, html
import plotly.express as px

# 创建Dash应用
app = dash.Dash()

# 数据
df = px.data.gapminder()

# 创建图表
fig = px.scatter(df, x="gdpPercap", y="lifeExp", color="continent", size="pop", hover_name="country", log_x=True, size_max=60)

# 布局
app.layout = html.Div([
    html.H1("Dash Example"),
    dcc.Graph(figure=fig)
])

# 运行应用
if __name__ == "__main__":
    app.run_server(debug=True)


# In[ ]:





# --- End of data_visualization_demo.py ---

