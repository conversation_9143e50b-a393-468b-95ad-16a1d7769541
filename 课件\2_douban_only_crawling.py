#!/usr/bin/env python
# coding: utf-8

# In[2]:


import requests
from bs4 import BeautifulSoup
import time

top250_url = "https://movie.douban.com/top250?start={}&filter="
headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.99 Safari/537.36'}

page = 0

for i in range(10):
    start = i*25
    page += 1
    print('抓取第'+str(page)+'页的内容...')
    url_visit = top250_url.format(start)
    req = requests.get(url_visit,headers=headers)  # 注意加入headers
    req.encoding = req.apparent_encoding
    content = req.text
    time.sleep(2)

    # 解析
    soup = BeautifulSoup(content, 'html.parser')
    all_item_divs = soup.find_all('div',class_='item')
    
    # 之前给的代码
    # for each_item in all_item_divs:
    #     pic_div = each_item.find(class_='pic')
    #     rank = pic_div.find('em').string  # 排名
    #     title = pic_div.find('img')['alt']  #电影名称
    #     url = pic_div.find('a')['href']      #电影链接
    # 
    #     star_div = each_item.find(class_='star')
    #     avg_rating = star_div.find('span',class_='rating_num').string  # 平均得分
    #     rating_num = star_div.find_all('span')[3].get_text()[:-3]  # 评分人数
    # 
    #     print(rank,title,url,avg_rating,rating_num)
    
    # 新代码
    for each_item in all_item_divs:
        pic_div = each_item.find(class_='pic')
        rank = pic_div.find('em').string  # 排名
        title = pic_div.find('img')['alt']  #电影名称
        url = pic_div.find('a')['href']      #电影链接
    
        star_div = each_item.find(class_='bd')
        attributes_div = star_div.find('div')
        avg_rating = attributes_div.find('span',class_='rating_num').string  # 平均得分
        rating_num = star_div.find_all('span')[3].get_text()[:-3]  # 评分人数
    
        print(rank,title,url,avg_rating,rating_num)


# In[ ]:




