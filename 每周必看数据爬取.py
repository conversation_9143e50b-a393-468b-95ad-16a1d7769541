import requests
import pandas as pd
import time
from tqdm import tqdm

# 1. 配置请求头
COOKIE = "_uuid=C7EAEC4C-1C410-B105A-1109C-1B243B1A4A6206748infoc; buvid_fp=40028e29b81b14ac49efa06d07c665dd; buvid3=FFEC89FF-63B7-1FCD-91CC-FD834CF0438206572infoc; b_nut=1750321907; buvid4=DFA3049C-E5E1-098E-9F59-37F1CDB06F1A06572-025061916-5MLPJieg5mRnK3tL9En1Mg%3D%3D; bsource=search_google; header_theme_version=CLOSE; enable_web_push=DISABLE; enable_feed_channel=ENABLE; SESSDATA=843b1077%2C1765875110%2C5db69%2A61CjCXWSw2yGJZovArLWBLZQTqF3B-GzT4VJ9lxvYTqcrb7DY_kkLPIupu64WhVLLXf8ASVldDRDFTblJJUXZ4UzJvNGt1VXZqSDd4WC1qZHRTdlA4MG9ZTTN4RllUSDkwOXFHNEgyLWc4dE52X05uaDFjWlB4UEVSMXl1aVMybzltRHZNQ1dsWUdnIIEC; bili_jct=1a7abcb959b1f8bf8e273ca9d5de0204; DedeUserID=2056235164; DedeUserID__ckMd5=10a7dac6e5d3cd30; sid=89kz4pb3; CURRENT_FNVAL=4048; rpdid=|(um~luR)ukR0J'u~l|JkR~lk; bili_ticket=eyJhbGciOiJIUzI1NiIsImtpZCI6InMwMyIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTA1ODM5NTcsImlhdCI6MTc1MDMyNDY5NywicGx0IjotMX0.iLCjY4mWThfCtuCulZSGhpXV9J0hVz2X4npRnKxAiag; bili_ticket_expires=1750583897; b_lsid=FB9EE3FB_1978B87C21D; timeMachine=0; bp_t_offset_2056235164=1080426068717862912; home_feed_column=4; browser_resolution=683-695"

if '在这里粘贴你复制的COOKIE字符串' in COOKIE:
    print("错误：请在代码中第7行的COOKIE变量处，填入你自己的B站Cookie！")
    exit()

HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
    'Cookie': COOKIE,
    'Referer': 'https://www.bilibili.com/v/popular/weekly'  # 增加Referer，模拟从每周必看页面发出的请求
}


# 2. 定义数据采集函数
def get_weekly_must_see(issue_number):
    """
    获取指定期数的B站每周必看视频列表。
    :param issue_number: 期数，例如 303
    :return: 包含该期所有视频信息的列表，或在失败时返回None
    """
    url = f"https://api.bilibili.com/x/web-interface/popular/series/one?number={issue_number}"
    try:
        response = requests.get(url, headers=HEADERS, timeout=15)
        response.raise_for_status()  # 如果请求出错（如 404, 500），则抛出异常
        data = response.json()

        if data['code'] == 0:
            videos_list = []
            issue_desc = data['data']['config']['name']  # 例如 "第303期"

            for item in data['data']['list']:
                stat = item['stat']  # 视频数据统计对象
                owner = item['owner']  # UP主信息对象

                # 构造视频链接
                video_link = f"https://www.bilibili.com/video/{item['bvid']}"

                # 将视频标签列表（tag）拼接成一个字符串
                tags = [tag['tag_name'] for tag in item.get('rcmd_reason_style_v2', {}).get('tags', [])]
                tags_str = ','.join(tags)

                video_info = {
                    '期数': issue_number,
                    '期数描述': issue_desc,
                    '标题': item['title'],
                    '视频标签': tags_str,
                    '视频链接': video_link,
                    'up主': owner['name'],
                    'up主_id': owner['mid'],
                    'aid': item['aid'],
                    'bvid': item['bvid'],
                    '投币数': stat['coin'],
                    '弹幕数': stat['danmaku'],
                    '收藏数': stat['favorite'],
                    '点赞数': stat['like'],
                    '评论数': stat['reply'],
                    '分享数': stat['share'],
                    '播放数': stat['view'],
                    '发布时间': item['pubdate'],  # 这是视频的原始发布时间戳
                    '所属分区': item['tname'],
                }
                videos_list.append(video_info)
            return videos_list
        else:
            print(f"\n获取第 {issue_number} 期数据时API返回错误: {data.get('message', '未知错误')}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"\n请求第 {issue_number} 期数据失败: {e}")
        return None


def get_uploader_fans(mid_list):
    """
    根据UP主的mid列表，批量获取UP主的粉丝数。
    :param mid_list: UP主mid的列表
    :return: 包含UP主粉丝数的字典，键为mid
    """
    fans_dict = {}
    print("\n开始批量获取UP主粉丝量...")
    url = "https://api.bilibili.com/x/web-interface/card"

    for mid in tqdm(mid_list, desc="获取UP主粉丝量进度"):
        if mid in fans_dict:
            continue

        params = {'mid': mid}
        try:
            response = requests.get(url, headers=HEADERS, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data['code'] == 0:
                fans_dict[mid] = data['data']['card'].get('fans', 0)
            else:
                fans_dict[mid] = 0  # 查询失败则记为0
        except requests.exceptions.RequestException:
            fans_dict[mid] = 0  # 网络异常也记为0

        time.sleep(0.5)  # 轻微延时，避免请求过快

    print("UP主粉丝量获取完成。")
    return fans_dict


# 3. 主程序执行流程
if __name__ == '__main__':
    # --- 参数配置 ---
    START_ISSUE = 303  # 开始期数
    END_ISSUE = 325  # 结束期数
    OUTPUT_CSV_FILE = 'bilibili_weekly_must_see_303_to_325.csv'

    # --- 数据采集 ---
    all_videos_data = []
    # 使用tqdm创建总进度条
    for issue in tqdm(range(START_ISSUE, END_ISSUE + 1), desc="采集每周必看总进度"):
        tqdm.write(f"正在采集第 {issue} 期...")  # 在进度条上方打印信息
        weekly_videos = get_weekly_must_see(issue)

        if weekly_videos:
            all_videos_data.extend(weekly_videos)
        else:
            tqdm.write(f"❌ 第 {issue} 期采集失败，跳过。")

        time.sleep(2)  # 友好请求，采集完一期后休息2秒

    if not all_videos_data:
        print("\n 最终采集结果为空，未能获取任何视频数据。请检查Cookie是否有效或网络连接。")
        exit()

    # 将列表转换为DataFrame
    df = pd.DataFrame(all_videos_data)
    print(f"\n 所有期数采集完毕，共获得 {len(df)} 条视频数据。")

    # --- 获取并合并UP主粉丝量 ---
    unique_mids = df['up主_id'].unique()
    fans_data = get_uploader_fans(unique_mids)

    # 将粉丝量数据映射到DataFrame中
    df['up主粉丝量'] = df['up主_id'].map(fans_data)

    # --- 数据格式化与保存 ---
    # 将Unix时间戳转换为标准日期格式 YYYY-MM-DD HH:MM:SS
    df['发布时间'] = pd.to_datetime(df['发布时间'], unit='s')

    # 调整列顺序以符合您的要求
    final_columns = [
        '期数', '期数描述', '标题', '视频标签', '视频链接', 'up主', 'up主_id', 'up主粉丝量',
        'aid', 'bvid', '投币数', '弹幕数', '收藏数', '点赞数', '评论数', '分享数',
        '播放数', '发布时间', '所属分区'
    ]
    df_final = df[final_columns]

    # 保存到CSV文件
    df_final.to_csv(OUTPUT_CSV_FILE, index=False, encoding='utf-8-sig')

    print(f"\n 全部任务完成！数据已清洗、整合并保存至文件: {OUTPUT_CSV_FILE}")